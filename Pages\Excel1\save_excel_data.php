<?php
// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the table data from the POST request
    $tableData = isset($_POST['tableData']) ? $_POST['tableData'] : null;
    $tableMetadata = isset($_POST['tableMetadata']) ? $_POST['tableMetadata'] : null;
    
    if ($tableData) {
        // Define the directory to save data
        $dataDir = __DIR__ . '/data';
        
        // Create the directory if it doesn't exist
        if (!file_exists($dataDir)) {
            mkdir($dataDir, 0755, true);
        }
        
        // Define the file paths
        $dataFilePath = $dataDir . '/excel_data.json';
        $metadataFilePath = $dataDir . '/excel_metadata.json';
        
        // Save the data to the files
        $dataResult = file_put_contents($dataFilePath, $tableData);
        $metadataResult = true;
        
        if ($tableMetadata) {
            $metadataResult = file_put_contents($metadataFilePath, $tableMetadata);
        }
        
        if ($dataResult !== false && $metadataResult !== false) {
            // Return success response
            echo json_encode(['success' => true, 'message' => 'تم حفظ البيانات بنجاح']);
        } else {
            // Return error response
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'فشل في حفظ البيانات']);
        }
    } else {
        // Return error response if tableData is not provided
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'لم يتم توفير بيانات الجدول']);
    }
} else {
    // Return error response if request method is not POST
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
}
?> 