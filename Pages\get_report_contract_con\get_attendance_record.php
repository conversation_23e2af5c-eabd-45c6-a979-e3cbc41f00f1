<?php
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    if (!isset($_GET['id'])) {
        throw new Exception("No attendance record ID provided");
    }

    $id = intval($_GET['id']);

    $query = "
        SELECT pd.*, 
               c.name_Job,
               c.contract_type,
               c.wage_contract,
               e.name_ar_contract,
               p.Project_name
        FROM permanent_diapers pd
        JOIN contract c ON pd.id_contract = c.id_contract
        JOIN employees e ON c.id_employees = e.id_employees
        JOIN Project p ON c.id_Project = p.id_Project
        WHERE pd.id_permanent_diapers = ?
    ";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Query preparation failed: " . $conn->error);
    }

    $stmt->bind_param("i", $id);
    
    if (!$stmt->execute()) {
        throw new Exception("Query execution failed: " . $stmt->error);
    }

    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        throw new Exception("No attendance record found with ID: " . $id);
    }

    $record = $result->fetch_assoc();
    
    // Decode the JSON data
    $record['data'] = json_decode($record['data'], true);

    echo json_encode([
        'success' => true,
        'data' => $record
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 