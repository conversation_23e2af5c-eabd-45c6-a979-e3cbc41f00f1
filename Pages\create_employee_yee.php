<?php
// بداية جلسة المستخدم وإعدادات عرض الأخطاء
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// معالجة طلب AJAX للحصول على مهام الوظيفة
if (isset($_GET['action']) && $_GET['action'] === 'get_job_tasks') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid job title ID');
        }

        $jobTitleId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // الحصول على بيانات المسمى الوظيفي
        $stmt = $conn->prepare("SELECT data_todo_list_Job FROM Job_titles WHERE id_Job_titles = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        $stmt->bind_param("i", $jobTitleId);
        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $todoList = json_decode($row['data_todo_list_Job'], true);
            $tasks = array_map(function($task) {
                return $task['taskName'];
            }, $todoList['jobDetails']['tasks']);
            echo json_encode($tasks);
        } else {
            echo json_encode([]);
        }

        $stmt->close();
        $conn->close();
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

// معالجة طلب AJAX للحصول على معلومات الموظف
if (isset($_GET['action']) && $_GET['action'] === 'get_employee_data') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid employee ID');
        }

        $employeeId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // الحصول على بيانات الموظف
        $stmt = $conn->prepare("SELECT * FROM employees WHERE id_employees = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        $stmt->bind_param("i", $employeeId);
        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            // تنظيف البيانات الثنائية من النتيجة
            $binaryFields = ['Identity_document', 'test_results_document', 'Interview_results_document', 
                             'guarantee_document', 'medical_examination_document', 'cv_document'];
            
            foreach ($binaryFields as $field) {
                if (isset($row[$field]) && $row[$field] !== null) {
                    $row[$field] = true; // إعادة قيمة منطقية بدلاً من البيانات الثنائية
                } else {
                    $row[$field] = false;
                }
            }
            
            // إعادة تسمية الحقل id_employees إلى id للاتساق مع واجهة المستخدم
            $row['id'] = $row['id_employees'];
            
            echo json_encode($row);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Employee not found']);
        }

        $stmt->close();
        $conn->close();
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

$error_message = '';
$success_message = '';

// جلب المشاريع والمسميات الوظيفية من قاعدة البيانات
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    // جلب المشاريع
    $projects = [];
    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }

    // جلب المسميات الوظيفية مع عدد المهام
    $job_titles = [];
    $result = $conn->query("SELECT id_Job_titles, name_Job, data_todo_list_Job FROM Job_titles");

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $job_titles[] = [
                'id' => $row['id_Job_titles'],
                'name' => $row['name_Job'],
                'data' => $row['data_todo_list_Job'] // حفظ JSON كما هو
            ];
        }
    }
    
    // جلب قائمة الموظفين
    $employees = [];
    $result = $conn->query("SELECT id_employees as id, name_ar_contract, name_en_contract, Identity_number_contract FROM employees ORDER BY name_ar_contract");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $employees[] = $row;
        }
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $validation_errors = [];
        
        // معرف الموظف للتحديث، إذا كان متوفراً
        $employee_id = isset($_POST['employee_id']) ? intval($_POST['employee_id']) : 0;
        $is_update = $employee_id > 0;
        
        // Get form data
        $name_ar_contract = trim($_POST['name_ar_contract'] ?? '');
        $name_en_contract = trim($_POST['name_en_contract'] ?? '');
        $address = trim($_POST['address'] ?? '');
        $phone_number = trim($_POST['phone_number'] ?? '');
        // Convert empty phone number to NULL for database compatibility
        $phone_number = !empty($phone_number) ? $phone_number : null;
        $Identity_contract_ar = trim($_POST['Identity_contract_ar'] ?? '');
        $Identity_contract_en = trim($_POST['Identity_contract_en'] ?? '');
        $Identity_number_contract = trim($_POST['Identity_number_contract'] ?? '');
        $Identity_issue_contract_ar = trim($_POST['Identity_issue_contract_ar'] ?? '');
        $Identity_issue_contract_en = trim($_POST['Identity_issue_contract_en'] ?? '');
        $Identity_issue_date_contract = trim($_POST['Identity_issue_date_contract'] ?? '');
        $polarization_method = trim($_POST['polarization_method'] ?? '');
        
        // Check for duplicates based on name - تجاهل التحقق للموظف نفسه في حالة التحديث
        $check_name_sql = "SELECT * FROM employees WHERE (name_ar_contract = ? OR name_en_contract = ?)";
        if ($is_update) {
            $check_name_sql .= " AND id_employees != ?";
        }
        $check_name_stmt = $conn->prepare($check_name_sql);
        
        if ($is_update) {
            $check_name_stmt->bind_param("ssi", $name_ar_contract, $name_en_contract, $employee_id);
        } else {
            $check_name_stmt->bind_param("ss", $name_ar_contract, $name_en_contract);
        }
        
        $check_name_stmt->execute();
        $name_result = $check_name_stmt->get_result();
        
        if ($name_result->num_rows > 0) {
            $validation_errors[] = 'يوجد موظف مسجل بنفس الاسم بالفعل';
        }
        $check_name_stmt->close();
        
        // Check for duplicates based on ID number if no name duplicates found
        if (empty($validation_errors)) {
            // Different validation depending on ID type
            if ($Identity_contract_en == 'National ID') {
                $check_id_sql = "SELECT * FROM employees WHERE Identity_number_contract = ? AND Identity_contract_en = 'National ID'";
            } else if ($Identity_contract_en == 'Passport') {
                $check_id_sql = "SELECT * FROM employees WHERE Identity_number_contract = ? AND Identity_contract_en = 'Passport'";
            } else {
                $check_id_sql = "SELECT * FROM employees WHERE Identity_number_contract = ?";
            }
            
            // استثناء الموظف نفسه عند التحديث
            if ($is_update) {
                $check_id_sql .= " AND id_employees != ?";
            }
            
            $check_id_stmt = $conn->prepare($check_id_sql);
            
            if ($is_update) {
                $check_id_stmt->bind_param("si", $Identity_number_contract, $employee_id);
            } else {
                $check_id_stmt->bind_param("s", $Identity_number_contract);
            }
            
            $check_id_stmt->execute();
            $id_result = $check_id_stmt->get_result();
            
            if ($id_result->num_rows > 0) {
                if ($Identity_contract_en == 'National ID') {
                    $validation_errors[] = 'يوجد موظف مسجل بنفس رقم البطاقة الشخصية';
                } else if ($Identity_contract_en == 'Passport') {
                    $validation_errors[] = 'يوجد موظف مسجل بنفس رقم جواز السفر';
                } else {
                    $validation_errors[] = 'يوجد موظف مسجل بنفس رقم الهوية';
                }
            }
            $check_id_stmt->close();
        }
        
        // Process file uploads
        $files_to_process = [
            'Identity_document' => 'هوية الموظف',
            'test_results_document' => 'نتائج الاختبار',
            'Interview_results_document' => 'نتائج المقابلة',
            'guarantee_document' => 'وثيقة الضمان',
            'medical_examination_document' => 'وثيقة الفحص الطبي',
            'cv_document' => 'السيرة الذاتية'
        ];
        
        $file_data = [];
        foreach ($files_to_process as $field => $label) {
            if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
                $file_data[$field] = file_get_contents($_FILES[$field]['tmp_name']);
            }
        }
        
        // Validate required fields
        if (empty($name_ar_contract)) {
            $validation_errors[] = 'الاسم العربي مطلوب';
        }

        if (empty($name_en_contract)) {
            $validation_errors[] = 'الاسم الإنجليزي مطلوب';
        }

        if (empty($Identity_contract_ar) || empty($Identity_contract_en)) {
            $validation_errors[] = 'نوع الهوية مطلوب';
        }

        if (empty($Identity_number_contract)) {
            $validation_errors[] = 'رقم الهوية مطلوب';
        }

        if (empty($Identity_issue_contract_ar) || empty($Identity_issue_contract_en)) {
            $validation_errors[] = 'مكان إصدار الهوية مطلوب';
        }

        if (empty($Identity_issue_date_contract)) {
            $validation_errors[] = 'تاريخ إصدار الهوية مطلوب';
        }

        // Note: All other fields are optional (removed validation checks)
        
        // If validation passes, insert or update in database
        if (empty($validation_errors)) {
            try {
                // Read database connection details
                $file = fopen(__DIR__ . "/connection/one.txt", "r");
                if (!$file) {
                    throw new Exception('خطأ في قراءة ملف الإعدادات');
                }

                $servername = trim(fgets($file));
                $username = trim(fgets($file));
                $password = trim(fgets($file));
                $dbname = trim(fgets($file));
                fclose($file);

                $conn = new mysqli($servername, $username, $password, $dbname);
                $conn->set_charset("utf8");

                if ($conn->connect_error) {
                    throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
                }

                if ($is_update) {
                    // في حالة التحديث، نحتاج إلى إنشاء استعلام SQL ديناميكي
                    $updateFields = [
                        'name_ar_contract = ?',
                        'name_en_contract = ?',
                        'address = ?',
                        'phone_number = ?',
                        'Identity_contract_ar = ?',
                        'Identity_contract_en = ?',
                        'Identity_number_contract = ?',
                        'Identity_issue_contract_ar = ?',
                        'Identity_issue_contract_en = ?',
                        'Identity_issue_date_contract = ?',
                        'polarization_method = ?'
                    ];
                    
                    $params = [
                        $name_ar_contract,
                        $name_en_contract,
                        $address,
                        $phone_number,
                        $Identity_contract_ar,
                        $Identity_contract_en,
                        $Identity_number_contract,
                        $Identity_issue_contract_ar,
                        $Identity_issue_contract_en,
                        $Identity_issue_date_contract,
                        $polarization_method
                    ];
                    
                    // Adjust the type for phone_number to handle NULL values
                    $types = "sss";
                    $types .= $phone_number === null ? "i" : "s"; // i placeholder will be filled with NULL
                    $types .= "sssssss";
                    
                    // إضافة حقول الملفات فقط إذا تم تحميلها
                    foreach ($files_to_process as $field => $label) {
                        if (isset($file_data[$field])) {
                            $updateFields[] = "$field = ?";
                            $params[] = $file_data[$field];
                            $types .= "b"; // binary data
                        }
                    }
                    
                    // إضافة معرف الموظف للشرط
                    $params[] = $employee_id;
                    $types .= "i"; // integer
                    
                    $sql = "UPDATE employees SET " . implode(', ', $updateFields) . " WHERE id_employees = ?";
                    
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                    }
                    
                    $stmt->bind_param($types, ...$params);
                    
                    if (!$stmt->execute()) {
                        throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
                    }
                    
                    $success_message = 'تم تحديث بيانات الموظف بنجاح';
                } else {
                    // في حالة الإضافة، استخدم كود الإضافة الحالي
                    $sql = "INSERT INTO employees (
                        name_ar_contract,
                        name_en_contract,
                        address,
                        phone_number,
                        Identity_contract_ar,
                        Identity_contract_en,
                        Identity_number_contract,
                        Identity_issue_contract_ar,
                        Identity_issue_contract_en,
                        Identity_issue_date_contract,
                        Identity_document,
                        polarization_method,
                        test_results_document,
                        Interview_results_document,
                        guarantee_document,
                        medical_examination_document,
                        cv_document
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?, NULL, NULL, NULL, NULL, NULL)";
                
                    // Modify the SQL query to use NULL for binary fields if no files were uploaded
                    // This avoids the need to bind binary parameters
                    
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                    }

                    // Bind only the string parameters
                    $stmt->bind_param("sssssssssss",
                        $name_ar_contract,
                        $name_en_contract,
                        $address,
                        $phone_number,
                        $Identity_contract_ar,
                        $Identity_contract_en,
                        $Identity_number_contract,
                        $Identity_issue_contract_ar,
                        $Identity_issue_contract_en,
                        $Identity_issue_date_contract,
                        $polarization_method
                    );

                    if (!$stmt->execute()) {
                        throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
                    }
                    
                    // Get the ID of the newly inserted employee
                    $employee_id = $conn->insert_id;
                    
                    // Now handle file uploads separately with individual UPDATE statements
                    $file_fields = [
                        'Identity_document' => 'Identity_document',
                        'test_results_document' => 'test_results_document',
                        'Interview_results_document' => 'Interview_results_document',
                        'guarantee_document' => 'guarantee_document',
                        'medical_examination_document' => 'medical_examination_document',
                        'cv_document' => 'cv_document'
                    ];
                    
                    foreach ($file_fields as $form_field => $db_field) {
                        if (isset($file_data[$form_field]) && $file_data[$form_field] !== null) {
                            $update_sql = "UPDATE employees SET $db_field = ? WHERE id_employees = ?";
                            $update_stmt = $conn->prepare($update_sql);
                            if (!$update_stmt) {
                                throw new Exception("خطأ في إعداد استعلام تحديث الملف: " . $conn->error);
                            }
                            
                            $update_stmt->bind_param("bi", $file_data[$form_field], $employee_id);
                            if (!$update_stmt->execute()) {
                                throw new Exception("خطأ في تحديث الملف $form_field: " . $update_stmt->error);
                            }
                            $update_stmt->close();
                        }
                    }
                    
                    // Update polarization method separately
                    if (!empty($polarization_method)) {
                        $update_sql = "UPDATE employees SET polarization_method = ? WHERE id_employees = ?";
                        $update_stmt = $conn->prepare($update_sql);
                        if (!$update_stmt) {
                            throw new Exception("خطأ في إعداد استعلام تحديث طريقة الاستقطاب: " . $conn->error);
                        }
                        
                        $update_stmt->bind_param("si", $polarization_method, $employee_id);
                        if (!$update_stmt->execute()) {
                            throw new Exception("خطأ في تحديث طريقة الاستقطاب: " . $update_stmt->error);
                        }
                        $update_stmt->close();
                    }

                    $success_message = 'تم إضافة الموظف بنجاح';
                }
                
            } catch (Exception $e) {
                $error_message = "خطأ: " . $e->getMessage();
            } finally {
                if (isset($stmt)) {
                    $stmt->close();
                }
                if (isset($conn)) {
                    $conn->close();
                }
            }
        } else {
            $error_message = "أخطاء التحقق:<br>" . implode("<br>", $validation_errors);
        }
    }
} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء عقد جديد - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Alert styling */
        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .section-title {
            color: #0d6efd;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Form controls */
        .form-control, .form-select {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            color: var(--text-color);
        }

        .form-control:disabled {
            background-color: var(--hover-color);
            color: var(--text-muted);
        }

        /* Helper text styling */
        .text-muted {
            color: #6c757d !important;
        }

        [data-theme="dark"] .text-muted {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        /* Primary color overrides */
        .text-primary {
            color: #0d6efd !important;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }

        /* Save button styling */
        .save-contract-btn {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 200px;
        }

        .save-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .save-contract-btn:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Select2 styling */
        .select2-container--default .select2-selection--single {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
            height: 38px;
            line-height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            color: var(--text-color);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-dropdown {
            background-color: var(--bg-input);
            border-color: var(--input-border);
        }

        .select2-container--default .select2-results__option {
            color: var(--text-color);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color);
        }

        /* Task list styling */
        .task-list-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: var(--bg-input);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .task-list-scrollable {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .task-list-scrollable::-webkit-scrollbar {
            width: 8px;
        }

        .task-list-scrollable::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        .task-item {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-item:last-child {
            margin-bottom: 0;
        }

        .task-input-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex: 1;
        }

        .task-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        /* Delete button styling */
        .btn-danger.btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }

        .btn-danger.btn-sm:hover {
            opacity: 0.9;
        }

        .text-end {
            text-align: end;
            margin-top: -0.5rem; /* Adjust spacing */
        }

        /* Card styling */
        .card {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .card-title {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--primary-color);
        }

        /* Button styling */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--btn-text);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-danger {
            color: var(--btn-text);
        }

        /* Duration helper text */
        .duration-helper {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        /* JSON section styling */
        .btn-secondary {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: var(--hover-color);
            border-color: var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary .bi-chevron-down {
            transition: transform 0.3s ease;
        }

        .btn-secondary[aria-expanded="true"] .bi-chevron-down {
            transform: rotate(180deg);
        }

        #jsonSection {
            transition: all 0.3s ease;
        }

        #jsonSection.collapsing {
            transition: all 0.3s ease;
        }

        #jsonSection.show {
            margin-top: 1rem;
        }

        #json_output {
            background-color: var(--bg-input);
            color: var(--text-color);
            font-family: monospace;
            font-size: 0.875rem;
            resize: vertical;
        }

        /* Identity attachment styling */
        .identity-attachment-container {
            transition: all 0.3s ease;
        }

        .identity-attachment-container:hover {
            background-color: var(--bg-input) !important;
        }

        .border-dashed {
            border-style: dashed !important;
        }

        .upload-box {
            transition: all 0.3s ease;
        }

        .upload-box:hover {
            border-color: var(--primary-color) !important;
            background-color: rgba(var(--primary-rgb), 0.05);
        }

        /* Dark theme support */
        [data-theme="dark"] .identity-attachment-container {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        [data-theme="dark"] .upload-box:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .selected-file {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* Dark theme support for form controls */
        [data-theme="dark"] .form-control,
        [data-theme="dark"] .form-select,
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        /* Dark theme support for Select2 dropdown */
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection--single {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--dark-text-color, #e9ecef);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__option {
            background-color: var(--dark-input-bg, #2b3035);
            color: var(--dark-text-color, #e9ecef);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color, #0d6efd);
            color: #ffffff;
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__option[aria-selected="true"] {
            background-color: var(--primary-color, #0d6efd);
            color: #ffffff;
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        /* Dark theme support for custom inputs */
        [data-theme="dark"] #custom_issue_place,
        [data-theme="dark"] #custom_polarization_method {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        /* Dark theme support for input focus states */
        [data-theme="dark"] .form-control:focus,
        [data-theme="dark"] .form-select:focus,
        [data-theme="dark"] .select2-container--bootstrap-5.select2-container--focus .select2-selection,
        [data-theme="dark"] .select2-container--bootstrap-5.select2-container--open .select2-selection {
            border-color: var(--primary-color, #0d6efd);
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            background-color: var(--dark-input-bg, #2b3035);
        }

        /* Dark theme support for disabled states */
        [data-theme="dark"] .form-control:disabled,
        [data-theme="dark"] .form-control[readonly],
        [data-theme="dark"] .form-select:disabled {
            background-color: var(--dark-disabled-bg, #343a40);
            color: var(--dark-text-muted, #6c757d);
        }

        /* Dark theme support for placeholder text */
        [data-theme="dark"] .form-control::placeholder,
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection--single::placeholder {
            color: var(--dark-text-muted, #6c757d);
        }

        /* Custom file input styling */
        .custom-file-input-wrapper {
            position: relative;
            width: 100%;
        }

        .custom-file-input-wrapper input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        .custom-file-input-trigger {
            display: flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: var(--text-color);
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            cursor: pointer;
        }

        .custom-file-input-trigger i {
            margin-left: 0.5rem;
        }

        .custom-file-input-trigger .file-name {
            margin-right: auto;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Dark theme support for file input */
        [data-theme="dark"] .custom-file-input-trigger {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        [data-theme="dark"] .custom-file-input-trigger:hover {
            background-color: var(--dark-hover-bg, #343a40);
        }

        /* Invalid field styling */
        .is-invalid {
            border-color: #dc3545 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.875em;
            margin-top: 0.25rem;
        }

        /* Dark theme support for invalid fields */
        [data-theme="dark"] .is-invalid {
            border-color: #dc3545 !important;
            background-color: var(--dark-input-bg) !important;
        }

        [data-theme="dark"] .invalid-feedback {
            color: #ff6b6b;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <!-- Excel Table Link -->
                    <div class="text-end mb-4">
                        <a href="Excel1/excel_table.php" class="btn btn-info">
                            <i class="bi bi-table"></i> إضافة كشف موظفين
                        </a>
                    </div>
                    
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card">
                        <div class="card-body">
                            <h1 class="mb-4">إضافة موظف جديد</h1>
                            
                            <div class="alert alert-info mb-4" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                قم بإدخال بيانات الموظف الجديد. الحقول المميزة بعلامة (*) إلزامية.
                            </div>
                            
                            <!-- Employee Selection Section -->
                            <div class="section-container mb-4" id="employee-selection-section">
                                <h6 class="section-title">تحديث بيانات موظف موجود</h6>
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="employee_selector" class="form-label">اختر الموظف</label>
                                        <select class="form-control select2" id="employee_selector">
                                            <option value="">اختر موظفاً للتحديث</option>
                                            <?php foreach ($employees as $employee): ?>
                                            <option value="<?php echo $employee['id']; ?>">
                                                <?php echo $employee['name_ar_contract'] . ' (' . $employee['Identity_number_contract'] . ')'; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3 d-flex align-items-end">
                                        <button type="button" id="viewEmployeeBtn" class="btn btn-info me-2" disabled>
                                            <i class="bi bi-eye-fill"></i> عرض البيانات
                                        </button>
                                        <button type="button" id="resetFormBtn" class="btn btn-secondary">
                                            <i class="bi bi-arrow-repeat"></i> إضافة جديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <form id="employeeForm" method="post" enctype="multipart/form-data">
                                <!-- Hidden field for employee ID when updating -->
                                <input type="hidden" id="employee_id" name="employee_id" value="0">
                                
                                <!-- Personal Information Section -->
                                <div class="section-container">
                                    <h6 class="section-title">البيانات الشخصية</h6>
                                    <div class="row">
                                    <div class="col-md-6 mb-3">
                                            <label for="name_ar_contract" class="form-label">الاسم العربي *</label>
                                            <input type="text" class="form-control" id="name_ar_contract" name="name_ar_contract" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                            <label for="name_en_contract" class="form-label">الاسم الإنجليزي *</label>
                                            <input type="text" class="form-control" id="name_en_contract" name="name_en_contract" dir="ltr" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="address" class="form-label">العنوان</label>
                                            <input type="text" class="form-control" id="address" name="address">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone_number" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone_number" name="phone_number" oninput="this.value = this.value.replace(/[^0-9]/g, '')" placeholder="اختياري">
                                        </div>
                                    </div>
                                </div>

                                <!-- Identity Information Section -->
                                <div class="section-container">
                                    <h6 class="section-title">معلومات الهوية</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="identity_type" class="form-label">نوع الهوية *</label>
                                            <select class="form-control select2" id="identity_type" onchange="updateIdentityType()" required>
                                                <option value="">اختر نوع الهوية</option>
                                                <option value="Passport|جواز سفر">جواز سفر</option>
                                                <option value="National ID|بطاقة شخصية">بطاقة شخصية</option>
                                            </select>
                                            <input type="text" class="form-control mt-2" id="Identity_type_en" 
                                                   placeholder="ID Type in English" dir="ltr" readonly required>
                                            <input type="hidden" id="Identity_contract_ar" name="Identity_contract_ar" required>
                                            <input type="hidden" id="Identity_contract_en" name="Identity_contract_en" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="Identity_number_contract" class="form-label">رقم الهوية *</label>
                                            <input type="text" class="form-control" id="Identity_number_contract" name="Identity_number_contract" required oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="identity_issue_place" class="form-label">مكان الإصدار *</label>
                                            <select class="form-control select2" id="identity_issue_place" onchange="handleIssuePlaceChange()" required>
                                                <option value="">اختر المحافظة</option>
                                                <option value="Sana'a|صنعاء">صنعاء</option>
                                                <option value="Aden|عدن">عدن</option>
                                                <option value="Taiz|تعز">تعز</option>
                                                <option value="Hodeidah|الحديدة">الحديدة</option>
                                                <option value="Ibb|إب">إب</option>
                                                <option value="Dhamar|ذمار">ذمار</option>
                                                <option value="Hadramaut|حضرموت">حضرموت</option>
                                                <option value="Al-Bayda|البيضاء">البيضاء</option>
                                                <option value="Hajjah|حجة">حجة</option>
                                                <option value="Sa'ada|صعدة">صعدة</option>
                                                <option value="Lahij|لحج">لحج</option>
                                                <option value="Marib|مأرب">مأرب</option>
                                                <option value="Al-Mahwit|المحويت">المحويت</option>
                                                <option value="Al-Jawf|الجوف">الجوف</option>
                                                <option value="Amran|عمران">عمران</option>
                                                <option value="Al-Dhale|الضالع">الضالع</option>
                                                <option value="Raymah|ريمة">ريمة</option>
                                                <option value="Socotra|سقطرى">سقطرى</option>
                                                <option value="custom">أخرى - إدخال يدوي</option>
                                            </select>
                                            <input type="text" class="form-control mt-2" id="custom_issue_place" 
                                                   style="display: none;" placeholder="أدخل مكان الإصدار"
                                                   onchange="handleCustomPlaceInput()" onkeyup="handleCustomPlaceInput()">
                                            <input type="text" class="form-control mt-2" id="Identity_issue_place_en" 
                                                   placeholder="Place of Issue in English" dir="ltr" required>
                                            <input type="hidden" id="Identity_issue_contract_ar" name="Identity_issue_contract_ar" required>
                                            <input type="hidden" id="Identity_issue_contract_en" name="Identity_issue_contract_en" required>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="Identity_issue_date_contract" class="form-label">تاريخ الإصدار *</label>
                                            <input type="date" class="form-control" id="Identity_issue_date_contract" name="Identity_issue_date_contract" required>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="Identity_document" class="form-label">صورة الهوية</label>
                                            <div class="custom-file-input-wrapper">
                                                <input type="file" class="custom-file-input" id="Identity_document" name="Identity_document" accept=".pdf,.jpg,.jpeg,.png">
                                                <div class="custom-file-input-trigger">
                                                    <i class="bi bi-upload"></i>
                                                    <span class="default-text">اختر ملفاً</span>
                                                    <span class="file-name"></span>
                                    </div>
                                                    </div>
                                                </div>
                                            </div>
                                </div>

                                <!-- Employment Information Section -->
                                <div class="section-container">
                                    <h6 class="section-title">معلومات التوظيف</h6>
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="polarization_method_select" class="form-label">طريقة الاستقطاب</label>
                                            <select class="form-control select2" id="polarization_method_select" onchange="handlePolarizationMethodChange()">
                                                <option value="">اختر طريقة الاستقطاب</option>
                                                <option value="استقطاب مباشر">استقطاب مباشر</option>
                                                <option value="توصية">توصية</option>
                                                <option value="موقع توظيف">موقع توظيف</option>
                                                <option value="وسائل التواصل الاجتماعي">وسائل التواصل الاجتماعي</option>
                                                <option value="custom">أخرى - إدخال يدوي</option>
                                            </select>
                                            <input type="text" class="form-control mt-2" id="custom_polarization_method" 
                                                   style="display: none;" placeholder="أدخل طريقة الاستقطاب"
                                                   onchange="updatePolarizationMethod()" onkeyup="updatePolarizationMethod()">
                                            <input type="hidden" id="polarization_method" name="polarization_method">
                                        </div>
                                    </div>
                                </div>

                                <!-- Documents Section -->
                                <div class="section-container">
                                    <h6 class="section-title">المستندات</h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="test_results_document" class="form-label">نتائج الاختبار</label>
                                            <div class="custom-file-input-wrapper">
                                                <input type="file" class="custom-file-input" id="test_results_document" 
                                                       name="test_results_document" 
                                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                                <div class="custom-file-input-trigger">
                                                    <i class="bi bi-upload"></i>
                                                    <span class="default-text">اختر ملفاً</span>
                                                    <span class="file-name"></span>
                                                </div>
                                            </div>
                                            <small class="text-muted">PDF, Word, أو صورة</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="Interview_results_document" class="form-label">نتائج المقابلة</label>
                                            <div class="custom-file-input-wrapper">
                                                <input type="file" class="custom-file-input" id="Interview_results_document" 
                                                       name="Interview_results_document" 
                                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                                <div class="custom-file-input-trigger">
                                                    <i class="bi bi-upload"></i>
                                                    <span class="default-text">اختر ملفاً</span>
                                                    <span class="file-name"></span>
                                                </div>
                                            </div>
                                            <small class="text-muted">PDF, Word, أو صورة</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="guarantee_document" class="form-label">وثيقة الضمان</label>
                                            <div class="custom-file-input-wrapper">
                                                <input type="file" class="custom-file-input" id="guarantee_document" 
                                                       name="guarantee_document" 
                                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                                <div class="custom-file-input-trigger">
                                                    <i class="bi bi-upload"></i>
                                                    <span class="default-text">اختر ملفاً</span>
                                                    <span class="file-name"></span>
                                        </div>
                                    </div>
                                            <small class="text-muted">PDF, Word, أو صورة</small>
                                </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="medical_examination_document" class="form-label">وثيقة الفحص الطبي</label>
                                            <div class="custom-file-input-wrapper">
                                                <input type="file" class="custom-file-input" id="medical_examination_document" 
                                                       name="medical_examination_document" 
                                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                                <div class="custom-file-input-trigger">
                                                    <i class="bi bi-upload"></i>
                                                    <span class="default-text">اختر ملفاً</span>
                                                    <span class="file-name"></span>
                                    </div>
                                    </div>
                                            <small class="text-muted">PDF, Word, أو صورة</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="cv_document" class="form-label">السيرة الذاتية</label>
                                            <div class="custom-file-input-wrapper">
                                                <input type="file" class="custom-file-input" id="cv_document" 
                                                       name="cv_document" 
                                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                                <div class="custom-file-input-trigger">
                                                    <i class="bi bi-upload"></i>
                                                    <span class="default-text">اختر ملفاً</span>
                                                    <span class="file-name"></span>
                                    </div>
                                </div>
                                            <small class="text-muted">PDF, Word, أو صورة</small>
                                    </div>
                                    </div>
                                    </div>

                                <!-- Submit Button -->
                                <div class="text-center mt-4">
                                    <button type="submit" id="saveBtn" class="btn btn-primary btn-lg save-contract-btn">
                                        <i class="bi bi-person-plus-fill me-2"></i>
                                        إضافة الموظف
                                        </button>
                                    <button type="submit" id="updateBtn" class="btn btn-success btn-lg save-contract-btn" style="display:none;">
                                        <i class="bi bi-pencil-square me-2"></i>
                                        تحديث البيانات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
                                <script>
        $(document).ready(function() {
            // Initialize all Select2 dropdowns
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });

            // Custom file input handling
            $('.custom-file-input').on('change', function(e) {
                const fileName = e.target.files[0]?.name;
                const wrapper = $(this).closest('.custom-file-input-wrapper');
                const trigger = wrapper.find('.custom-file-input-trigger');
                const defaultText = trigger.find('.default-text');
                const fileNameSpan = trigger.find('.file-name');
                
                if (fileName) {
                    defaultText.hide();
                    fileNameSpan.text(fileName).show();
                } else {
                    defaultText.show();
                    fileNameSpan.text('').hide();
                }
            });

            // Initialize file inputs
            $('.custom-file-input').each(function() {
                const wrapper = $(this).closest('.custom-file-input-wrapper');
                const trigger = wrapper.find('.custom-file-input-trigger');
                const fileNameSpan = trigger.find('.file-name');
                fileNameSpan.hide();
            });

            // Initialize polarization method value
            handlePolarizationMethodChange();

            // Listen for theme changes
            document.addEventListener('themeChanged', function(e) {
                updateSelect2Theme();
            });

            // Initialize place of issuance handlers
            handleIssuePlaceChange();
            
            // Handle English place of issuance input
            $('#Identity_issue_place_en').on('input', function() {
                document.getElementById('Identity_issue_contract_en').value = this.value;
            });
            
            // Enable View button when employee is selected
            $('#employee_selector').on('change', function() {
                if ($(this).val()) {
                    $('#viewEmployeeBtn').prop('disabled', false);
                } else {
                    $('#viewEmployeeBtn').prop('disabled', true);
                }
            });
            
            // View Employee button click
            $('#viewEmployeeBtn').on('click', function() {
                const employeeId = $('#employee_selector').val();
                if (!employeeId) return;
                
                // Show loading indicator
                $(this).html('<i class="bi bi-hourglass-split"></i> جاري التحميل...');
                $(this).prop('disabled', true);
                
                // Fetch employee data
                $.ajax({
                    url: 'create_employee_yee.php',
                    data: {
                        action: 'get_employee_data',
                        id: employeeId
                    },
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Populate form fields
                        $('#employee_id').val(data.id);
                        $('#name_ar_contract').val(data.name_ar_contract);
                        $('#name_en_contract').val(data.name_en_contract);
                        $('#address').val(data.address);
                        $('#phone_number').val(data.phone_number);
                        
                        // Set identity type
                        if (data.Identity_contract_en) {
                            // Find the option that matches
                            let identityValue = `${data.Identity_contract_en}|${data.Identity_contract_ar}`;
                            $('#identity_type').val(identityValue).trigger('change');
                        }
                        
                        $('#Identity_number_contract').val(data.Identity_number_contract);
                        
                        // Set place of issuance
                        // First check if it's one of our predefined options
                        let foundPlace = false;
                        $('#identity_issue_place option').each(function() {
                            let optionValue = $(this).val();
                            if (optionValue && optionValue !== 'custom') {
                                let [en, ar] = optionValue.split('|');
                                if (en === data.Identity_issue_contract_en && ar === data.Identity_issue_contract_ar) {
                                    $('#identity_issue_place').val(optionValue).trigger('change');
                                    foundPlace = true;
                                    return false; // break the loop
                                }
                            }
                        });
                        
                        // If not found in options, use custom
                        if (!foundPlace && data.Identity_issue_contract_ar) {
                            $('#identity_issue_place').val('custom').trigger('change');
                            $('#custom_issue_place').val(data.Identity_issue_contract_ar);
                            $('#Identity_issue_place_en').val(data.Identity_issue_contract_en);
                            $('#Identity_issue_contract_ar').val(data.Identity_issue_contract_ar);
                            $('#Identity_issue_contract_en').val(data.Identity_issue_contract_en);
                        }
                        
                        $('#Identity_issue_date_contract').val(data.Identity_issue_date_contract);
                        
                        // Set polarization method
                        let foundMethod = false;
                        $('#polarization_method_select option').each(function() {
                            if ($(this).val() === data.polarization_method) {
                                $('#polarization_method_select').val(data.polarization_method).trigger('change');
                                foundMethod = true;
                                return false;
                            }
                        });
                        
                        if (!foundMethod && data.polarization_method) {
                            $('#polarization_method_select').val('custom').trigger('change');
                            $('#custom_polarization_method').val(data.polarization_method);
                            $('#polarization_method').val(data.polarization_method);
                        }
                        
                        // Update file indicators
                        updateFileIndicator('Identity_document', data.Identity_document);
                        updateFileIndicator('test_results_document', data.test_results_document);
                        updateFileIndicator('Interview_results_document', data.Interview_results_document);
                        updateFileIndicator('guarantee_document', data.guarantee_document);
                        updateFileIndicator('medical_examination_document', data.medical_examination_document);
                        updateFileIndicator('cv_document', data.cv_document);
                        
                        // Update form UI
                        $('#saveBtn').hide();
                        $('#updateBtn').show();
                        $('h1.mb-4').text('تحديث بيانات الموظف');
                        
                        // Reset view button
                        $('#viewEmployeeBtn').html('<i class="bi bi-eye-fill"></i> عرض البيانات');
                        $('#viewEmployeeBtn').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        // Show error
                        alert('حدث خطأ أثناء استرجاع بيانات الموظف: ' + (xhr.responseJSON?.error || error));
                        
                        // Reset view button
                        $('#viewEmployeeBtn').html('<i class="bi bi-eye-fill"></i> عرض البيانات');
                        $('#viewEmployeeBtn').prop('disabled', false);
                    }
                });
            });
            
            // Reset Form button click
            $('#resetFormBtn').on('click', function() {
                resetForm();
            });
            
            // Helper function to update file indicators
            function updateFileIndicator(fieldId, hasFile) {
                const fileInput = $(`#${fieldId}`);
                const wrapper = fileInput.closest('.custom-file-input-wrapper');
                const trigger = wrapper.find('.custom-file-input-trigger');
                const defaultText = trigger.find('.default-text');
                const fileNameSpan = trigger.find('.file-name');
                
                if (hasFile) {
                    defaultText.hide();
                    fileNameSpan.text('ملف موجود').show();
                    // Add indicator that file exists
                    trigger.addClass('border-success');
                } else {
                    defaultText.show();
                    fileNameSpan.text('').hide();
                    trigger.removeClass('border-success');
                }
            }
            
            // Helper function to reset form
            function resetForm() {
                // Reset employee ID
                $('#employee_id').val('0');
                
                // Reset form fields
                document.getElementById('employeeForm').reset();
                
                // Reset Select2 dropdowns
                $('#identity_type').val('').trigger('change');
                $('#identity_issue_place').val('').trigger('change');
                $('#polarization_method_select').val('').trigger('change');
                $('#employee_selector').val('').trigger('change');
                
                // Reset file inputs
                $('.custom-file-input-trigger').removeClass('border-success');
                $('.custom-file-input-wrapper .default-text').show();
                $('.custom-file-input-wrapper .file-name').hide();
                
                // Reset button visibility
                $('#saveBtn').show();
                $('#updateBtn').hide();
                
                // Reset form title
                $('h1.mb-4').text('إضافة موظف جديد');
            }
        });

                                    // Handle identity type selection
                                    function updateIdentityType() {
                                        const select = document.getElementById('identity_type');
            const typeEnInput = document.getElementById('Identity_type_en');
                                        const arInput = document.getElementById('Identity_contract_ar');
                                        const enInput = document.getElementById('Identity_contract_en');
            
            if (select.value) {
                const [en, ar] = select.value.split('|');
                typeEnInput.value = en;
                arInput.value = ar;
                enInput.value = en;
                                        } else {
                typeEnInput.value = '';
                                            arInput.value = '';
                                            enInput.value = '';
            }
        }

        // Handle recruitment method selection
        function handlePolarizationMethodChange() {
            const select = document.getElementById('polarization_method_select');
            const customInput = document.getElementById('custom_polarization_method');
            const hiddenInput = document.getElementById('polarization_method');
            
            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.required = true;
                hiddenInput.value = customInput.value;
            } else {
                customInput.style.display = 'none';
                customInput.required = false;
                hiddenInput.value = select.value;
            }
        }

        // Update hidden input when custom value changes
        function updatePolarizationMethod() {
            const customInput = document.getElementById('custom_polarization_method');
            const hiddenInput = document.getElementById('polarization_method');
            hiddenInput.value = customInput.value;
        }

                                    // Handle place of issuance selection
                                    function handleIssuePlaceChange() {
                                        const select = document.getElementById('identity_issue_place');
                                        const customInput = document.getElementById('custom_issue_place');
            const enInput = document.getElementById('Identity_issue_place_en');
                                        const arInput = document.getElementById('Identity_issue_contract_ar');
            const enHiddenInput = document.getElementById('Identity_issue_contract_en');

                                        if (select.value === 'custom') {
                                            customInput.style.display = 'block';
                                            customInput.required = true;
                enInput.readOnly = false;
                                            arInput.value = customInput.value;
                enHiddenInput.value = enInput.value;
                                        } else if (select.value) {
                                            customInput.style.display = 'none';
                                            customInput.required = false;
                                            const [en, ar] = select.value.split('|');
                                            enInput.value = en;
                enInput.readOnly = true;
                arInput.value = ar;
                enHiddenInput.value = en;
                                        } else {
                                            customInput.style.display = 'none';
                                            customInput.required = false;
                                            enInput.value = '';
                enInput.readOnly = true;
                arInput.value = '';
                enHiddenInput.value = '';
                                        }
                                    }

                                    // Handle custom place input
                                    function handleCustomPlaceInput() {
                                        const customInput = document.getElementById('custom_issue_place');
            const enInput = document.getElementById('Identity_issue_place_en');
                                        const arInput = document.getElementById('Identity_issue_contract_ar');
            const enHiddenInput = document.getElementById('Identity_issue_contract_en');
                                        
                                        arInput.value = customInput.value;
            enHiddenInput.value = enInput.value;
        }

        // Update form validation
        function validateForm() {
            let isValid = true;
            const requiredFields = [
                { id: 'name_ar_contract', name: 'الاسم العربي' },
                { id: 'name_en_contract', name: 'الاسم الإنجليزي' },
                { id: 'identity_type', name: 'نوع الهوية' },
                { id: 'Identity_number_contract', name: 'رقم الهوية' },
                { id: 'identity_issue_place', name: 'مكان الإصدار' },
                { id: 'Identity_issue_place_en', name: 'مكان الإصدار بالإنجليزي' },
                { id: 'Identity_issue_date_contract', name: 'تاريخ الإصدار' }
            ];

            // Remove previous error styling
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').remove();

            // Check each required field
            requiredFields.forEach(field => {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    isValid = false;
                    markFieldAsInvalid(element, `حقل ${field.name} مطلوب`);
                }
            });

            // Special validation for hidden required fields
            const hiddenRequiredFields = [
                { id: 'Identity_contract_ar', name: 'نوع الهوية بالعربية' },
                { id: 'Identity_contract_en', name: 'نوع الهوية بالإنجليزية' },
                { id: 'Identity_issue_contract_ar', name: 'مكان الإصدار بالعربية' },
                { id: 'Identity_issue_contract_en', name: 'مكان الإصدار بالإنجليزية' }
            ];
            
            hiddenRequiredFields.forEach(field => {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    isValid = false;
                    // For hidden fields, mark the related visible field as invalid
                    if (field.id === 'Identity_contract_ar' || field.id === 'Identity_contract_en') {
                        markFieldAsInvalid(document.getElementById('identity_type'), `${field.name} مطلوب`);
                    } else if (field.id === 'Identity_issue_contract_ar' || field.id === 'Identity_issue_contract_en') {
                        markFieldAsInvalid(document.getElementById('identity_issue_place'), `${field.name} مطلوب`);
                    }
                }
            });

            // Special validation for custom inputs when they're visible and required
            const issuePlaceSelect = document.getElementById('identity_issue_place');
            const customIssuePlace = document.getElementById('custom_issue_place');
            const issuePlaceEn = document.getElementById('Identity_issue_place_en');
            
            if (issuePlaceSelect.value === 'custom') {
                if (!customIssuePlace.value.trim()) {
                    isValid = false;
                    markFieldAsInvalid(customIssuePlace, 'يرجى إدخال مكان الإصدار');
                }
                if (!issuePlaceEn.value.trim()) {
                    isValid = false;
                    markFieldAsInvalid(issuePlaceEn, 'يرجى إدخال مكان الإصدار بالإنجليزي');
                }
            }

            if (!isValid) {
                // Scroll to the first invalid field
                const firstInvalid = document.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }

            return isValid;
        }

        // Helper function to mark invalid fields
        function markFieldAsInvalid(element, message) {
            element.classList.add('is-invalid');
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = message;
            element.parentNode.appendChild(feedback);
        }

        // Add form submit handler
        document.getElementById('employeeForm').addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
            }
        });

        // Ensure numeric fields only accept numbers
        document.addEventListener('DOMContentLoaded', function() {
            // Add numeric validation for phone and identity number
            const numericInputs = ['phone_number', 'Identity_number_contract'];
            
            numericInputs.forEach(function(id) {
                const input = document.getElementById(id);
                input.addEventListener('keypress', function(e) {
                    // Allow only 0-9 keys
                    if (!/^\d*$/.test(e.key)) {
                        e.preventDefault();
                    }
                });
                
                // Handle paste events
                input.addEventListener('paste', function(e) {
                    const clipboardData = e.clipboardData || window.clipboardData;
                    const pastedText = clipboardData.getData('text');
                    if (!/^\d*$/.test(pastedText)) {
                        e.preventDefault();
                    }
                });
            });
        });
                                </script>
</body>
</html>

