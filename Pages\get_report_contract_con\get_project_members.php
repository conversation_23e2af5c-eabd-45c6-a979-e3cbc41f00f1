<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    // Read database connection details
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    $project_id = isset($_GET['project_id']) ? intval($_GET['project_id']) : 0;
    $type = isset($_GET['type']) ? $_GET['type'] : '';

    if (!$project_id) {
        throw new Exception('Invalid project ID');
    }

    if ($type === 'employees') {
        // Get employees assigned to the project through contracts
        $sql = "SELECT DISTINCT e.id_employees, e.name_ar_contract, e.name_en_contract 
                FROM employees e 
                INNER JOIN contract c ON e.id_employees = c.id_employees 
                WHERE c.id_Project = ? AND e.status = 1";
    } elseif ($type === 'delegates') {
        // Get delegates assigned to the project
        $sql = "SELECT id_assigned, name_ar_assigned, name_en_assigned 
                FROM assigned 
                WHERE id_Project = ? AND status = 1";
    } else {
        throw new Exception('Invalid type specified');
    }

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $project_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode($data);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?> 