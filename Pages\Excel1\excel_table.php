<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة كشف موظفين - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    <link href="../../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        .excel-container {
            height: 600px;
            overflow: auto;
            position: relative;
            border: 1px solid #ddd;
            max-width: 100%;
            scrollbar-width: thin;
            scrollbar-color: #888 #f1f1f1;
        }
        
        /* For WebKit browsers (Chrome, Safari) */
        .excel-container::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        
        .excel-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .excel-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 5px;
        }
        
        .excel-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        .excel-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-family: 'Cairo', sans-serif;
            table-layout: fixed;
            min-width: 100%;
        }
        
        .excel-table th, .excel-table td {
            border: 1px solid #ddd;
            padding: 8px;
            position: relative;
            min-width: 120px;
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .excel-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .date-format-hint {
            font-size: 11px;
            color: #666;
            font-weight: normal;
        }
        
        .excel-table .editable {
            min-height: 20px;
            width: 100%;
            outline: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* Styling for date input field */
        .editable[dir="ltr"] {
            text-align: left;
        }
        
        .excel-table .editable:focus {
            background-color: #f0f8ff;
            box-shadow: 0 0 0 2px #007bff;
            white-space: normal;
            overflow: visible;
        }
        
        .excel-mode-toggle {
            margin-bottom: 15px;
        }
        
        .excel-table .cell-selected {
            background-color: #e6f2ff;
        }
        
        .excel-tools {
            margin-bottom: 20px;
        }
        
        #saveData {
            margin-left: 10px;
        }
        
        .column-resizer {
            position: absolute;
            top: 0;
            left: 0; /* For RTL layout, the resizer goes on left side */
            width: 5px;
            height: 100%;
            background: transparent;
            cursor: col-resize;
            user-select: none;
            touch-action: none;
            z-index: 20;
        }
        
        .column-resizer:hover, .resizing {
            background: #0095ff;
        }
        
        .add-row-btn {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 8px 15px;
            text-align: center;
            z-index: 100;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        /* Hide empty rows in view mode */
        .excel-table tr.empty-row.view-mode {
            display: none;
        }
        
        .scroll-hint {
            position: absolute;
            bottom: 40px;
            right: 15px;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 100;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        /* Invalid data styling */
        .excel-table .invalid-data {
            background-color: rgba(255, 200, 200, 0.3);
        }
        
        /* Row completion status styling */
        .excel-table tr.fully-complete {
            background-color: rgba(0, 255, 0, 0.1); /* Transparent green */
        }
        
        .excel-table tr.partially-complete {
            background-color: rgba(255, 0, 0, 0.1); /* Transparent red */
        }
        
        /* Tab styling - modified to be hidden by default */
        .form-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .form-tab {
            padding: 12px 20px;
            cursor: pointer;
            font-weight: 600;
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            margin-bottom: -1px;
            transition: all 0.2s ease;
            font-size: 16px;
        }
        
        .form-tab.active {
            border-color: #dee2e6 #dee2e6 #fff;
            background-color: #fff;
            color: #007bff;
        }
        
        .form-tab:not(.active) {
            background-color: #f8f9fa;
        }
        
        .form-tab:not(.active):hover {
            background-color: #e9ecef;
        }
        
        .form-content {
            display: none;
        }
        
        .form-content.active {
            display: block;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar - adjust path as needed
    include '../sidebar.php';
    
    // Determine which page referred to this one
    $referrer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    $show_employees = true; // Default
    $show_delegates = true; // Default
    $show_tabs = true;      // Default - show tabs
    
    // Check if from specific pages
    if (strpos($referrer, 'create_employee_yee.php') !== false) {
        $show_employees = true;
        $show_delegates = false;
        $show_tabs = false; // Hide tabs when coming from employee page
    } elseif (strpos($referrer, 'create_assigned_ned.php') !== false) {
        $show_employees = false;
        $show_delegates = true;
        $show_tabs = false; // Hide tabs when coming from delegates page
    }
    ?>
    
    <!-- Main Content -->
    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h1 class="mb-4">إضافة كشف موظفين</h1>
                            
                            <!-- Form Tabs - only shown when not accessed from specific pages -->
                            <?php if ($show_tabs): ?>
                            <div class="form-tabs">
                                <div class="form-tab <?php echo $show_employees ? 'active' : ''; ?>" data-target="employees-form">الموظفين</div>
                                <div class="form-tab <?php echo (!$show_employees && $show_delegates) ? 'active' : ''; ?>" data-target="delegates-form">المندوبين</div>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Employees Form -->
                            <?php if ($show_employees): ?>
                            <div id="employees-form" class="form-content <?php echo $show_employees ? 'active' : ''; ?>">
                                <div class="excel-tools d-flex justify-content-between">
                                    <div class="excel-mode-toggle">
                                        <button id="editModeBtn-employees" class="btn btn-primary active">وضع التحرير</button>
                                        <button id="viewModeBtn-employees" class="btn btn-outline-primary">وضع العرض</button>
                                    </div>
                                    
                                    <div>
                                        <button id="clearData-employees" class="btn btn-danger">تفريغ الجدول</button>
                                        <button id="sendGreenRows-employees" class="btn btn-success ms-2"><i class="bi bi-send"></i> إرسال البيانات المكتملة</button>
                                    </div>
                                </div>
                                
                                <div class="excel-container">
                                    <table id="excelTable-employees" class="excel-table">
                                        <thead>
                                            <tr id="headerRow-employees">
                                                <!-- Headers will be generated dynamically -->
                                            </tr>
                                        </thead>
                                        <tbody id="tableBody-employees">
                                            <!-- Table body will be populated dynamically -->
                                        </tbody>
                                    </table>
                                    <div id="scrollHint-employees" class="scroll-hint">اسحب للجانب <i class="bi bi-arrow-left-right"></i></div>
                                </div>
                                
                                <div class="add-row-btn">
                                    <button id="addRowBtn-employees" class="btn btn-outline-primary"><i class="bi bi-plus-circle"></i> إضافة موظف جديد</button>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Delegates Form -->
                            <?php if ($show_delegates): ?>
                            <div id="delegates-form" class="form-content <?php echo (!$show_employees && $show_delegates) ? 'active' : ''; ?>">
                                <div class="excel-tools d-flex justify-content-between">
                                    <div class="excel-mode-toggle">
                                        <button id="editModeBtn-delegates" class="btn btn-primary active">وضع التحرير</button>
                                        <button id="viewModeBtn-delegates" class="btn btn-outline-primary">وضع العرض</button>
                                    </div>
                                    
                                    <div>
                                        <button id="clearData-delegates" class="btn btn-danger">تفريغ الجدول</button>
                                        <button id="sendGreenRows-delegates" class="btn btn-success ms-2"><i class="bi bi-send"></i> إرسال البيانات المكتملة</button>
                                    </div>
                                </div>
                                
                                <!-- Project Selection for Delegates -->
                                <div class="row mb-3 mt-3">
                                    <div class="col-md-4">
                                        <label for="delegate-project-select" class="form-label">المشروع <span class="text-danger">*</span></label>
                                        <select id="delegate-project-select" class="form-select" required>
                                            <option value="">اختر المشروع</option>
                                            <?php
                                            // Get projects from database
                                            try {
                                                $file = fopen(__DIR__ . "/../connection/one.txt", "r");
                                                if (!$file) {
                                                    throw new Exception('خطأ في قراءة ملف الإعدادات');
                                                }
                                                
                                                $servername = trim(fgets($file));
                                                $username = trim(fgets($file));
                                                $password = trim(fgets($file));
                                                $dbname = trim(fgets($file));
                                                fclose($file);
                                                
                                                $conn = new mysqli($servername, $username, $password, $dbname);
                                                $conn->set_charset("utf8");
                                                
                                                if ($conn->connect_error) {
                                                    throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
                                                }
                                                
                                                // Get active projects
                                                $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
                                                
                                                if ($result && $result->num_rows > 0) {
                                                    while ($row = $result->fetch_assoc()) {
                                                        echo '<option value="' . $row['id_Project'] . '">' . $row['Project_name'] . '</option>';
                                                    }
                                                }
                                                
                                                $conn->close();
                                            } catch (Exception $e) {
                                                echo '<option value="" disabled>خطأ في تحميل المشاريع</option>';
                                            }
                                            ?>
                                        </select>
                                        <div id="project-error" class="text-danger" style="display: none;">الرجاء اختيار المشروع قبل إرسال البيانات</div>
                                    </div>
                                </div>
                                
                                <div class="excel-container">
                                    <table id="excelTable-delegates" class="excel-table">
                                        <thead>
                                            <tr id="headerRow-delegates">
                                                <!-- Headers will be generated dynamically -->
                                            </tr>
                                        </thead>
                                        <tbody id="tableBody-delegates">
                                            <!-- Table body will be populated dynamically -->
                                        </tbody>
                                    </table>
                                    <div id="scrollHint-delegates" class="scroll-hint">اسحب للجانب <i class="bi bi-arrow-left-right"></i></div>
                                </div>
                                
                                <div class="add-row-btn">
                                    <button id="addRowBtn-delegates" class="btn btn-outline-primary"><i class="bi bi-plus-circle"></i> إضافة مندوب جديد</button>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Progress Modal -->
    <div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="progressModalLabel">جاري إضافة الموظفين</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p>يرجى الانتظار، جاري إضافة الموظفين...</p>
                    <div class="progress mb-3">
                        <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <p id="progressStatus">0 من 0 تم الإضافة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Modal -->
    <div class="modal fade" id="resultsModal" tabindex="-1" aria-labelledby="resultsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="resultsModalLabel">نتائج إضافة الموظفين</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table id="resultsTable" class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>الاسم العربي</th>
                                    <th>الاسم الإنجليزي</th>
                                    <th>رقم الهوية</th>
                                    <th>الحالة</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                                <!-- Results will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- Buttons will be added dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../../assets/js/sidebar.js"></script>
    
    <script>
        $(document).ready(function() {
            // Tab switching functionality
            $('.form-tab').click(function() {
                // Remove active class from all tabs and contents
                $('.form-tab').removeClass('active');
                $('.form-content').removeClass('active');
                
                // Add active class to clicked tab
                $(this).addClass('active');
                
                // Show corresponding content
                const targetId = $(this).data('target');
                $('#' + targetId).addClass('active');
            });
            
            // Initialize employees table
            <?php if ($show_employees): ?>
            initTable('employees', 8, [
                150, // الاسم العربي
                150, // الاسم الإنجليزي
                140, // نوع الهوية ar
                140, // نوع الهوية en
                140, // رقم الهوية
                150, // مكان الإصدار ar
                150, // مكان الإصدار en
                140  // تاريخ الإصدار
            ], [
                "الاسم العربي", "الاسم الإنجليزي", "نوع الهوية ar", "نوع الهوية en", 
                "رقم الهوية", "مكان الإصدار ar", "مكان الإصدار en", "تاريخ الإصدار"
            ], {
                4: 'numeric', // رقم الهوية (ID number) - index 4 is the 5th column (0-based)
                7: 'date'     // تاريخ الإصدار (ID issuance date) - index 7 is the 8th column
            });
            <?php endif; ?>
            
            // Initialize delegates table
            <?php if ($show_delegates): ?>
            initTable('delegates', 5, [
                150, // الاسم العربي
                150, // الاسم الإنجليزي
                140, // نوع الهوية ar
                140, // نوع الهوية en
                140  // رقم الهوية
            ], [
                "الاسم العربي", "الاسم الإنجليزي", "نوع الهوية ar", "نوع الهوية en", 
                "رقم الهوية"
            ], {
                4: 'numeric' // رقم الهوية (ID number) - index 4 is the 5th column (0-based)
            });
            <?php endif; ?>
            
            // Function to initialize a table
            function initTable(tableId, totalColumns, colWidths, colLabels, colTypes) {
                let currentRowCount = 0;
                let isResizing = false;
                let currentResizer = null;
                let startX, startWidth;
                let columnWidths = colWidths;
                let columnLabels = colLabels;
                const TOTAL_COLUMNS = totalColumns;
                
                // Column type definitions
                const columnTypes = colTypes;
                
                // Validate data based on column type
                function validateData(value, columnIndex) {
                    // If column doesn't have a specific type, any value is valid
                    if (!columnTypes[columnIndex]) {
                        return true;
                    }
                    
                    // Validate based on column type
                    switch (columnTypes[columnIndex]) {
                        case 'numeric':
                            // Allow only digits, can include a decimal point
                            return /^\d+(\.\d+)?$/.test(value.trim());
                        
                        case 'date':
                            // Validate date in day/month/year format (DD/MM/YYYY)
                            const datePattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
                            if (!datePattern.test(value.trim())) {
                                return false;
                            }
                            
                            // Further validate the date components are valid
                            const matches = value.trim().match(datePattern);
                            const day = parseInt(matches[1], 10);
                            const month = parseInt(matches[2], 10);
                            const year = parseInt(matches[3], 10);
                            
                            // Check if month and day are valid
                            if (month < 1 || month > 12) return false;
                            
                            // Get days in month (accounting for leap years)
                            const daysInMonth = new Date(year, month, 0).getDate();
                            if (day < 1 || day > daysInMonth) return false;
                            
                            return true;
                        
                        default:
                            return true;
                    }
                }
                
                // Process cell content based on column type
                function processCellContent(value, columnIndex) {
                    if (!value || value.trim() === '') {
                        return '';
                    }
                    
                    // If valid for the column type, return the value
                    if (validateData(value, columnIndex)) {
                        return value;
                    }
                    
                    // If invalid, return empty string
                    return '';
                }
                
                // Initialize header row
                function initializeHeaderRow() {
                    let headerHtml = '';
                    
                    for (let i = 0; i < TOTAL_COLUMNS; i++) {
                        headerHtml += `<th style="width: ${columnWidths[i]}px">
                                       ${columnLabels[i]}
                                       ${i === 7 && columnLabels[i] === "تاريخ الإصدار" ? '<div class="date-format-hint">(السنة/الشهر/اليوم)</div>' : ''}
                                       <div class="column-resizer" data-index="${i}"></div>
                                     </th>`;
                    }
                    
                    $(`#headerRow-${tableId}`).html(headerHtml);
                }
                
                // Initialize with empty rows
                function addInitialRows(count) {
                    for (let i = 0; i < count; i++) {
                        addNewRow();
                    }
                }
                
                // Add a new row to the table
                function addNewRow() {
                    let rowHtml = '<tr>';
                    for (let j = 0; j < TOTAL_COLUMNS; j++) {
                        // Special handling for date column - add direction attribute
                        const dirAttribute = (j === 7 && tableId === 'employees') ? ' dir="ltr"' : '';
                        
                        rowHtml += `<td style="width: ${columnWidths[j]}px">
                            <div class="editable" contenteditable="true" data-row="${currentRowCount}" data-col="${j}" data-table="${tableId}"${dirAttribute}></div>
                        </td>`;
                    }
                    rowHtml += '</tr>';
                    $(`#tableBody-${tableId}`).append(rowHtml);
                    currentRowCount++;
                    
                    // Mark as empty initially
                    $(`.editable[data-row="${currentRowCount-1}"][data-col="0"][data-table="${tableId}"]`).closest('tr').addClass('empty-row');
                    
                    // Re-bind event handlers
                    bindCellEvents();
                }
                
                // Check if a row is empty
                function isRowEmpty(rowIndex) {
                    let isEmpty = true;
                    
                    for (let j = 0; j < TOTAL_COLUMNS; j++) {
                        const cellContent = $(`.editable[data-row="${rowIndex}"][data-col="${j}"][data-table="${tableId}"]`).text().trim();
                        if (cellContent !== '') {
                            isEmpty = false;
                            break;
                        }
                    }
                    
                    return isEmpty;
                }
                
                // Check if a row is fully complete (all fields filled)
                function isRowFullyComplete(rowIndex) {
                    for (let j = 0; j < TOTAL_COLUMNS; j++) {
                        const cellContent = $(`.editable[data-row="${rowIndex}"][data-col="${j}"][data-table="${tableId}"]`).text().trim();
                        if (cellContent === '') {
                            return false;
                        }
                    }
                    return true;
                }
                
                // Check if a row is partially complete (at least one field filled, but not all)
                function isRowPartiallyComplete(rowIndex) {
                    let hasFilledField = false;
                    let hasEmptyField = false;
                    
                    for (let j = 0; j < TOTAL_COLUMNS; j++) {
                        const cellContent = $(`.editable[data-row="${rowIndex}"][data-col="${j}"][data-table="${tableId}"]`).text().trim();
                        if (cellContent === '') {
                            hasEmptyField = true;
                        } else {
                            hasFilledField = true;
                        }
                        
                        // If we've found both a filled and empty field, we can stop checking
                        if (hasFilledField && hasEmptyField) {
                            break;
                        }
                    }
                    
                    return hasFilledField && hasEmptyField;
                }
                
                // Update row completion status
                function updateRowCompletionStatus(rowIndex) {
                    const $row = $(`.editable[data-row="${rowIndex}"][data-col="0"][data-table="${tableId}"]`).closest('tr');
                    
                    // Remove existing status classes
                    $row.removeClass('fully-complete partially-complete empty-row');
                    
                    if (isRowFullyComplete(rowIndex)) {
                        $row.addClass('fully-complete');
                    } else if (isRowPartiallyComplete(rowIndex)) {
                        $row.addClass('partially-complete');
                    } else if (isRowEmpty(rowIndex)) {
                        $row.addClass('empty-row');
                    }
                }
                
                // Mark empty rows
                function markEmptyRows() {
                    for (let i = 0; i < currentRowCount; i++) {
                        updateRowCompletionStatus(i);
                    }
                }
                
                // Update empty row state for a specific row
                function updateEmptyRowState(rowIndex) {
                    updateRowCompletionStatus(rowIndex);
                }
                
                // Send green rows to create_employee_yee.php
                $(`#sendGreenRows-${tableId}`).click(function() {
                    // Find all green (fully complete) rows
                    const $greenRows = $(`#tableBody-${tableId} tr.fully-complete`);
                    
                    if ($greenRows.length === 0) {
                        alert('لا توجد بيانات مكتملة لإرسالها. يرجى إكمال بيانات صف واحد على الأقل.');
                        return;
                    }
                    
                    // For delegates, check if a project is selected
                    if (tableId === 'delegates') {
                        const selectedProject = $('#delegate-project-select').val();
                        if (!selectedProject) {
                            $('#project-error').show();
                            // Scroll to the project selection
                            $('#delegate-project-select')[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                            return;
                        } else {
                            $('#project-error').hide();
                        }
                    }
                    
                    const itemList = [];
                    let processingUrl = '../process_employee.php';
                    let dataParamName = 'employee';
                    
                    // Set the correct URL and parameter name based on the table ID
                    if (tableId === 'delegates') {
                        processingUrl = '../process_delegate.php';
                        dataParamName = 'delegate';
                    }
                    
                    // Collect data from green rows
                    $greenRows.each(function() {
                        const item = {};
                        $(this).find('.editable').each(function() {
                            const colIndex = $(this).data('col');
                            const colLabel = columnLabels[colIndex];
                            item[colLabel] = $(this).text().trim();
                        });
                        
                        // For delegates, add project ID
                        if (tableId === 'delegates') {
                            item['id_Project'] = $('#delegate-project-select').val();
                        }
                        
                        itemList.push(item);
                    });
                    
                    // Show progress modal
                    $('#progressModal .modal-title').text(tableId === 'employees' ? 'جاري إضافة الموظفين' : 'جاري إضافة المندوبين');
                    $('#progressModal p:first-of-type').text(tableId === 'employees' ? 'يرجى الانتظار، جاري إضافة الموظفين...' : 'يرجى الانتظار، جاري إضافة المندوبين...');
                    $('#progressModal').modal('show');
                    
                    // Initialize progress tracking
                    const totalItems = itemList.length;
                    let processedCount = 0;
                    const results = [];
                    
                    // Process each item sequentially
                    function processNextItem(index) {
                        if (index >= itemList.length) {
                            // All items processed
                            setTimeout(function() {
                                $('#progressModal').modal('hide');
                                displayResults(results, tableId);
                            }, 500);
                            return;
                        }
                        
                        const item = itemList[index];
                        const data = {};
                        data[dataParamName] = item;
                        data['action'] = 'add_from_excel';
                        
                        // Send data via AJAX
                        $.ajax({
                            url: processingUrl,
                            type: 'POST',
                            data: data,
                            dataType: 'json',
                            success: function(response) {
                                processedCount++;
                                
                                // Update progress
                                const percent = Math.round((processedCount / totalItems) * 100);
                                $('#progressBar').css('width', percent + '%').attr('aria-valuenow', percent).text(percent + '%');
                                $('#progressStatus').text(processedCount + ' من ' + totalItems + ' تم الإضافة');
                                
                                // Store result
                                results.push({
                                    name_ar: item["الاسم العربي"],
                                    name_en: item["الاسم الإنجليزي"],
                                    id_number: item["رقم الهوية"],
                                    status: response.success ? 'success' : 'error',
                                    message: response.message
                                });
                                
                                // Process next item
                                processNextItem(index + 1);
                            },
                            error: function(xhr, status, error) {
                                processedCount++;
                                
                                // Update progress
                                const percent = Math.round((processedCount / totalItems) * 100);
                                $('#progressBar').css('width', percent + '%').attr('aria-valuenow', percent).text(percent + '%');
                                $('#progressStatus').text(processedCount + ' من ' + totalItems + ' تم الإضافة');
                                
                                // Store error result
                                results.push({
                                    name_ar: item["الاسم العربي"],
                                    name_en: item["الاسم الإنجليزي"],
                                    id_number: item["رقم الهوية"],
                                    status: 'error',
                                    message: 'خطأ في الاتصال بالخادم'
                                });
                                
                                // Process next item
                                processNextItem(index + 1);
                            }
                        });
                    }
                    
                    // Start processing
                    processNextItem(0);
                });
                
                // Display results in the modal
                function displayResults(results, tableType) {
                    // Update modal title based on table type
                    $('#resultsModalLabel').text(tableType === 'employees' ? 'نتائج إضافة الموظفين' : 'نتائج إضافة المندوبين');
                    
                    // Clear previous results
                    $('#resultsTableBody').empty();
                    
                    // Track if there are any successful rows
                    let hasSuccessfulRows = false;
                    
                    // Add rows to the results table
                    results.forEach(function(result) {
                        const statusClass = result.status === 'success' ? 'success' : 'danger';
                        const statusText = result.status === 'success' ? 'تم الإضافة' : 'فشل';
                        
                        if (result.status === 'success') {
                            hasSuccessfulRows = true;
                        }
                        
                        const row = `
                            <tr data-status="${result.status}" data-name-ar="${result.name_ar}">
                                <td>${result.name_ar}</td>
                                <td>${result.name_en}</td>
                                <td>${result.id_number}</td>
                                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                                <td>${result.message}</td>
                            </tr>
                        `;
                        
                        $('#resultsTableBody').append(row);
                    });
                    
                    // Update modal footer - remove the delete button since deletion is now automatic
                    const $modalFooter = $('#resultsModal .modal-footer');
                    $modalFooter.empty();
                    $modalFooter.append(`
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    `);
                    
                    // Store the results and table type for use when modal is closed
                    $('#resultsModal').data('results', results);
                    $('#resultsModal').data('tableType', tableType);
                    
                    // Show results modal
                    $('#resultsModal').modal('show');
                    
                    // Setup event handler for when the modal is hidden
                    $('#resultsModal').off('hidden.bs.modal').on('hidden.bs.modal', function() {
                        const storedResults = $(this).data('results');
                        const storedTableType = $(this).data('tableType');
                        
                        // Only proceed if we have results and a table type
                        if (storedResults && storedTableType) {
                            // Auto-delete successful rows when modal is closed
                            removeSuccessfulRows(storedResults, storedTableType);
                        }
                    });
                }
                
                // Remove rows that were successfully added to the database
                function removeSuccessfulRows(results, tableType) {
                    // Count the number of successful results
                    const successfulResults = results.filter(result => result.status === 'success');
                    
                    if (successfulResults.length === 0) {
                        return;
                    }
                    
                    // Create an array of Arabic names of successfully added items
                    const successfulNames = successfulResults.map(result => result.name_ar);
                    
                    // Find all rows in the table that match the successful names
                    let rowsRemoved = 0;
                    
                    // Loop through all rows in the table
                    $(`#tableBody-${tableType} tr`).each(function() {
                        const $row = $(this);
                        const rowNameAr = $row.find(`.editable[data-col="0"][data-table="${tableType}"]`).text().trim();
                        
                        // If this row's name matches a successful name, remove it
                        if (successfulNames.includes(rowNameAr)) {
                            $row.remove(); // Immediately remove without animation
                            rowsRemoved++;
                        }
                    });
                    
                    // Update row indexes after removal
                    updateRowIndexes(tableType);
                    
                    // Save the updated table to localStorage
                    saveTableData(tableType);
                }
                
                // Update row indexes after removing rows
                function updateRowIndexes(tableType) {
                    let newRowIndex = 0;
                    
                    $(`#tableBody-${tableType} tr`).each(function() {
                        const $row = $(this);
                        
                        // Update data-row attributes for all cells in this row
                        $row.find(`.editable[data-table="${tableType}"]`).each(function() {
                            $(this).attr('data-row', newRowIndex);
                        });
                        
                        newRowIndex++;
                    });
                }
                
                // Function to save table data
                function saveTableData(tableType) {
                    const tableData = [];
                    
                    // Loop through all rows in the table
                    $(`#tableBody-${tableType} tr`).each(function() {
                        const rowData = [];
                        
                        // Get data from each cell in the row
                        $(this).find(`.editable[data-table="${tableType}"]`).each(function() {
                            rowData.push($(this).text().trim());
                        });
                        
                        // Add row data to table data array
                        tableData.push(rowData);
                    });
                    
                    // Save to localStorage
                    localStorage.setItem(`excelTableData-${tableType}`, JSON.stringify(tableData));
                    
                    // Save metadata if needed
                    const metadata = {
                        columnWidths: columnWidths
                    };
                    localStorage.setItem(`excelTableMetadata-${tableType}`, JSON.stringify(metadata));
                }
                
                // Bind events to cells
                function bindCellEvents() {
                    // Cell Selection
                    $(`.editable[data-table="${tableId}"]`).off('click').on('click', function() {
                        $(`.editable[data-table="${tableId}"]`).removeClass("cell-selected");
                        $(this).addClass("cell-selected");
                    });
                    
                    // Handle paste from Excel
                    $(`.editable[data-table="${tableId}"]`).off('paste').on('paste', function(e) {
                        e.preventDefault();
                        
                        // Get clipboard data
                        const clipboardData = e.originalEvent.clipboardData || window.clipboardData;
                        const pastedData = clipboardData.getData('text');
                        
                        // Check if there are multiple rows (separated by newlines)
                        if (pastedData.includes('\n') || pastedData.includes('\r')) {
                            const rows = pastedData.split(/[\r\n]+/).filter(row => row.trim() !== '');
                            
                            // Get starting position
                            const startRow = parseInt($(this).data('row'));
                            const startCol = parseInt($(this).data('col'));
                            
                            // Ensure we have enough rows
                            const neededRows = startRow + rows.length;
                            while (currentRowCount < neededRows) {
                                addNewRow();
                            }
                            
                            // Paste each row
                            rows.forEach((row, rowIndex) => {
                                const cells = row.split('\t');
                                
                                cells.forEach((cellValue, colIndex) => {
                                    const targetRow = startRow + rowIndex;
                                    const targetCol = startCol + colIndex;
                                    
                                    if (targetCol < TOTAL_COLUMNS) {  // Make sure we don't go out of bounds horizontally
                                        const targetCell = $(`.editable[data-row="${targetRow}"][data-col="${targetCol}"][data-table="${tableId}"]`);
                                        
                                        // Process cell content based on column type
                                        const processedValue = processCellContent(cellValue, targetCol);
                                        targetCell.text(processedValue);
                                        
                                        // Mark as invalid if original value is different from processed value
                                        if (cellValue.trim() !== '' && processedValue === '') {
                                            targetCell.addClass('invalid-data');
                                        } else {
                                            targetCell.removeClass('invalid-data');
                                        }
                                    }
                                });
                                
                                // Update empty state for this row
                                updateEmptyRowState(startRow + rowIndex);
                            });
                            
                            // Auto-save after pasting multiple rows
                            setTimeout(() => {
                                saveTableData(tableId);
                            }, 200);
                        } else {
                            // For a single cell paste
                            const colIndex = parseInt($(this).data('col'));
                            const processedValue = processCellContent(pastedData, colIndex);
                            
                            // Set the processed value
                            $(this).text(processedValue);
                            
                            // Mark as invalid if original value is different from processed value
                            if (pastedData.trim() !== '' && processedValue === '') {
                                $(this).addClass('invalid-data');
                            } else {
                                $(this).removeClass('invalid-data');
                            }
                            
                            // Update empty state for this row
                            const rowIndex = parseInt($(this).data('row'));
                            updateEmptyRowState(rowIndex);
                            
                            // Auto-save after pasting a single cell
                            setTimeout(() => {
                                saveTableData(tableId);
                            }, 200);
                        }
                    });
                    
                    // Validate data on input
                    $(`.editable[data-table="${tableId}"]`).off('input').on('input', function() {
                        const colIndex = parseInt($(this).data('col'));
                        const value = $(this).text().trim();
                        
                        // Check if the column has type validation
                        if (columnTypes[colIndex]) {
                            if (value === '' || validateData(value, colIndex)) {
                                $(this).removeClass('invalid-data');
                            } else {
                                $(this).addClass('invalid-data');
                            }
                        }
                        
                        // Update row status
                        const rowIndex = parseInt($(this).data('row'));
                        setTimeout(() => {
                            updateRowCompletionStatus(rowIndex);
                            
                            // Auto-save table data to localStorage after editing
                            saveTableData(tableId);
                        }, 100);
                    });
                    
                    // Auto-add new row when tabbing from the last cell
                    $(`.editable[data-col='${TOTAL_COLUMNS-1}'][data-table="${tableId}"]`).off('keydown').on('keydown', function(e) {
                        if (e.key === "Tab" && !e.shiftKey) {
                            if (parseInt($(this).data('row')) === currentRowCount - 1) {
                                addNewRow();
                            }
                        }
                    });
                }
                
                // Initialize resizing functionality
                function initResizableColumns() {
                    $(`#headerRow-${tableId} .column-resizer`).off('mousedown').on('mousedown', function(e) {
                        isResizing = true;
                        currentResizer = $(this);
                        
                        const index = currentResizer.data('index');
                        const columnElement = currentResizer.closest('th');
                        
                        startX = e.pageX;
                        startWidth = columnWidths[index]; // Current column width
                        
                        $(document).on('mousemove', function(e) {
                            if (isResizing) {
                                // Calculate the new width based on mouse movement
                                const moveX = e.pageX - startX;
                                
                                // In RTL layout, we reverse the direction of resize
                                let newWidth = startWidth - moveX;
                                
                                // Set minimum width
                                if (newWidth >= 50) {
                                    columnWidths[index] = newWidth;
                                    updateColumnWidth(index, newWidth);
                                }
                            }
                        });
                        
                        $(document).off('mouseup').on('mouseup', function() {
                            isResizing = false;
                            currentResizer = null;
                            $(document).off('mousemove');
                            $(document).off('mouseup');
                        });
                        
                        e.preventDefault();
                    });
                }
                
                // Show scroll hint when table is wider than container
                function showScrollHintIfNeeded() {
                    const $container = $(`#${tableId}-form .excel-container`);
                    const $table = $(`#excelTable-${tableId}`);
                    
                    if ($table.width() > $container.width()) {
                        $(`#scrollHint-${tableId}`).css('opacity', 1);
                        setTimeout(() => {
                            $(`#scrollHint-${tableId}`).css('opacity', 0);
                        }, 3000);
                    }
                }
                
                // Update column width
                function updateColumnWidth(index, width) {
                    $(`#excelTable-${tableId} th`).eq(index).css('width', width + 'px');
                    $(`#excelTable-${tableId} td:nth-child(${index + 1})`).css('width', width + 'px');
                    
                    // Update the total table width
                    updateTableWidth();
                    
                    // Show scroll hint if needed
                    showScrollHintIfNeeded();
                }
                
                // Calculate and update the total table width
                function updateTableWidth() {
                    let totalWidth = 0;
                    columnWidths.forEach(width => {
                        totalWidth += width;
                    });
                    
                    // Add some extra width to ensure horizontal scrolling works properly
                    totalWidth += 10;
                    
                    // Set the table width
                    $(`#excelTable-${tableId}`).css('width', totalWidth + 'px');
                }
                
                // Mode Toggle Functionality
                $(`#editModeBtn-${tableId}`).click(function() {
                    $(this).addClass("active").removeClass("btn-outline-primary").addClass("btn-primary");
                    $(`#viewModeBtn-${tableId}`).removeClass("active").removeClass("btn-primary").addClass("btn-outline-primary");
                    $(`.editable[data-table="${tableId}"]`).attr("contenteditable", "true");
                    $(`#addRowBtn-${tableId}`).show();
                    
                    // Show all rows in edit mode
                    $(`#tableBody-${tableId} tr`).removeClass('view-mode');
                });
                
                $(`#viewModeBtn-${tableId}`).click(function() {
                    $(this).addClass("active").removeClass("btn-outline-primary").addClass("btn-primary");
                    $(`#editModeBtn-${tableId}`).removeClass("active").removeClass("btn-primary").addClass("btn-outline-primary");
                    $(`.editable[data-table="${tableId}"]`).attr("contenteditable", "false");
                    $(`#addRowBtn-${tableId}`).hide();
                    
                    // Hide empty rows in view mode
                    markEmptyRows();
                    $(`#tableBody-${tableId} tr`).addClass('view-mode');
                });
                
                // Add row button
                $(`#addRowBtn-${tableId}`).click(function() {
                    addNewRow();
                });
                
                // Clear all data
                $(`#clearData-${tableId}`).click(function() {
                    if (confirm('هل أنت متأكد من رغبتك في تفريغ الجدول؟')) {
                        $(`#tableBody-${tableId}`).empty();
                        currentRowCount = 0;
                        addInitialRows(5); // Add 5 empty rows after clearing
                        
                        // Reset column widths
                        columnWidths = Array(TOTAL_COLUMNS).fill(120);
                        for (let i = 0; i < TOTAL_COLUMNS; i++) {
                            updateColumnWidth(i, 120);
                        }
                        
                        // Mark all rows as empty
                        $(`#tableBody-${tableId} tr`).addClass('empty-row');
                        
                        localStorage.removeItem(`excelTableData-${tableId}`);
                        localStorage.removeItem(`excelTableMetadata-${tableId}`);
                    }
                });
                
                // Load data from localStorage on page load
                function loadSavedData() {
                    // Try to load from localStorage
                    const savedData = localStorage.getItem(`excelTableData-${tableId}`);
                    const savedMetadata = localStorage.getItem(`excelTableMetadata-${tableId}`);
                    
                    // Load metadata first if available
                    if (savedMetadata) {
                        const metadata = JSON.parse(savedMetadata);
                        if (metadata.columnWidths) {
                            columnWidths = metadata.columnWidths;
                        }
                        
                        // Reinitialize header with saved data
                        initializeHeaderRow();
                    }
                    
                    if (savedData) {
                        loadDataToTable(JSON.parse(savedData));
                    } else {
                        // No data, add initial rows
                        addInitialRows(5);
                    }
                }
                
                // Function to populate table with data
                function loadDataToTable(tableData) {
                    $(`#tableBody-${tableId}`).empty();
                    currentRowCount = 0;
                    
                    if (tableData.length === 0) {
                        addInitialRows(5);
                        return;
                    }
                    
                    // Populate the table with saved data
                    tableData.forEach((rowData) => {
                        let rowHtml = '<tr>';
                        let isEmpty = true;
                        
                        for (let j = 0; j < TOTAL_COLUMNS; j++) {
                            const cellContent = j < rowData.length ? rowData[j] : '';
                            
                            // Check for invalid data when loading (for existing data)
                            const isInvalid = (columnTypes[j] && cellContent.trim() !== '' && !validateData(cellContent, j));
                            const invalidClass = isInvalid ? 'invalid-data' : '';
                            
                            // Special handling for date column - add direction attribute
                            const dirAttribute = (j === 7 && tableId === 'employees') ? ' dir="ltr"' : '';
                            
                            rowHtml += `<td style="width: ${columnWidths[j]}px">
                                <div class="editable ${invalidClass}" contenteditable="true" data-row="${currentRowCount}" data-col="${j}" data-table="${tableId}"${dirAttribute}>${cellContent}</div>
                            </td>`;
                            
                            if (cellContent.trim() !== '') {
                                isEmpty = false;
                            }
                        }
                        
                        rowHtml += '</tr>';
                        const $row = $(rowHtml).appendTo(`#tableBody-${tableId}`);
                        
                        if (isEmpty) {
                            $row.addClass('empty-row');
                        }
                        
                        currentRowCount++;
                    });
                    
                    // Add a few empty rows at the end
                    addInitialRows(3);
                    
                    // Re-bind event handlers
                    bindCellEvents();
                    
                    // Ensure table width is updated
                    updateTableWidth();
                    
                    // Show scroll hint if needed
                    showScrollHintIfNeeded();
                    
                    // Apply current view mode if in view mode
                    if ($(`#viewModeBtn-${tableId}`).hasClass('active')) {
                        $(`#tableBody-${tableId} tr`).addClass('view-mode');
                    }
                    
                    // Update row status for all rows
                    markEmptyRows();
                }
                
                // Initialize the table
                initializeHeaderRow();
                initResizableColumns();
                updateTableWidth(); // Set initial table width
                loadSavedData();
                
                // If no data was loaded, add initial rows
                if (currentRowCount === 0) {
                    addInitialRows(5);
                }
                
                // Mark empty rows on initial load
                setTimeout(() => {
                    markEmptyRows();
                    // If in view mode, apply it to hide empty rows
                    if ($(`#viewModeBtn-${tableId}`).hasClass('active')) {
                        $(`#tableBody-${tableId} tr`).addClass('view-mode');
                    }
                }, 200);
                
                // Show scroll hint on window resize
                $(window).on('resize', function() {
                    showScrollHintIfNeeded();
                });
            }
            
            // Initialize the project selection dropdown with Select2
            $('#delegate-project-select').select2({
                theme: 'bootstrap-5',
                placeholder: 'اختر المشروع',
                width: '100%'
            });
            
            // Handle dark mode
            document.addEventListener('themeChanged', function(e) {
                const theme = document.body.getAttribute('data-theme');
                if (theme === 'dark') {
                    $('.select2-container--bootstrap-5 .select2-selection').css({
                        'background-color': '#2b3035',
                        'border-color': '#495057',
                        'color': '#e9ecef'
                    });
                } else {
                    $('.select2-container--bootstrap-5 .select2-selection').css({
                        'background-color': '',
                        'border-color': '',
                        'color': ''
                    });
                }
            });
            
            // Reset project error when selection changes
            $('#delegate-project-select').on('change', function() {
                if ($(this).val()) {
                    $('#project-error').hide();
                }
            });
        });
    </script>
</body>
</html> 
