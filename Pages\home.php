<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Add the database connection popup -->
    
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <!-- Main Content -->
    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <div class="dashboard-card text-center welcome-card">
                        <div class="welcome-icon mb-4">
                            <i class="bi bi-house-heart" style="font-size: 3rem; color: var(--primary-color);"></i>
                        </div>
                        <h1 class="mb-4">مرحباً بك في نظام إدارة الموارد البشرية</h1>
                        <p class="text-muted mb-4">نظام متكامل لإدارة شؤون الموظفين والموارد البشرية</p>
                        <div class="row justify-content-center mt-4">
                            <div class="col-md-4 mb-3">
                                <div class="quick-stat">
                                    <i class="bi bi-people"></i>
                                    <h3>إدارة الموظفين</h3>
                                    <p>إدارة كاملة لبيانات وملفات الموظفين</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="quick-stat">
                                    <i class="bi bi-calendar-check"></i>
                                    <h3>الحضور والانصراف</h3>
                                    <p>متابعة دقيقة لحضور وانصراف الموظفين</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="quick-stat">
                                    <i class="bi bi-cash-stack"></i>
                                    <h3>إدارة الرواتب</h3>
                                    <p>نظام متكامل لإدارة رواتب الموظفين</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-5">
                            
                            <a href="/Pages/xxx.php" class="btn btn-success btn-lg me-2" target="_blank">
                                <i class="bi bi-house-door me-2"></i>
                                النسخ الاحتياطية
                            </a>
                            <a href="/Pages/offer.php" class="btn btn-info btn-lg" target="_blank">
                                <i class="bi bi-file-earmark-text me-2"></i>
                                العروض
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    
    <!-- Database Connection Verification Script -->
    <script>
        // Database connection checking functionality
        const dbConnectionChecker = {
            popupId: 'db-connection-popup',
            checkInterval: 5000, // 5 seconds
            initialDelay: 5000, // 5 seconds initial delay

            init() {
                // Initial check with delay if PHP indicates no connection
                <?php if (!$isConnected): ?>
                setTimeout(() => {
                    this.showPopup();
                }, this.initialDelay);
                <?php endif; ?>

                // Start periodic checking after initial delay
                setTimeout(() => {
                    this.startPeriodicCheck();
                }, this.initialDelay);
            },

            checkConnection() {
                fetch('check_connection.php')
                    .then(response => response.json())
                    .then(data => {
                        if (!data.connected) {
                            this.showPopup();
                        } else {
                            this.hidePopup();
                        }
                    })
                    .catch(() => {
                        this.showPopup();
                    });
            },

            showPopup() {
                document.getElementById(this.popupId).style.display = 'flex';
            },

            hidePopup() {
                document.getElementById(this.popupId).style.display = 'none';
            },

            startPeriodicCheck() {
                setInterval(() => this.checkConnection(), this.checkInterval);
            }
        };

        // Initialize the connection checker
        document.addEventListener('DOMContentLoaded', () => {
            dbConnectionChecker.init();
        });
    </script>
</body>
</html>

