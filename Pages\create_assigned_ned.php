<?php
// بداية جلسة المستخدم وإعدادات عرض الأخطاء
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// معالجة طلب AJAX للحصول على مهام الوظيفة
if (isset($_GET['action']) && $_GET['action'] === 'get_job_tasks') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid job title ID');
        }

        $jobTitleId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // الحصول على بيانات المسمى الوظيفي
        $stmt = $conn->prepare("SELECT data_todo_list_Job FROM Job_titles WHERE id_Job_titles = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        $stmt->bind_param("i", $jobTitleId);
        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $todoList = json_decode($row['data_todo_list_Job'], true);
            $tasks = array_map(function($task) {
                return $task['taskName'];
            }, $todoList['jobDetails']['tasks']);
            echo json_encode($tasks);
        } else {
            echo json_encode([]);
        }

        $stmt->close();
        $conn->close();
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

// معالجة طلب AJAX للحصول على بيانات المنتدب
if (isset($_GET['action']) && $_GET['action'] === 'get_assigned_data') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid assigned employee ID');
        }

        $assignedId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // الحصول على بيانات المنتدب
        $stmt = $conn->prepare("SELECT * FROM assigned WHERE id_assigned = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        $stmt->bind_param("i", $assignedId);
        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            // تنظيف البيانات الثنائية من النتيجة
            $binaryFields = ['Identity_document', 'code_conduct'];
            
            foreach ($binaryFields as $field) {
                if (isset($row[$field]) && $row[$field] !== null) {
                    $row[$field] = true; // إعادة قيمة منطقية بدلاً من البيانات الثنائية
                } else {
                    $row[$field] = false;
                }
            }
            
            echo json_encode($row);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Assigned employee not found']);
        }

        $stmt->close();
        $conn->close();
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

// معالجة طلب AJAX للتحقق من وجود موظف مكرر
if (isset($_GET['action']) && $_GET['action'] === 'check_duplicate') {
    header('Content-Type: application/json');
    try {
        // التحقق من البيانات الواردة
        $checkType = $_GET['check_type'] ?? '';
        if (!in_array($checkType, ['name', 'identity'])) {
            throw new Exception('نوع الفحص غير صالح');
        }

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('خطأ في قراءة ملف الإعدادات');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
        }

        $isDuplicate = false;
        $message = '';

        // التحقق من تكرار الاسم
        if ($checkType === 'name') {
            $name_ar = $_GET['name_ar'] ?? '';
            $name_en = $_GET['name_en'] ?? '';
            
            if (empty($name_ar) && empty($name_en)) {
                throw new Exception('يجب توفير الاسم للتحقق');
            }
            
            $checkSql = "SELECT id_assigned FROM assigned WHERE (name_ar_assigned = ? OR name_en_assigned = ?) AND status = 1";
            $stmt = $conn->prepare($checkSql);
            $stmt->bind_param("ss", $name_ar, $name_en);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $isDuplicate = true;
                $message = 'يوجد منتدب بنفس الاسم مسجل في النظام بالفعل';
            }
        } 
        // التحقق من تكرار رقم الهوية
        else if ($checkType === 'identity') {
            $identity_number = $_GET['identity_number'] ?? '';
            $identity_type_ar = $_GET['identity_type_ar'] ?? '';
            
            if (empty($identity_number) || empty($identity_type_ar)) {
                throw new Exception('يجب توفير رقم ونوع الهوية للتحقق');
            }
            
            $checkSql = "SELECT id_assigned FROM assigned WHERE Identity_number_assigned = ? AND Identity_assigned_ar = ? AND status = 1";
            $stmt = $conn->prepare($checkSql);
            $stmt->bind_param("ss", $identity_number, $identity_type_ar);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $isDuplicate = true;
                if ($identity_type_ar === 'بطاقة شخصية') {
                    $message = 'يوجد منتدب برقم البطاقة الشخصية هذا مسجل في النظام بالفعل';
                } else if ($identity_type_ar === 'جواز سفر') {
                    $message = 'يوجد منتدب برقم جواز السفر هذا مسجل في النظام بالفعل';
                } else {
                    $message = 'يوجد منتدب برقم الهوية هذا مسجل في النظام بالفعل';
                }
            }
        }

        $stmt->close();
        $conn->close();
        
        echo json_encode([
            'isDuplicate' => $isDuplicate,
            'message' => $message
        ]);
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

$error_message = '';
$success_message = '';

// جلب المشاريع والمسميات الوظيفية من قاعدة البيانات
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    // جلب المشاريع
    $projects = [];
    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }

    // جلب قائمة المنتدبين
    $assigned_employees = [];
    $result = $conn->query("SELECT id_assigned, name_ar_assigned, name_en_assigned, Identity_number_assigned FROM assigned WHERE status = 1 ORDER BY name_ar_assigned");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $assigned_employees[] = $row;
        }
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $validation_errors = [];
        
        // معرف المنتدب للتحديث، إذا كان متوفراً
        $assigned_id = isset($_POST['assigned_id']) ? intval($_POST['assigned_id']) : 0;
        $is_update = $assigned_id > 0;
        
        // Get form data
        $id_Project = trim($_POST['id_Project'] ?? '');
        $name_ar_assigned = trim($_POST['name_ar_assigned'] ?? '');
        $name_en_assigned = trim($_POST['name_en_assigned'] ?? '');
        $address = trim($_POST['address'] ?? '');
        $phone_number = !empty($_POST['phone_number']) ? intval($_POST['phone_number']) : null;
        $Identity_assigned_ar = trim($_POST['Identity_assigned_ar'] ?? '');
        $Identity_assigned_en = trim($_POST['Identity_assigned_en'] ?? '');
        $Identity_number_assigned = trim($_POST['Identity_number_assigned'] ?? '');
        $Identity_issue_assigned_ar = trim($_POST['Identity_issue_assigned_ar'] ?? '');
        $Identity_issue_assigned_en = trim($_POST['Identity_issue_assigned_en'] ?? '');
        $Identity_issue_date_assigned = !empty($_POST['Identity_issue_date_assigned']) ? $_POST['Identity_issue_date_assigned'] : null;
        $polarization_method = trim($_POST['polarization_method'] ?? '');
        
        // Process file uploads
        $Identity_document = null;
        if (isset($_FILES['Identity_document']) && $_FILES['Identity_document']['error'] === UPLOAD_ERR_OK) {
            $Identity_document = file_get_contents($_FILES['Identity_document']['tmp_name']);
        }

        $code_conduct = null;
        if (isset($_FILES['code_conduct']) && $_FILES['code_conduct']['error'] === UPLOAD_ERR_OK) {
            $code_conduct = file_get_contents($_FILES['code_conduct']['tmp_name']);
        }
        
        // Validate required fields
        if (empty($id_Project)) {
            $validation_errors[] = 'اختيار المشروع مطلوب';
        }
        if (empty($name_ar_assigned)) {
            $validation_errors[] = 'الاسم العربي مطلوب';
        }
        if (empty($name_en_assigned)) {
            $validation_errors[] = 'الاسم الإنجليزي مطلوب';
        }
        if (empty($Identity_assigned_ar) || empty($Identity_assigned_en)) {
            $validation_errors[] = 'نوع الهوية مطلوب';
        }
        if (empty($Identity_number_assigned)) {
            $validation_errors[] = 'رقم الهوية مطلوب';
        }
        
        // Check for duplicate employee by name - تجاهل التحقق للمنتدب نفسه في حالة التحديث
        if (empty($validation_errors)) {
            $checkNameSql = "SELECT id_assigned FROM assigned WHERE 
                              (name_ar_assigned = ? OR name_en_assigned = ?) 
                              AND status = 1";
            
            if ($is_update) {
                $checkNameSql .= " AND id_assigned != ?";
            }
            
            $checkNameStmt = $conn->prepare($checkNameSql);
            
            if ($is_update) {
                $checkNameStmt->bind_param("ssi", $name_ar_assigned, $name_en_assigned, $assigned_id);
            } else {
                $checkNameStmt->bind_param("ss", $name_ar_assigned, $name_en_assigned);
            }
            
            $checkNameStmt->execute();
            $nameResult = $checkNameStmt->get_result();
            
            if ($nameResult->num_rows > 0) {
                $validation_errors[] = 'يوجد منتدب بنفس الاسم مسجل في النظام بالفعل';
            }
            $checkNameStmt->close();
            
            // If name validation passed, check for duplicate identity number
            if (empty($validation_errors)) {
                $checkIdentitySql = "SELECT id_assigned FROM assigned WHERE 
                                    Identity_number_assigned = ? AND 
                                    Identity_assigned_ar = ? AND 
                                    status = 1";
                
                if ($is_update) {
                    $checkIdentitySql .= " AND id_assigned != ?";
                }
                
                $checkIdentityStmt = $conn->prepare($checkIdentitySql);
                
                if ($is_update) {
                    $checkIdentityStmt->bind_param("ssi", $Identity_number_assigned, $Identity_assigned_ar, $assigned_id);
                } else {
                    $checkIdentityStmt->bind_param("ss", $Identity_number_assigned, $Identity_assigned_ar);
                }
                
                $checkIdentityStmt->execute();
                $identityResult = $checkIdentityStmt->get_result();
                
                if ($identityResult->num_rows > 0) {
                    if ($Identity_assigned_ar === 'بطاقة شخصية') {
                        $validation_errors[] = 'يوجد منتدب برقم البطاقة الشخصية هذا مسجل في النظام بالفعل';
                    } else if ($Identity_assigned_ar === 'جواز سفر') {
                        $validation_errors[] = 'يوجد منتدب برقم جواز السفر هذا مسجل في النظام بالفعل';
                    } else {
                        $validation_errors[] = 'يوجد منتدب برقم الهوية هذا مسجل في النظام بالفعل';
                    }
                }
                $checkIdentityStmt->close();
            }
        }
        
        // If validation passes, insert or update in database
        if (empty($validation_errors)) {
            try {
                if ($is_update) {
                    // تجهيز عملية التحديث
                    $updateFields = [
                        'id_Project = ?',
                        'name_ar_assigned = ?',
                        'name_en_assigned = ?',
                        'address = ?',
                        'phone_number = ?',
                        'Identity_assigned_ar = ?',
                        'Identity_assigned_en = ?',
                        'Identity_number_assigned = ?',
                        'Identity_issue_assigned_ar = ?',
                        'Identity_issue_assigned_en = ?',
                        'Identity_issue_date_assigned = ?',
                        'polarization_method = ?'
                    ];
                    
                    $params = [
                        $id_Project,
                        $name_ar_assigned,
                        $name_en_assigned,
                        $address,
                        $phone_number,
                        $Identity_assigned_ar,
                        $Identity_assigned_en,
                        $Identity_number_assigned,
                        $Identity_issue_assigned_ar,
                        $Identity_issue_assigned_en,
                        $Identity_issue_date_assigned,
                        $polarization_method
                    ];
                    
                    $types = "isssisssssss";
                    
                    // إضافة المستندات فقط إذا تم تحميلها
                    if ($Identity_document !== null) {
                        $updateFields[] = "Identity_document = ?";
                        $params[] = $Identity_document;
                        $types .= "b";
                    }
                    
                    if ($code_conduct !== null) {
                        $updateFields[] = "code_conduct = ?";
                        $params[] = $code_conduct;
                        $types .= "b";
                    }
                    
                    // إضافة معرف المنتدب للشرط
                    $params[] = $assigned_id;
                    $types .= "i";
                    
                    $sql = "UPDATE assigned SET " . implode(', ', $updateFields) . " WHERE id_assigned = ?";
                    
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                    }
                    
                    $stmt->bind_param($types, ...$params);
                    
                    if (!$stmt->execute()) {
                        throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
                    }
                    
                    $success_message = 'تم تحديث بيانات المنتدب بنجاح';
                } else {
                    // Prepare SQL statement for insert
                    $sql = "INSERT INTO assigned (
                        id_Project,
                        name_ar_assigned,
                        name_en_assigned,
                        address,
                        phone_number,
                        Identity_assigned_ar,
                        Identity_assigned_en,
                        Identity_number_assigned,
                        Identity_issue_assigned_ar,
                        Identity_issue_assigned_en,
                        Identity_issue_date_assigned,
                        Identity_document,
                        polarization_method,
                        code_conduct,
                        status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";
                
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                    }

                    // Bind parameters
                    $stmt->bind_param("issssssssssbsb",
                        $id_Project,
                        $name_ar_assigned,
                        $name_en_assigned,
                        $address,
                        $phone_number,
                        $Identity_assigned_ar,
                        $Identity_assigned_en,
                        $Identity_number_assigned,
                        $Identity_issue_assigned_ar,
                        $Identity_issue_assigned_en,
                        $Identity_issue_date_assigned,
                        $Identity_document,
                        $polarization_method,
                        $code_conduct
                    );

                    if (!$stmt->execute()) {
                        throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
                    }

                    $success_message = 'تمت إضافة المنتدب بنجاح';
                }
            } catch (Exception $e) {
                $error_message = "خطأ: " . $e->getMessage();
            } finally {
                if (isset($stmt)) {
                    $stmt->close();
                }
            }
        } else {
            $error_message = "أخطاء التحقق:<br>" . implode("<br>", $validation_errors);
        }
    }
} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء منتدب جديد - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Alert styling */
        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .section-title {
            color: #0d6efd;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Form controls */
        .form-control, .form-select {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            color: var(--text-color);
        }

        .form-control:disabled {
            background-color: var(--hover-color);
            color: var(--text-muted);
        }

        /* Helper text styling */
        .text-muted {
            color: #6c757d !important;
        }

        [data-theme="dark"] .text-muted {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        /* Primary color overrides */
        .text-primary {
            color: #0d6efd !important;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }

        /* Save button styling */
        .save-contract-btn {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 200px;
        }

        .save-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .save-contract-btn:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Select2 styling */
        .select2-container--default .select2-selection--single {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
            height: 38px;
            line-height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            color: var(--text-color);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-dropdown {
            background-color: var(--bg-input);
            border-color: var(--input-border);
        }

        .select2-container--default .select2-results__option {
            color: var(--text-color);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color);
        }

        /* Task list styling */
        .task-list-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: var(--bg-input);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .task-list-scrollable {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .task-list-scrollable::-webkit-scrollbar {
            width: 8px;
        }

        .task-list-scrollable::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        .task-item {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-item:last-child {
            margin-bottom: 0;
        }

        .task-input-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex: 1;
        }

        .task-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        /* Delete button styling */
        .btn-danger.btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }

        .btn-danger.btn-sm:hover {
            opacity: 0.9;
        }

        .text-end {
            text-align: end;
            margin-top: -0.5rem; /* Adjust spacing */
        }

        /* Card styling */
        .card {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .card-title {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--primary-color);
        }

        /* Button styling */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--btn-text);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-danger {
            color: var(--btn-text);
        }

        /* Duration helper text */
        .duration-helper {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        /* JSON section styling */
        .btn-secondary {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: var(--hover-color);
            border-color: var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary .bi-chevron-down {
            transition: transform 0.3s ease;
        }

        .btn-secondary[aria-expanded="true"] .bi-chevron-down {
            transform: rotate(180deg);
        }

        #jsonSection {
            transition: all 0.3s ease;
        }

        #jsonSection.collapsing {
            transition: all 0.3s ease;
        }

        #jsonSection.show {
            margin-top: 1rem;
        }

        #json_output {
            background-color: var(--bg-input);
            color: var(--text-color);
            font-family: monospace;
            font-size: 0.875rem;
            resize: vertical;
        }

        /* Identity attachment styling */
        .identity-attachment-container {
            transition: all 0.3s ease;
        }

        .identity-attachment-container:hover {
            background-color: var(--bg-input) !important;
        }

        .border-dashed {
            border-style: dashed !important;
        }

        .upload-box {
            transition: all 0.3s ease;
        }

        .upload-box:hover {
            border-color: var(--primary-color) !important;
            background-color: rgba(var(--primary-rgb), 0.05);
        }

        /* Dark theme support */
        [data-theme="dark"] .identity-attachment-container {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        [data-theme="dark"] .upload-box:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .selected-file {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* Dark theme support for form controls */
        [data-theme="dark"] .form-control,
        [data-theme="dark"] .form-select,
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        /* Dark theme support for Select2 dropdown */
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection--single {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--dark-text-color, #e9ecef);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__option {
            background-color: var(--dark-input-bg, #2b3035);
            color: var(--dark-text-color, #e9ecef);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color, #0d6efd);
            color: #ffffff;
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__option[aria-selected="true"] {
            background-color: var(--primary-color, #0d6efd);
            color: #ffffff;
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        /* Dark theme support for custom inputs */
        [data-theme="dark"] #custom_issue_place,
        [data-theme="dark"] #custom_polarization_method {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        /* Dark theme support for input focus states */
        [data-theme="dark"] .form-control:focus,
        [data-theme="dark"] .form-select:focus,
        [data-theme="dark"] .select2-container--bootstrap-5.select2-container--focus .select2-selection,
        [data-theme="dark"] .select2-container--bootstrap-5.select2-container--open .select2-selection {
            border-color: var(--primary-color, #0d6efd);
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            background-color: var(--dark-input-bg, #2b3035);
        }

        /* Dark theme support for disabled states */
        [data-theme="dark"] .form-control:disabled,
        [data-theme="dark"] .form-control[readonly],
        [data-theme="dark"] .form-select:disabled {
            background-color: var(--dark-disabled-bg, #343a40);
            color: var(--dark-text-muted, #6c757d);
        }

        /* Dark theme support for placeholder text */
        [data-theme="dark"] .form-control::placeholder,
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection--single::placeholder {
            color: var(--dark-text-muted, #6c757d);
        }

        /* Custom file input styling */
        .custom-file-input-wrapper {
            position: relative;
            width: 100%;
        }

        .custom-file-input-wrapper input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        .custom-file-input-trigger {
            display: flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: var(--text-color);
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            cursor: pointer;
        }

        .custom-file-input-trigger i {
            margin-left: 0.5rem;
        }

        .custom-file-input-trigger .file-name {
            margin-right: auto;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Dark theme support for file input */
        [data-theme="dark"] .custom-file-input-trigger {
            background-color: var(--dark-input-bg, #2b3035);
            border-color: var(--dark-border-color, #495057);
            color: var(--dark-text-color, #e9ecef);
        }

        [data-theme="dark"] .custom-file-input-trigger:hover {
            background-color: var(--dark-hover-bg, #343a40);
        }

        /* Invalid field styling */
        .is-invalid {
            border-color: #dc3545 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.875em;
            margin-top: 0.25rem;
        }

        /* Dark theme support for invalid fields */
        [data-theme="dark"] .is-invalid {
            border-color: #dc3545 !important;
            background-color: var(--dark-input-bg) !important;
        }

        [data-theme="dark"] .invalid-feedback {
            color: #ff6b6b;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <!-- Excel Table Link -->
                    <div class="text-end mb-4">
                        <a href="Excel1/excel_table.php" class="btn btn-info">
                            <i class="bi bi-table"></i> إضافة كشف مندوبين
                        </a>
                    </div>
                    
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card">
                        <div class="card-body">
                            <h1 class="mb-4">إضافة منتدب جديد</h1>
                            
                            <div class="alert alert-info mb-4" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                قم بإدخال بيانات المنتدب الجديد. الحقول المميزة بعلامة (*) إلزامية.
                            </div>
                            
                            <!-- Assigned Employee Selection Section -->
                            <div class="section-container mb-4" id="assigned-selection-section">
                                <h6 class="section-title">تحديث بيانات منتدب موجود</h6>
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="assigned_selector" class="form-label">اختر المنتدب</label>
                                        <select class="form-control select2" id="assigned_selector">
                                            <option value="">اختر منتدباً للتحديث</option>
                                            <?php foreach ($assigned_employees as $assigned): ?>
                                            <option value="<?php echo $assigned['id_assigned']; ?>">
                                                <?php echo $assigned['name_ar_assigned'] . ' (' . $assigned['Identity_number_assigned'] . ')'; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3 d-flex align-items-end">
                                        <button type="button" id="viewAssignedBtn" class="btn btn-info me-2" disabled>
                                            <i class="bi bi-eye-fill"></i> عرض البيانات
                                        </button>
                                        <button type="button" id="resetFormBtn" class="btn btn-secondary">
                                            <i class="bi bi-arrow-repeat"></i> إضافة جديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <form id="delegateForm" method="post" enctype="multipart/form-data">
                                <!-- Hidden field for assigned employee ID when updating -->
                                <input type="hidden" id="assigned_id" name="assigned_id" value="0">
                                
                                <!-- Add Project Selection -->
                                <div class="section-container">
                                    <h6 class="section-title">معلومات المشروع</h6>
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="id_Project" class="form-label">المشروع *</label>
                                            <select class="form-control select2" id="id_Project" name="id_Project" required>
                                                <option value="">اختر المشروع</option>
                                                <?php foreach ($projects as $project): ?>
                                                    <option value="<?php echo htmlspecialchars($project['id_Project']); ?>">
                                                        <?php echo htmlspecialchars($project['Project_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Update name fields -->
                                <div class="section-container">
                                    <h6 class="section-title">البيانات الشخصية</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name_ar_assigned" class="form-label">الاسم العربي *</label>
                                            <input type="text" class="form-control" id="name_ar_assigned" name="name_ar_assigned" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="name_en_assigned" class="form-label">الاسم الإنجليزي *</label>
                                            <input type="text" class="form-control" id="name_en_assigned" name="name_en_assigned" dir="ltr" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="address" class="form-label">العنوان</label>
                                            <input type="text" class="form-control" id="address" name="address">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone_number" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone_number" name="phone_number" pattern="[0-9]*" inputmode="numeric" oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                                        </div>
                                    </div>
                                </div>

                                <!-- Update identity fields -->
                                <div class="section-container">
                                    <h6 class="section-title">معلومات الهوية</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="identity_type" class="form-label">نوع الهوية *</label>
                                            <select class="form-control select2" id="identity_type" onchange="updateIdentityType()">
                                                <option value="">اختر نوع الهوية</option>
                                                <option value="Passport|جواز سفر">جواز سفر</option>
                                                <option value="National ID|بطاقة شخصية">بطاقة شخصية</option>
                                            </select>
                                            <input type="text" class="form-control mt-2" id="Identity_type_en" 
                                                   placeholder="ID Type in English" dir="ltr" readonly>
                                            <input type="hidden" id="Identity_assigned_ar" name="Identity_assigned_ar" required>
                                            <input type="hidden" id="Identity_assigned_en" name="Identity_assigned_en" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="Identity_number_assigned" class="form-label">رقم الهوية *</label>
                                            <input type="text" class="form-control" id="Identity_number_assigned" name="Identity_number_assigned" required pattern="[0-9]*" inputmode="numeric" oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="identity_issue_place" class="form-label">مكان الإصدار</label>
                                            <select class="form-control select2" id="identity_issue_place" onchange="handleIssuePlaceChange()">
                                                <option value="">اختر المحافظة</option>
                                                <option value="Sana'a|صنعاء">صنعاء</option>
                                                <option value="Aden|عدن">عدن</option>
                                                <option value="Taiz|تعز">تعز</option>
                                                <option value="Hodeidah|الحديدة">الحديدة</option>
                                                <option value="Ibb|إب">إب</option>
                                                <option value="Dhamar|ذمار">ذمار</option>
                                                <option value="Hadramaut|حضرموت">حضرموت</option>
                                                <option value="Al-Bayda|البيضاء">البيضاء</option>
                                                <option value="Hajjah|حجة">حجة</option>
                                                <option value="Sa'ada|صعدة">صعدة</option>
                                                <option value="Lahij|لحج">لحج</option>
                                                <option value="Marib|مأرب">مأرب</option>
                                                <option value="Al-Mahwit|المحويت">المحويت</option>
                                                <option value="Al-Jawf|الجوف">الجوف</option>
                                                <option value="Amran|عمران">عمران</option>
                                                <option value="Al-Dhale|الضالع">الضالع</option>
                                                <option value="Raymah|ريمة">ريمة</option>
                                                <option value="Socotra|سقطرى">سقطرى</option>
                                                <option value="custom">أخرى - إدخال يدوي</option>
                                            </select>
                                            <input type="text" class="form-control mt-2" id="custom_issue_place" 
                                                   style="display: none;" placeholder="أدخل مكان الإصدار"
                                                   onchange="handleCustomPlaceInput()" onkeyup="handleCustomPlaceInput()">
                                            <input type="text" class="form-control mt-2" id="Identity_issue_place_en" 
                                                   placeholder="Place of Issue in English" dir="ltr">
                                            <input type="hidden" id="Identity_issue_assigned_ar" name="Identity_issue_assigned_ar">
                                            <input type="hidden" id="Identity_issue_assigned_en" name="Identity_issue_assigned_en">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="Identity_issue_date_assigned" class="form-label">تاريخ الإصدار</label>
                                            <input type="date" class="form-control" id="Identity_issue_date_assigned" name="Identity_issue_date_assigned">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="Identity_document" class="form-label">صورة الهوية</label>
                                            <div class="custom-file-input-wrapper">
                                                <input type="file" class="custom-file-input" id="Identity_document" name="Identity_document" accept=".pdf,.jpg,.jpeg,.png">
                                                <div class="custom-file-input-trigger">
                                                    <i class="bi bi-upload"></i>
                                                    <span class="default-text">اختر ملفاً</span>
                                                    <span class="file-name"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Place Polarization and Documents sections side by side -->
                                <div class="row">
                                    <!-- Polarization Information Section -->
                                    <div class="col-md-6">
                                        <div class="section-container h-100">
                                            <h6 class="section-title">معلومات الاستقطاب</h6>
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label for="polarization_method_select" class="form-label fw-bold mb-2">طريقة الاستقطاب</label>
                                                    <select class="form-select select2-search" id="polarization_method_select" name="polarization_method_select" onchange="handlePolarizationMethodChange()">
                                                        <option value="">اختر طريقة الاستقطاب</option>
                                                        <option value="Direct Recruitment|توظيف مباشر">توظيف مباشر</option>
                                                        <option value="Employment Agency|وكالة توظيف">وكالة توظيف</option>
                                                        <option value="Employee Referral|ترشيح موظف">ترشيح موظف</option>
                                                        <option value="Online Platform|منصة إلكترونية">منصة إلكترونية</option>
                                                        <option value="custom">أخرى - إدخال يدوي</option>
                                                    </select>
                                                    <input type="text" class="form-control mt-2" id="custom_polarization_method" 
                                                           style="display: none;" placeholder="أدخل طريقة الاستقطاب"
                                                           onchange="updatePolarizationMethod()" onkeyup="updatePolarizationMethod()">
                                                    <input type="hidden" id="polarization_method" name="polarization_method">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Documents Section -->
                                    <div class="col-md-6">
                                        <div class="section-container h-100">
                                            <h6 class="section-title">المستندات</h6>
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label for="code_conduct" class="form-label fw-bold mb-2">مدونة السلوك</label>
                                                    <div class="custom-file-input-wrapper">
                                                        <input type="file" class="custom-file-input" id="code_conduct" name="code_conduct" 
                                                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif">
                                                        <div class="custom-file-input-trigger">
                                                            <i class="bi bi-upload"></i>
                                                            <span class="default-text">اختر ملفاً</span>
                                                            <span class="file-name"></span>
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">
                                                        الملفات المسموح بها: PDF, Word, JPG, PNG, GIF
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="text-center mt-4">
                                    <button type="submit" id="saveBtn" class="btn btn-primary btn-lg save-contract-btn">
                                        <i class="bi bi-person-plus-fill me-2"></i>
                                        إضافة المنتدب
                                    </button>
                                    <button type="submit" id="updateBtn" class="btn btn-success btn-lg save-contract-btn" style="display:none;">
                                        <i class="bi bi-pencil-square me-2"></i>
                                        تحديث البيانات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize all Select2 dropdowns
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
            
            // Custom file input handling
            $('.custom-file-input').on('change', function(e) {
                const fileName = e.target.files[0]?.name;
                const wrapper = $(this).closest('.custom-file-input-wrapper');
                const trigger = wrapper.find('.custom-file-input-trigger');
                const defaultText = trigger.find('.default-text');
                const fileNameSpan = trigger.find('.file-name');
                
                if (fileName) {
                    defaultText.hide();
                    fileNameSpan.text(fileName).show();
                } else {
                    defaultText.show();
                    fileNameSpan.text('').hide();
                }
            });

            // Initialize file inputs
            $('.custom-file-input').each(function() {
                const wrapper = $(this).closest('.custom-file-input-wrapper');
                const trigger = wrapper.find('.custom-file-input-trigger');
                const fileNameSpan = trigger.find('.file-name');
                fileNameSpan.hide();
            });

            // Initialize polarization method value
            handlePolarizationMethodChange();

            // Listen for theme changes
            document.addEventListener('themeChanged', function(e) {
                updateSelect2Theme();
            });

            // Initialize place of issuance handlers
            handleIssuePlaceChange();
            
            // Handle English place of issuance input
            $('#Identity_issue_place_en').on('input', function() {
                document.getElementById('Identity_issue_assigned_en').value = this.value;
            });

            // Force numeric input for phone number and identity number fields
            const numericInputs = ['phone_number', 'Identity_number_assigned'];
            numericInputs.forEach(fieldId => {
                document.getElementById(fieldId).addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
            });

            // Enable View button when assigned employee is selected
            $('#assigned_selector').on('change', function() {
                if ($(this).val()) {
                    $('#viewAssignedBtn').prop('disabled', false);
                } else {
                    $('#viewAssignedBtn').prop('disabled', true);
                }
            });
            
            // View Assigned Employee button click
            $('#viewAssignedBtn').on('click', function() {
                const assignedId = $('#assigned_selector').val();
                if (!assignedId) return;
                
                // Show loading indicator
                $(this).html('<i class="bi bi-hourglass-split"></i> جاري التحميل...');
                $(this).prop('disabled', true);
                
                // Fetch assigned employee data
                $.ajax({
                    url: 'create_assigned_ned.php',
                    data: {
                        action: 'get_assigned_data',
                        id: assignedId
                    },
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        console.log("Received data:", data); // Debug: Log received data
                        
                        // Populate form fields
                        $('#assigned_id').val(data.id_assigned);
                        $('#id_Project').val(data.id_Project).trigger('change');
                        $('#name_ar_assigned').val(data.name_ar_assigned);
                        $('#name_en_assigned').val(data.name_en_assigned);
                        $('#address').val(data.address);
                        $('#phone_number').val(data.phone_number);
                        
                        // Set identity type
                        if (data.Identity_assigned_en && data.Identity_assigned_ar) {
                            let identityValue = `${data.Identity_assigned_en}|${data.Identity_assigned_ar}`;
                            // Check if this is a standard option or we need to match it
                            let foundIdentity = false;
                            $('#identity_type option').each(function() {
                                const optValue = $(this).val();
                                if (optValue && optValue.split('|')[1] === data.Identity_assigned_ar) {
                                    $('#identity_type').val(optValue).trigger('change');
                                    foundIdentity = true;
                                    return false;
                                }
                            });
                            
                            // If not found, select first option and manually set hidden fields
                            if (!foundIdentity && data.Identity_assigned_ar) {
                                $('#identity_type').val($('#identity_type option:eq(1)').val()).trigger('change');
                                setTimeout(function() {
                                    $('#Identity_assigned_ar').val(data.Identity_assigned_ar);
                                    $('#Identity_assigned_en').val(data.Identity_assigned_en);
                                    $('#Identity_type_en').val(data.Identity_assigned_en);
                                }, 100);
                            }
                        }
                        
                        $('#Identity_number_assigned').val(data.Identity_number_assigned);
                        
                        // Set place of issuance
                        if (data.Identity_issue_assigned_ar || data.Identity_issue_assigned_en) {
                            // First check if it's one of our predefined options
                            let foundPlace = false;
                            $('#identity_issue_place option').each(function() {
                                let optionValue = $(this).val();
                                if (optionValue && optionValue !== 'custom') {
                                    let [en, ar] = optionValue.split('|');
                                    if (ar === data.Identity_issue_assigned_ar) {
                                        $('#identity_issue_place').val(optionValue).trigger('change');
                                        foundPlace = true;
                                        return false; // break the loop
                                    }
                                }
                            });
                            
                            // If not found in options, use custom
                            if (!foundPlace && data.Identity_issue_assigned_ar) {
                                $('#identity_issue_place').val('custom').trigger('change');
                                $('#custom_issue_place').val(data.Identity_issue_assigned_ar);
                                $('#Identity_issue_place_en').val(data.Identity_issue_assigned_en);
                                $('#Identity_issue_assigned_ar').val(data.Identity_issue_assigned_ar);
                                $('#Identity_issue_assigned_en').val(data.Identity_issue_assigned_en);
                                // Make sure the custom field is visible
                                $('#custom_issue_place').show();
                            }
                        }
                        
                        $('#Identity_issue_date_assigned').val(data.Identity_issue_date_assigned);
                        
                        // Set polarization method
                        if (data.polarization_method) {
                            let foundMethod = false;
                            $('#polarization_method_select option').each(function() {
                                const optValue = $(this).val();
                                if (optValue && optValue !== 'custom') {
                                    const [en, ar] = optValue.split('|');
                                    if (ar === data.polarization_method) {
                                        $('#polarization_method_select').val(optValue).trigger('change');
                                        foundMethod = true;
                                        return false;
                                    }
                                }
                            });
                            
                            if (!foundMethod) {
                                $('#polarization_method_select').val('custom').trigger('change');
                                $('#custom_polarization_method').val(data.polarization_method);
                                $('#polarization_method').val(data.polarization_method);
                                // Make sure the custom field is visible
                                $('#custom_polarization_method').show();
                            }
                        }
                        
                        // Update file indicators
                        updateFileIndicator('Identity_document', data.Identity_document);
                        updateFileIndicator('code_conduct', data.code_conduct);
                        
                        // Update form UI
                        $('#saveBtn').hide();
                        $('#updateBtn').show();
                        $('h1.mb-4').text('تحديث بيانات المنتدب');
                        
                        // Reset view button
                        $('#viewAssignedBtn').html('<i class="bi bi-eye-fill"></i> عرض البيانات');
                        $('#viewAssignedBtn').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        // Show error
                        console.error("AJAX error:", xhr, status, error); // Debug: Log the error
                        let errorMsg = 'حدث خطأ أثناء استرجاع بيانات المنتدب';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMsg += ': ' + xhr.responseJSON.error;
                        } else if (error) {
                            errorMsg += ': ' + error;
                        }
                        
                        alert(errorMsg);
                        
                        // Reset view button
                        $('#viewAssignedBtn').html('<i class="bi bi-eye-fill"></i> عرض البيانات');
                        $('#viewAssignedBtn').prop('disabled', false);
                    }
                });
            });
            
            // Reset Form button click
            $('#resetFormBtn').on('click', function() {
                resetForm();
            });
            
            // Helper function to update file indicators
            function updateFileIndicator(fieldId, hasFile) {
                const fileInput = $(`#${fieldId}`);
                const wrapper = fileInput.closest('.custom-file-input-wrapper');
                const trigger = wrapper.find('.custom-file-input-trigger');
                const defaultText = trigger.find('.default-text');
                const fileNameSpan = trigger.find('.file-name');
                
                if (hasFile) {
                    defaultText.hide();
                    fileNameSpan.text('ملف موجود').show();
                    // Add indicator that file exists
                    trigger.addClass('border-success');
                } else {
                    defaultText.show();
                    fileNameSpan.text('').hide();
                    trigger.removeClass('border-success');
                }
            }
            
            // Helper function to reset form
            function resetForm() {
                // Reset assigned ID
                $('#assigned_id').val('0');
                
                // Reset form fields
                document.getElementById('delegateForm').reset();
                
                // Reset Select2 dropdowns
                $('#id_Project').val('').trigger('change');
                $('#identity_type').val('').trigger('change');
                $('#identity_issue_place').val('').trigger('change');
                $('#polarization_method_select').val('').trigger('change');
                $('#assigned_selector').val('').trigger('change');
                
                // Reset file inputs
                $('.custom-file-input-trigger').removeClass('border-success');
                $('.custom-file-input-wrapper .default-text').show();
                $('.custom-file-input-wrapper .file-name').hide();
                
                // Reset button visibility
                $('#saveBtn').show();
                $('#updateBtn').hide();
                
                // Reset form title
                $('h1.mb-4').text('إضافة منتدب جديد');
                
                // Clear any validation errors or warnings
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').remove();
            }
        });

        function updateIdentityType() {
            const selectedValue = document.getElementById('identity_type').value;
            if (!selectedValue) return;

            const [en, ar] = selectedValue.split('|');
            
            document.getElementById('Identity_type_en').value = en;
            document.getElementById('Identity_assigned_ar').value = ar;
            document.getElementById('Identity_assigned_en').value = en;
        }

        function handleIssuePlaceChange() {
            const selectedPlace = document.getElementById('identity_issue_place').value;
            const customPlaceInput = document.getElementById('custom_issue_place');
            
            if (selectedPlace === 'custom') {
                customPlaceInput.style.display = 'block';
                customPlaceInput.focus();
                // Clear the English field
                document.getElementById('Identity_issue_place_en').value = '';
                document.getElementById('Identity_issue_assigned_ar').value = '';
                document.getElementById('Identity_issue_assigned_en').value = '';
            } else if (selectedPlace) {
                customPlaceInput.style.display = 'none';
                
                const [en, ar] = selectedPlace.split('|');
                document.getElementById('Identity_issue_place_en').value = en;
                document.getElementById('Identity_issue_assigned_ar').value = ar;
                document.getElementById('Identity_issue_assigned_en').value = en;
            } else {
                customPlaceInput.style.display = 'none';
                // Clear both fields
                document.getElementById('Identity_issue_place_en').value = '';
                document.getElementById('Identity_issue_assigned_ar').value = '';
                document.getElementById('Identity_issue_assigned_en').value = '';
            }
        }

        function handleCustomPlaceInput() {
            const customValue = document.getElementById('custom_issue_place').value;
            document.getElementById('Identity_issue_assigned_ar').value = customValue;
        }

        function handlePolarizationMethodChange() {
            const selectedMethod = document.getElementById('polarization_method_select').value;
            const customMethodInput = document.getElementById('custom_polarization_method');
            
            if (selectedMethod === 'custom') {
                customMethodInput.style.display = 'block';
                customMethodInput.focus();
                document.getElementById('polarization_method').value = '';
            } else if (selectedMethod) {
                customMethodInput.style.display = 'none';
                
                const [en, ar] = selectedMethod.split('|');
                document.getElementById('polarization_method').value = ar;
            } else {
                customMethodInput.style.display = 'none';
                document.getElementById('polarization_method').value = '';
            }
        }

        function updatePolarizationMethod() {
            const customValue = document.getElementById('custom_polarization_method').value;
            document.getElementById('polarization_method').value = customValue;
        }

        function updateSelect2Theme() {
            // Update Select2 theme based on current theme
            const theme = document.body.getAttribute('data-theme');
            $('.select2-container--bootstrap-5').each(function() {
                $(this).removeClass('select2-container--light select2-container--dark');
                $(this).addClass(`select2-container--${theme}`);
            });
        }
    </script>
</body>
</html>

