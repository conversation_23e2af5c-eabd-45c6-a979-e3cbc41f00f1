<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Fetch all projects for the search dropdown
    $projects = [];
    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }

    // Fetch all job titles for the search dropdown
    $job_titles = [];
    $result = $conn->query("SELECT DISTINCT c.name_Job FROM contract c ORDER BY c.name_Job");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $job_titles[] = $row['name_Job'];
        }
    }

    $contract_data = null;
    $error_message = '';
    $success_message = '';

    // Add this function near the top of the file after the database connection
    function handleFileUpload($employeeId, $fieldName) {
        global $conn;
        
        if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'لم يتم اختيار ملف أو حدث خطأ أثناء الرفع'];
        }

        // Check file type
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        $fileType = $_FILES[$fieldName]['type'];
        
        if (!in_array($fileType, $allowedTypes)) {
            return ['success' => false, 'message' => 'نوع الملف غير مسموح به. يرجى اختيار ملف PDF أو صورة'];
        }

        // Check file size (5MB max)
        if ($_FILES[$fieldName]['size'] > 5 * 1024 * 1024) {
            return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى هو 5 ميجابايت'];
        }

        try {
            $fileContent = file_get_contents($_FILES[$fieldName]['tmp_name']);
            
            // Update the query to use proper SQL syntax
            $sql = "UPDATE employees SET `$fieldName` = ? WHERE id_employees = ?";
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Error preparing statement: " . $conn->error);
            }
            
            $null = null;
            $stmt->bind_param("bi", $fileContent, $employeeId);
            $result = $stmt->execute();
            
            if (!$result) {
                throw new Exception("Error executing statement: " . $stmt->error);
            }
            
            $stmt->close();
            
            // Refresh the contract data after upload
            $refresh_stmt = $conn->prepare("SELECT * FROM employees WHERE id_employees = ?");
            $refresh_stmt->bind_param("i", $employeeId);
            $refresh_stmt->execute();
            $result = $refresh_stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                global $contract_data;
                $contract_data = $row;
            }
            $refresh_stmt->close();
            
            return ['success' => true, 'message' => 'تم رفع الملف بنجاح'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء معالجة لملف: ' . $e->getMessage()];
        }
    }

    // Update file upload handling
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_file'])) {
        $employeeId = $_POST['employee_id'];
        $fieldName = $_POST['field_name'];
        
        $result = handleFileUpload($employeeId, $fieldName);
        if ($result['success']) {
            $success_message = $result['message'];
            
            // Refresh the contract data
            $refresh_stmt = $conn->prepare("SELECT * FROM employees WHERE id_employees = ?");
            $refresh_stmt->bind_param("i", $employeeId);
            $refresh_stmt->execute();
            $result = $refresh_stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $contract_data = $row;
            }
            $refresh_stmt->close();
        } else {
            $error_message = $result['message'];
        }
    }

    // Add file download handling
    if (isset($_GET['download']) && isset($_GET['employee_id']) && isset($_GET['field'])) {
        $employeeId = $_GET['employee_id'];
        $fieldName = $_GET['field'];
        
        // Wrap the column name in backticks
        $stmt = $conn->prepare("SELECT `$fieldName` FROM employees WHERE id_employees = ?");
        $stmt->bind_param("i", $employeeId);
        $stmt->execute();
        $stmt->store_result();
        $stmt->bind_result($fileContent);
        
        if ($stmt->fetch() && $fileContent) {
            // Detect file type from the first few bytes
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->buffer($fileContent);
            
            switch ($mimeType) {
                case 'application/pdf':
                    $extension = '.pdf';
                    break;
                case 'image/jpeg':
                    $extension = '.jpg';
                    break;
                case 'image/png':
                    $extension = '.png';
                    break;
                default:
                    $extension = '';
            }

            // Set appropriate headers
            header('Content-Type: ' . $mimeType);
            header('Content-Disposition: attachment; filename="' . $fieldName . '_' . $employeeId . $extension . '"');
            header('Content-Length: ' . strlen($fileContent));
            header('Cache-Control: private, must-revalidate, max-age=0');
            header('Pragma: public');
            
            echo $fileContent;
            exit;
        }
        
        $stmt->close();
        $error_message = "الملف غير موجود";
    }

    // Add this near the download handler
    if (isset($_GET['preview']) && isset($_GET['employee_id']) && isset($_GET['field'])) {
        $employeeId = $_GET['employee_id'];
        $fieldName = $_GET['field'];
        
        // Wrap the column name in backticks
        $stmt = $conn->prepare("SELECT `$fieldName` FROM employees WHERE id_employees = ?");
        $stmt->bind_param("i", $employeeId);
        $stmt->execute();
        $stmt->store_result();
        $stmt->bind_result($fileContent);
        
        if ($stmt->fetch() && $fileContent) {
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->buffer($fileContent);
            
            header('Content-Type: ' . $mimeType);
            echo $fileContent;
            exit;
        }
        
        $stmt->close();
        echo "الملف غير موجود";
        exit;
    }

    // Handle search request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search'])) {
        // Verify that project is selected (required)
        if (!isset($_POST['project_id']) || empty($_POST['project_id'])) {
            $error_message = 'يرجى اختيار المشروع قبل البحث';
        } else {
            $conditions = [];
            $params = [];
            $types = '';

            // Base query with joins to get executor details
            $sql = "SELECT t.*, p.Project_name, 
                    e.name_ar_contract as employee_name_ar, e.name_en_contract as employee_name_en,
                    e.phone_number as employee_phone, e.Identity_number_contract as employee_identity,
                    a.name_ar_assigned as delegate_name_ar, a.name_en_assigned as delegate_name_en,
                    a.phone_number as delegate_phone, a.Identity_number_assigned as delegate_identity
                    FROM tasks t 
                    LEFT JOIN project p ON t.id_Project = p.id_Project 
                    LEFT JOIN employees e ON t.id_employees = e.id_employees
                    LEFT JOIN assigned a ON t.id_assigned = a.id_assigned 
                    WHERE 1=1";

            // Add executor type condition - MODIFIED to allow both types
            if (isset($_POST['executor_type']) && $_POST['executor_type'] !== '') {
                if ($_POST['executor_type'] === 'employee') {
                    $conditions[] = "t.id_employees IS NOT NULL AND t.id_assigned IS NULL";
                } elseif ($_POST['executor_type'] === 'delegate') {
                    $conditions[] = "t.id_assigned IS NOT NULL AND t.id_employees IS NULL";
                }
                // If executor_type is empty, we don't add any condition - show both
            }

            // Add project filter condition (required)
            if (isset($_POST['project_id']) && !empty($_POST['project_id'])) {
                $conditions[] = "t.id_Project = ?";
                $params[] = intval($_POST['project_id']);
                $types .= 'i';
            }

            // Search by task name or task number
            if (!empty($_POST['name_ar_contract'])) {
                $searchTerm = '%' . $_POST['name_ar_contract'] . '%';
                // Check if the search term is numeric (potential task number)
                if (is_numeric($_POST['name_ar_contract'])) {
                    $conditions[] = "(t.name_TASKS LIKE ? OR t.name_TASKS_en LIKE ? OR t.id_TASKS = ?)";
                    $params = array_merge($params, [$searchTerm, $searchTerm, intval($_POST['name_ar_contract'])]);
                    $types .= 'ssi';
                } else {
                    $conditions[] = "(t.name_TASKS LIKE ? OR t.name_TASKS_en LIKE ?)";
                    $params = array_merge($params, [$searchTerm, $searchTerm]);
                    $types .= 'ss';
                }
            }

            // Add conditions to query
            if (!empty($conditions)) {
                $sql .= " AND " . implode(" AND ", $conditions);
            }

            // Add ORDER BY clause
            $sql .= " ORDER BY t.id_TASKS DESC";

            // Prepare and execute the query
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Query preparation failed: " . $conn->error);
            }

            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }

            if (!$stmt->execute()) {
                throw new Exception("Query execution failed: " . $stmt->error);
            }

            $result = $stmt->get_result();
            $search_results = [];
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    // Decode JSON data_todo_list_TASKS
                    $row['data_todo_list_TASKS'] = json_decode($row['data_todo_list_TASKS'], true);
                    $search_results[] = $row;
                }
                $success_message = 'تم العثور على ' . count($search_results) . ' مهمة';
            } else {
                $error_message = 'لم يتم العثور على أي مهام تطابق معايير البحث';
            }

            $stmt->close();

            // If a specific task is selected
            if (isset($_POST['selected_task'])) {
                $task_id = (int) $_POST['selected_task'];
                
                $sql = "SELECT t.*, p.Project_name,
                        e.name_ar_contract, e.name_en_contract, e.phone_number as employee_phone, e.Identity_number_contract,
                        a.name_ar_assigned, a.name_en_assigned, a.phone_number as delegate_phone, a.Identity_number_assigned
                        FROM tasks t 
                        LEFT JOIN project p ON t.id_Project = p.id_Project 
                        LEFT JOIN employees e ON t.id_employees = e.id_employees
                        LEFT JOIN assigned a ON t.id_assigned = a.id_assigned
                        WHERE t.id_TASKS = ?";
                
                // Add project filter if provided
                $taskParams = [$task_id];
                $taskTypes = "i";
                
                if (isset($_POST['project_id']) && !empty($_POST['project_id'])) {
                    $sql .= " AND t.id_Project = ?";
                    $taskParams[] = intval($_POST['project_id']);
                    $taskTypes .= "i";
                }
                
                $stmt = $conn->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Query preparation failed: " . $conn->error);
                }
                
                $stmt->bind_param($taskTypes, ...$taskParams);
                
                if (!$stmt->execute()) {
                    throw new Exception("Query execution failed: " . $stmt->error);
                }

                $result = $stmt->get_result();
                if ($result && $result->num_rows > 0) {
                    $task_data = $result->fetch_assoc();
                    // Decode JSON data_todo_list_TASKS
                    $task_data['data_todo_list_TASKS'] = json_decode($task_data['data_todo_list_TASKS'], true);
                    $success_message = 'تم العثور على بيانات المهمة';
                } else {
                    $error_message = 'لم يتم العثور على بيانات المهمة';
                }
                $stmt->close();
            }
        }
    }

    // Fetch attendance records (permanent diapers)
    $attendance_records = [];
    $attendance_sql = "SELECT id_permanent_diapers, 
                             start_date_permanent_diapers, 
                             end_date_permanent_diapers,
                             add_permanent_diapers 
                      FROM permanent_diapers 
                      WHERE id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $attendance_sql .= " AND id_extension_contract = ?";
        $stmt = $conn->prepare($attendance_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $attendance_sql .= " AND (id_extension_contract IS NULL OR id_extension_contract = 0)";
        $stmt = $conn->prepare($attendance_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $attendance_records[] = $row;
    }

    // Fetch achievement reports
    $achievement_reports = [];
    $achievement_sql = "SELECT ar.id_achievement_reports,
                              ar.start_date_achievement_reports,
                              ar.end_date_achievement_reports,
                              ar.actual_working_days,
                              ar.add_achievement_reports,
                              pd.id_permanent_diapers
                       FROM achievement_reports ar
                       JOIN permanent_diapers pd ON ar.id_permanent_diapers = pd.id_permanent_diapers
                       WHERE ar.id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $achievement_sql .= " AND ar.id_extension_contract = ?";
        $stmt = $conn->prepare($achievement_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $achievement_sql .= " AND (ar.id_extension_contract IS NULL OR ar.id_extension_contract = 0)";
        $stmt = $conn->prepare($achievement_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        // Calculate working days if actual_working_days is 0
        if ($row['actual_working_days'] == 0) {
            $start = new DateTime($row['start_date_achievement_reports']);
            $end = new DateTime($row['end_date_achievement_reports']);
            $interval = $start->diff($end);
            $row['actual_working_days'] = $interval->days + 1;
        }
        $achievement_reports[] = $row;
    }

    // Fetch merit reports
    $merit_reports = [];
    $merit_sql = "SELECT mr.id_merit_reports,
                         mr.actual_working_days,
                         mr.today_wage,
                         mr.total,
                         ar.id_achievement_reports,
                         ar.start_date_achievement_reports,
                         ar.end_date_achievement_reports
                  FROM merit_reports mr
                  JOIN achievement_reports ar ON mr.id_achievement_reports = ar.id_achievement_reports
                  WHERE mr.id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $merit_sql .= " AND mr.id_extension_contract = ?";
        $stmt = $conn->prepare($merit_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $merit_sql .= " AND (mr.id_extension_contract IS NULL OR mr.id_extension_contract = 0)";
        $stmt = $conn->prepare($merit_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $merit_reports[] = $row;
    }

    // Fetch extended contracts if this is a main contract
    $extended_contracts = [];
    if (isset($_POST['selected_contract']) && strpos($_POST['selected_contract'], 'contract_') === 0) {
        $contract_id = (int) str_replace('contract_', '', $_POST['selected_contract']);
        
        $ext_sql = "SELECT ec.*, 
                           DATE_FORMAT(ec.version_date, '%d-%m-%Y') as formatted_version_date,
                           DATE_FORMAT(ec.start_date_contract, '%d-%m-%Y') as formatted_start_date,
                           COALESCE(DATE_FORMAT(ec.end_date_contract, '%d-%m-%Y'), 'مفتوح') as formatted_end_date
                    FROM extension_contract ec 
                    WHERE ec.id_contract = ? 
                    ORDER BY ec.version_date DESC";
        
        $stmt = $conn->prepare($ext_sql);
        $stmt->bind_param("i", $contract_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $extended_contracts[] = $row;
        }
        $stmt->close();
    }

} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

function formatDate($date) {
    if (empty($date)) return "غير محدد";
    return date("Y/m/d", strtotime($date));
}

function getContractTypeName($type) {
    switch ($type) {
        case 1: return "راتب شهري";
        case 2: return "أجر يومي";
        default: return "غير محدد";
    }
}

// First, add this helper function near the other helper functions at the top of the file
function getCurrencyTypeName($type) {
    switch ($type) {
        case 1: return "دولار";
        case 2: return "ريال";
        default: return "غير محدد";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير عقد العمل - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Updated styles for better dark theme compatibility */
        .search-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 2px 15px var(--shadow-color);
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filters {
            padding: 1rem;
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filter-item {
            min-width: 150px;
            position: relative;
        }

        .quick-filter-item .form-select,
        .quick-filter-item .select2-container .select2-selection--single {
            border-radius: 20px;
            padding: 0.4rem 2rem 0.4rem 1rem;
            border: 1px solid var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .quick-filter-item .form-select:hover,
        .quick-filter-item .select2-container .select2-selection--single:hover {
            border-color: var(--primary-color);
        }

        .quick-filter-item .form-select:focus,
        .quick-filter-item .select2-container .select2-selection--single:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .advanced-filters-container {
            padding: 1.5rem;
            background: var(--bg-main);
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .filter-group {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            box-shadow: 0 1px 3px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .filter-group-title {
            color: var(--text-color);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-group-title i {
            color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .form-control {
            background-color: var(--bg-input);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .form-control:focus {
            background-color: var(--bg-input);
            border-color: var(--primary-color);
            color: var(--text-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
            border-radius: 20px;
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Results styles with dark theme support */
        .results-container {
            margin-top: 1.5rem;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .results-header {
            position: sticky;
            top: 0;
            background: var(--bg-main);
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            z-index: 1;
            transition: all 0.3s ease;
        }

        .results-search input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-input);
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .results-search input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .result-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-card);
        }

        .result-item:hover {
            background: var(--hover-color);
        }

        .result-item-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .result-item-info {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .select-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Select2 Dark Theme Adjustments */
        .select2-container--bootstrap-5 .select2-selection {
            background-color: var(--bg-input) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--bg-card) !important;
            border-color: var(--border-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            color: var(--text-color) !important;
            background-color: var(--bg-card) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--hover-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: var(--primary-color) !important;
            color: white !important;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .search-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .quick-filters {
            background: var(--bg-card);
        }

        [data-theme="dark"] .advanced-filters-container {
            background: var(--bg-main);
        }

        [data-theme="dark"] .filter-group {
            background: var(--bg-card);
        }

        [data-theme="dark"] input[type="date"] {
            color-scheme: dark;
        }

        /* Scrollbar styling */
        .results-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .results-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .quick-filter-item {
                min-width: 120px;
            }
            
            .filter-group {
                margin-bottom: 1rem;
            }
            
            .advanced-filters-container {
                padding: 1rem;
            }
            
            .filter-actions {
                justify-content: center;
            }
        }

        /* Add these styles for the Contract Details section */
        .report-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 2px 15px var(--shadow-color);
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            color: var(--text-color);
            margin: 0;
            font-weight: 600;
        }

        .report-header .btn-print {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .report-header .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .report-section {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1.25rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            background: var(--bg-card);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        .info-value.text-primary {
            color: var(--primary-color) !important;
        }

        .info-value.text-success {
            color: var(--success-color) !important;
        }

        .badge {
            padding: 0.5em 0.75em;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .badge-primary {
            background: var(--primary-color);
            color: white;
        }

        .badge-success {
            background: var(--success-color);
            color: white;
        }

        .task-list {
            display: grid;
            gap: 1rem;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .task-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .task-name-en {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .task-progress {
            width: 100px;
            background: var(--bg-main);
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-value {
            height: 6px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .progress-label {
            color: var(--text-muted);
            font-size: 0.75rem;
            text-align: center;
            margin-top: 0.25rem;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .report-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .report-section {
            background: var(--bg-main);
        }

        [data-theme="dark"] .info-item {
            background: var(--bg-card);
        }

        [data-theme="dark"] .task-item {
            background: var(--bg-card);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .report-container {
                margin: 0;
                padding: 0;
                box-shadow: none;
            }

            .report-section {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }

        .extension-item {
            padding-right: 2rem !important;
            position: relative;
            font-size: 0.95em;
            background-color: var(--bg-secondary) !important;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: 1rem;
            top: 50%;
            width: 0.5rem;
            height: 1px;
            background-color: var(--text-muted);
        }

        .result-item-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .result-item:not(.extension-item) {
            margin-top: 0.5rem;
        }

        .result-item:not(.extension-item) + .extension-item {
            margin-top: 0.25rem;
        }

        .tasks-container {
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.5rem;
        }

        .task-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-color);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-size: 0.95rem;
            line-height: 1.4;
        }

        /* Scrollbar styling */
        .tasks-container::-webkit-scrollbar {
            width: 6px;
        }

        .tasks-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Add these styles to your existing style section */
        .scrollable-section {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--bg-main);
        }

        .scrollable-section::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-section::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .scrollable-section::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .search-box {
            margin-left: 1rem;
        }

        .list-group-item {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            background-color: var(--hover-color);
        }

        .list-group-item h6 {
            color: var(--text-color);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .list-group-item small {
            font-size: 0.8rem;
        }

        /* Add these new styles */
        .inner-section {
            background: var(--bg-main);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .inner-section .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.35em 0.65em;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        /* Add these new styles */
        .stats-pills {
            display: flex;
            gap: 1rem;
        }

        .stat-pill {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.35rem 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 0.875rem;
        }

        .stat-pill i {
            color: var(--primary-color);
        }

        .stat-value {
            font-weight: 600;
            color: var(--text-color);
        }

        .stat-label {
            color: var(--text-muted);
        }

        .nav-tabs-custom {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background: var(--bg-main);
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .tab-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: var(--text-muted);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .tab-btn:hover {
            color: var(--text-color);
            background: var(--hover-color);
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .tab-content-wrapper {
            background: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .tab-content {
            display: none;
            height: 400px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .search-box {
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .search-input {
            padding-left: 2.5rem;
            background: var(--bg-main);
        }

        .scrollable-section {
            height: calc(100% - 65px);
            overflow-y: auto;
            padding: 1rem;
        }

        .list-item {
            padding: 1rem;
            border-radius: 8px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
        }

        .item-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .item-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 8px;
            flex-shrink: 0;
        }

        .item-details {
            flex: 1;
        }

        .item-title {
            color: var(--text-color);
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .item-meta {
            color: var(--text-muted);
            font-size: 0.8rem;
            display: flex;
            gap: 1rem;
        }

        .item-meta i {
            margin-right: 0.25rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
        }

        .btn-icon:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        /* Add to the existing style section */
        .extension-list-item {
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .extension-list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .extension-list-item .item-icon {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .extension-list-item .item-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .extension-list-item .btn-icon:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Add to your existing styles */
        .result-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .result-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-left: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        .extension-item {
            margin-left: 2rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            left: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .result-item-details {
            flex: 1;
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            border-radius: 4px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .select-contract-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.25em 0.5em;
            border-radius: 4px;
        }

        /* Update these styles in your existing style section */

        .extension-item {
            margin-right: 2rem;
            margin-bottom: 0.5rem;
            border-right: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-right: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        /* Add new style for the arrow icon */
        .extension-item .bi-arrow-return-right {
            transform: scaleX(-1);  /* Flip the arrow icon horizontally */
        }

        /* Optional: Add a connecting line from bottom extension to top extension */
        .extension-item::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -2rem;
            width: 2px;
            height: calc(-100% - 0.5rem);
            background-color: var(--border-color);
            z-index: 0;
        }

        /* Remove the vertical line from the first extension */
        .extension-item:first-child::after {
            display: none;
        }

        /* Add hover effect for the connection lines */
        .extension-item:hover::before,
        .extension-item:hover::after {
            background-color: var(--primary-color);
        }

        /* Add these styles to make the modal content more readable */
        .modal-xl {
            max-width: 90%; /* Makes modal wider */
        }
        
        .modal-body {
            padding: 2rem;
        }
        
        .report-details .section {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .info-item {
            background: var(--bg-main);
            padding: 1rem;
            border-radius: 6px;
            height: 100%;
        }
        
        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        /* Modal Styling */
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Modal Header Styling */
        .modal-header {
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header .modal-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            color: var(--text-color);
            margin: 0;
            order: 1; /* Changed from 2 to 1 */
        }

        .modal-header .modal-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .modal-header .btn-close {
            order: 2; /* Changed from 1 to 2 */
            margin: 0;
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-main);
            border-radius: 8px;
            opacity: 1;
            transition: all 0.2s ease;
            position: relative;
        }

        /* Custom close button design */
        .modal-header .btn-close::before,
        .modal-header .btn-close::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 2px;
            background-color: var(--text-color);
            border-radius: 1px;
            transition: all 0.2s ease;
        }

        .modal-header .btn-close::before {
            transform: rotate(45deg);
        }

        .modal-header .btn-close::after {
            transform: rotate(-45deg);
        }

        .modal-header .btn-close:hover {
            background-color: var(--hover-color);
            transform: rotate(90deg);
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .modal-header .btn-close {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] .modal-header .btn-close::before,
        [data-theme="dark"] .modal-header .btn-close::after {
            background-color: var(--text-muted);
        }

        [data-theme="dark"] .modal-header .btn-close:hover {
            background-color: var(--primary-color);
        }

        [data-theme="dark"] .modal-header .btn-close:hover::before,
        [data-theme="dark"] .modal-header .btn-close:hover::after {
            background-color: white;
        }

        .modal-body {
            padding: 2rem;
            background: var(--bg-main);
        }

        /* Report Details Styling */
        .report-details {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .report-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .report-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-header i {
            color: var(--primary-color);
            font-size: 1.25rem;
            background: var(--primary-light);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .section-header h6 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--text-color);
        }

        /* Info Grid Styling */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            background: var(--hover-color);
            border-color: var(--primary-color);
        }

        .info-card .label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-card .value {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 500;
        }

        /* Task List Styling */
        .task-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .task-item {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.75rem;
        }

        .progress-wrapper {
            background: var(--bg-card);
            border-radius: 6px;
            padding: 0.75rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-muted);
            font-size: 0.75rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 48px;
            height: 48px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Achievement Timeline Report Table Styles */
        .achievement-schedule-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 0;
            border: 1px solid var(--border-color);
        }

        .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            white-space: nowrap;
            position: relative;
        }

        .achievement-schedule-table tbody td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
            transition: all 0.2s ease;
        }

        .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Task name column specific styling */
        .achievement-schedule-table td:first-child {
            text-align: right;
            font-weight: 500;
            color: var(--primary-color);
        }

        /* Percentage/Days values styling */
        .achievement-schedule-table td:not(:first-child):not(:last-child) {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Notes column styling */
        .achievement-schedule-table td:last-child {
            color: var(--text-muted);
            font-style: italic;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .achievement-schedule-table {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border-bottom-color: var(--primary-color);
        }

        [data-theme="dark"] .achievement-schedule-table tbody td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Responsive table container */
        .achievement-schedule-container {
            width: 100%;
            overflow-x: auto;
            border-radius: 8px;
            position: relative;
        }

        /* Custom scrollbar styling */
        .achievement-schedule-container::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .achievement-schedule-container::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Value highlighting */
        .value-percentage {
            color: var(--success-color);
            background: var(--success-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        .value-days {
            color: var(--info-color);
            background: var(--info-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .achievement-schedule-table thead th,
            .achievement-schedule-table tbody td {
                padding: 0.75rem;
                font-size: 0.9rem;
            }
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            margin-bottom: 0.25rem;
        }

        .status-badge:empty::after {
            content: "-";
            color: var(--text-muted);
        }

        .status-badge.present {
            background-color: var(--success-bg);
            color: var(--success-color);
        }

        .time-display {
            font-size: 0.875rem;
            color: var(--text-primary);
            text-align: center;
        }

        .time-display:empty::after {
            content: "00:00";
            color: var(--text-muted);
        }

        .table th {
            text-align: center;
            vertical-align: middle;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            white-space: nowrap;
        }

        .table td {
            text-align: center;
            vertical-align: middle;
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        /* Attendance Tables Styling */
        .attendance-summary-table,
        .attendance-details-table {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
        }

        .attendance-summary-table th,
        .attendance-details-table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            border-color: var(--border-color);
            text-align: center;
            white-space: nowrap;
        }

        .attendance-summary-table td,
        .attendance-details-table td {
            background-color: var(--bg-card);
            color: var(--text-primary);
            border-color: var(--border-color);
            padding: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .attendance-details-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Status Badge Styling */
        .status-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.35rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            min-width: 80px;
        }

        .status-badge.present {
            background-color: var(--success-light);
            color: var(--success-color);
        }

        .status-badge.absent {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }

        .status-badge.leave {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }

        /* Time Display Styling */
        .time-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .time-value {
            font-family: monospace;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        /* Dark Theme Specific Adjustments */
        [data-theme="dark"] .attendance-summary-table,
        [data-theme="dark"] .attendance-details-table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] .attendance-summary-table th,
        [data-theme="dark"] .attendance-details-table th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .attendance-summary-table td,
        [data-theme="dark"] .attendance-details-table td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.absent {
            background-color: rgba(var(--danger-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.leave {
            background-color: rgba(var(--warning-rgb), 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .attendance-summary-table,
            .attendance-details-table {
                font-size: 0.875rem;
            }

            .attendance-summary-table th,
            .attendance-details-table th,
            .attendance-summary-table td,
            .attendance-details-table td {
                padding: 0.625rem;
            }

            .status-badge {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                min-width: 60px;
            }
        }

        /* Add these styles to the existing style section */
        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .report-header .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .report-header .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .report-header .btn i {
            font-size: 1rem;
        }

        /* Add to the existing style section */
        .document-card {
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
        }

        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .document-card.has-file {
            border-color: var(--success-color);
        }

        .document-card.no-file {
            border-style: dashed;
        }

        .document-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .document-header i {
            font-size: 1.25rem;
            color: var(--primary-color);
            background: var(--primary-light);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .document-header h6 {
            margin: 0;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .document-status {
            margin-bottom: 1rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.35rem 0.75rem;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .status-badge.success {
            background: var(--success-light);
            color: var(--success-color);
        }

        .status-badge.warning {
            background: var(--warning-light);
            color: var(--warning-color);
        }

        .document-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .file-input-wrapper {
            position: relative;
            margin-bottom: 0.75rem;
        }

        .file-input-wrapper input[type="file"] {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .file-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--bg-card);
            border: 1px dashed var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            color: var(--text-muted);
            transition: all 0.2s ease;
        }

        .file-label:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .upload-btn, .download-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.5rem;
            font-size: 0.875rem;
        }

        .upload-btn {
            background: var(--primary-color);
            border: none;
        }

        .download-btn {
            background: var(--secondary-color);
            border: none;
        }

        .upload-btn:hover, .download-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn-group {
            display: flex;
            gap: 0.5rem;
        }

        .preview-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .download-btn {
            flex: 1;
        }

        /* Custom Styles */
        /* Select2 RTL Fixes with Theme Support */
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px !important;
            display: flex !important;
            align-items: center !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            transition: all 0.3s ease !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            width: 100% !important;
            padding-right: 8px !important;
            padding-left: 20px !important;
            display: block !important;
            position: static !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            color: var(--select-text) !important;
        }

        /* Placeholder color */
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
            color: var(--select-placeholder) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            position: absolute !important;
            left: 3px !important;
            right: auto !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
            border-color: var(--select-text) transparent transparent transparent !important;
        }

        .select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow b {
            border-color: transparent transparent var(--select-text) transparent !important;
        }

        /* Dropdown styles */
        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--select-bg) !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            text-align: right !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            padding: 6px 12px !important;
            text-align: right !important;
            color: var(--select-text) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
            background-color: rgba(var(--primary-rgb), 0.1);
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
        
        /* Enhanced select field styling */
        .form-select.select2-search {
            height: 38px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .form-select.select2-search:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.15);
            outline: none;
        }
        
        .form-select.select2-search:hover {
            border-color: #bdbdbd;
        }

        /* Set CSS variables for light/dark themes */
        :root {
            --select-bg: #fff;
            --select-text: #495057;
            --select-border: #ced4da;
            --select-placeholder: #999;
            --select-hover-bg: #f8f9fa;
            --select-focus-border: #86b7fe;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
            --primary-color: #0d6efd;
            --primary-rgb: 13, 110, 253;
            --text-light: #fff;
        }
        
        /* Existing styles */
        .form-label.fw-bold {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            border-radius: 8px;
            height: calc(2.5rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding-right: 0;
            color: var(--text-color);
            line-height: 1.5;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: 38px;
        }
        
        .select2-dropdown-rtl {
            text-align: right;
        }
        
        .select2-dropdown {
            border-color: var(--border-color);
            background-color: var(--bg-card);
        }
        
        .select2-results__option {
            padding: 0.5rem 0.75rem;
            transition: background-color 0.15s ease-in-out;
        }
        
        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--primary-color);
            color: white;
        }
        
        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
    </style>

    <!-- CSS Styles to add for the detail-item UI pattern -->
    <style>
        .detail-item {
            padding: 0.75rem;
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
            display: block;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
            font-size: 1rem;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Main Container -->
                    <div class="search-container">
                        <!-- Quick Filters -->
                        <div class="quick-filters">
                            <form method="post" id="searchForm" class="mb-0">
                                <div class="d-flex flex-column gap-3">
                                    <!-- Task Executor Type Selection -->
                                    <div class="filter-group mb-3">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-person-badge"></i> نوع منفذ المهمة
                                        </h6>
                                        <div class="d-flex gap-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="executor_type" 
                                                       id="both_type" value="" checked>
                                                <label class="form-check-label" for="both_type">
                                                    الكل
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="executor_type" 
                                                       id="employee_type" value="employee">
                                                <label class="form-check-label" for="employee_type">
                                                    موظف
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="executor_type" 
                                                       id="delegate_type" value="delegate">
                                                <label class="form-check-label" for="delegate_type">
                                                    مندوب
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Add Project Selection Field -->
                                    <div class="filter-group mb-3">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-building"></i> المشروع
                                        </h6>
                                        <div class="form-group">
                                            <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                            <select class="form-select select2-search" id="project_id" name="project_id" required>
                                                <option value="">اختر المشروع</option>
                                                <?php foreach ($projects as $project): ?>
                                                    <option value="<?= $project['id_Project'] ?>" <?= isset($_POST['project_id']) && $_POST['project_id'] == $project['id_Project'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($project['Project_name']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Search Input -->
                                    <div class="filter-group mb-0">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-search"></i> بحث عن مهمة
                                        </h6>
                                        <div class="d-flex gap-2 flex-wrap">
                                            <div class="flex-grow-1">
                                                <input type="text" class="form-control form-control-sm" 
                                                       id="name_ar_contract" 
                                                       name="name_ar_contract" 
                                                       placeholder="البحث (اسم المهمة أو رقم المهمة)">
                                            </div>
                                            <div>
                                                <button type="submit" name="search" class="btn btn-primary btn-sm">
                                                    <i class="bi bi-search"></i> بحث
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Results Section -->
                        <?php if (isset($search_results) && !empty($search_results)): ?>
                        <div class="results-container">
                            <div class="results-header">
                                <div class="results-search">
                                    <input type="text" id="resultSearch" placeholder="البحث في النتائج..." class="form-control">
                                </div>
                            </div>
                            <div class="results-list">
                                <?php foreach ($search_results as $task): ?>
                                    <div class="result-item" data-search-text="<?= htmlspecialchars($task['name_TASKS']) ?> <?= htmlspecialchars($task['id_TASKS']) ?>">
                                        <div class="result-item-details">
                                            <div class="result-item-name">
                                                <i class="bi bi-list-task text-primary me-2"></i>
                                                <span class="badge bg-secondary me-1">مهمة #<?= htmlspecialchars($task['id_TASKS']) ?></span>
                                                <?= htmlspecialchars($task['name_TASKS']) ?>
                                            </div>
                                            <div class="result-item-info small text-muted mt-1">
                                                <span class="me-3">
                                                    <i class="bi bi-person"></i>
                                                    <?php
                                                    if (!empty($task['id_employees'])) {
                                                        echo htmlspecialchars($task['employee_name_ar']);
                                                    } elseif (!empty($task['id_assigned'])) {
                                                        echo htmlspecialchars($task['delegate_name_ar']);
                                                    } else {
                                                        echo "غير محدد";
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?= !empty($task['id_employees']) ? 'primary' : 'info' ?> ms-1">
                                                        <?= !empty($task['id_employees']) ? 'موظف' : 'مندوب' ?>
                                                    </span>
                                                </span>
                                                <span class="me-3">
                                                    <i class="bi bi-calendar-event"></i>
                                                    <?= date('Y-m-d', strtotime($task['start_date_TASKS'])) ?>
                                                </span>
                                                <span>
                                                    <i class="bi bi-calendar-check"></i>
                                                    <?= date('Y-m-d', strtotime($task['end_date_TASKS'])) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <form method="post" style="display: inline;">
                                            <input type="hidden" name="search" value="1">
                                            <input type="hidden" name="executor_type" value="<?= $_POST['executor_type'] ?>">
                                            <input type="hidden" name="selected_task" value="<?= $task['id_TASKS'] ?>">
                                            <input type="hidden" name="project_id" value="<?= $_POST['project_id'] ?>">
                                            <button type="submit" class="select-contract-btn">
                                                <i class="bi bi-eye me-1"></i>عرض
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if (isset($_POST['selected_task']) && $task_data): ?>
                    <div class="row">
                        <!-- Task Details - Full Width -->
                        <div class="col-12">
                            <!-- Report Container -->
                            <div class="report-container">
                                <div class="report-header">
                                    <h5 class="card-title mb-4">تفاصيل المهمة</h5>
                                </div>

                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">رقم المهمة</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['id_TASKS']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">اسم المهمة بالعربية</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['name_TASKS']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">تاريخ البدء</label>
                                            <span class="detail-value">
                                                <?php echo date('Y-m-d', strtotime($task_data['start_date_TASKS'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">تاريخ الانتهاء</label>
                                            <span class="detail-value">
                                                <?php echo date('Y-m-d', strtotime($task_data['end_date_TASKS'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">الأجر</label>
                                            <span class="detail-value">
                                                <?php echo number_format($task_data['wage_TASKS'], 2) . ' ' . 
                                                    ($task_data['Type_currency'] == 1 ? 'دولار' : 'ريال'); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">مكان العمل</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['workplace']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">المشروع المرتبط</label>
                                            <span class="detail-value">
                                                <?php 
                                                    if (!empty($task_data['id_Project'])) {
                                                        echo htmlspecialchars($task_data['id_Project']);
                                                    } else {
                                                        echo 'غير محدد';
                                                    }
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php if (!empty($task_data['note'])): ?>
                                    <div class="col-md-12">
                                        <div class="detail-item">
                                            <label class="detail-label">ملاحظات</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['note']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php elseif (isset($search_results) && !empty($search_results)): ?>
                    <div class="text-center mt-4 text-muted">
                        <i class="bi bi-arrow-up-circle fs-1"></i>
                        <p class="mt-2">الرجاء اختيار مهمة من القائمة أعلاه لعرض التفاصيل</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for all select elements
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                language: {
                    noResults: function() {
                        return "لا توجد نتائج";
                    }
                }
            });

            // Search within results functionality
            $('#resultSearch').on('input', function() {
                const searchText = $(this).val().toLowerCase();
                $('.result-item').each(function() {
                    const itemText = $(this).data('search-text').toLowerCase();
                    $(this).toggle(itemText.includes(searchText));
                });
            });

            // Auto-scroll to contract details when a contract is selected
            <?php if (isset($_POST['selected_task']) && $contract_data): ?>
                $('html, body').animate({
                    scrollTop: $('.report-container').offset().top - 20
                }, 300);
            <?php endif; ?>

            // Initialize Select2 for project dropdown
            $('#project_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر المشروع',
                allowClear: true
            });

            // Form submission validation
            $('#searchForm').on('submit', function(e) {
                if (!$('#project_id').val()) {
                    e.preventDefault();
                    alert('يرجى اختيار المشروع قبل البحث');
                    return false;
                }
            });
        });
    </script>
</body>
</html>
