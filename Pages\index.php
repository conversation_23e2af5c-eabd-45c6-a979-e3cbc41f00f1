<?php
session_start();
header('Content-Type: text/html; charset=UTF-8');
// Security configurations
define('TEMPLATE_DIR', __DIR__ . '/templates/');
ini_set('display_errors', 0);
error_reporting(0);

// Check if we have a restored template in the session
$restoredTemplate = null;
$updateTemplateId = null;
if (isset($_GET['restore']) && isset($_SESSION['restored_template'])) {
    $restoredTemplate = $_SESSION['restored_template'];
    // Set update template ID if provided
    if (isset($_GET['update']) && is_numeric($_GET['update'])) {
        $updateTemplateId = (int)$_GET['update'];
    }
    // Clear the session data after retrieving it
    unset($_SESSION['restored_template']);
}

// Handle template saving
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // ---------------------------
        // Handle deletion of a template record
        // ---------------------------
        if (isset($_POST['delete'])) {
            $templateName = basename($_POST['file']);
            
            // Connect to the database (using connection details similar to create_project.php)
            $file = fopen(__DIR__ . "/connection/one.txt", "r");
            if (!$file) {
                throw new Exception('خطأ في قراءة ملف الإعدادات');
            }
            $servername = trim(fgets($file));
            $username   = trim(fgets($file));
            $password   = trim(fgets($file));
            $dbname     = trim(fgets($file));
            fclose($file);
            
            $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
            $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if the template exists
            $stmt = $db->prepare("SELECT id_template_contract FROM template_contract WHERE name_template_contract = :name");
            $stmt->execute([':name' => $templateName]);
            if ($stmt->fetch(PDO::FETCH_ASSOC)) {
                $deleteStmt = $db->prepare("DELETE FROM template_contract WHERE name_template_contract = :name");
                $deleteStmt->execute([':name' => $templateName]);
                echo json_encode(['status' => 'success', 'message' => '✅ تم حذف القالب بنجاح!']);
            } else {
                throw new Exception('الملف غير موجود');
            }
            exit;
        }

        // ---------------------------
        // Handle saving a new template directly to the database
        // ---------------------------
        $data = json_decode(file_get_contents("php://input"), true);
        if (!isset($data['template']) || !is_string($data['template'])) {
            throw new Exception('Invalid template data');
        }
        
        if (!isset($data['type']) || !in_array($data['type'], ['1', '2'])) {
            throw new Exception('نوع العقد غير صحيح');
        }
        
        // Connect to the database
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('خطأ في قراءة ملف الإعدادات');
        }
        $servername = trim(fgets($file));
        $username   = trim(fgets($file));
        $password   = trim(fgets($file));
        $dbname     = trim(fgets($file));
        fclose($file);
        
        $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Process the template content to handle dynamic variables
        $templateContent = $data['template'];
        
        // Extract all variable placeholders from the template
        preg_match_all('/\(name_Job_en\)/', $templateContent, $matches);
        
        if (!empty($matches[0])) {
            // Add a comment to indicate this template uses dynamic job name
            $templateData = [
                'content' => $templateContent,
                'meta'    => [
                    'created' => date('Y-m-d H:i:s'),
                    'version' => '1.1',
                    'styles'  => $data['styles'],
                    'dynamic_vars' => [
                        'name_Job_en' => true // Flag that this template uses the job name variable
                    ]
                ]
            ];
        } else {
            // Regular template without dynamic job name
            $templateData = [
                'content' => $templateContent,
                'meta'    => [
                    'created' => date('Y-m-d H:i:s'),
                    'version' => '1.1',
                    'styles'  => $data['styles']
                ]
            ];
        }

        // Create template name and save as before
        if (isset($data['name']) && is_string($data['name']) && trim($data['name']) !== '') {
            $templateName = basename(trim($data['name']));
        } else {
            $templateName = 'template_' . preg_replace('/[^0-9]/', '', microtime(true))
                            . '_' . bin2hex(random_bytes(4)) . '.json';
        }

        $jsonData = json_encode($templateData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_HEX_TAG);

        // Check if we're updating an existing template
        if (isset($data['updateId']) && is_numeric($data['updateId'])) {
            // Verify the template exists
            $stmt = $db->prepare("SELECT id_template_contract FROM template_contract WHERE id_template_contract = :id");
            $stmt->execute([':id' => $data['updateId']]);
            
            if ($stmt->fetch()) {
                // Update the template
                $updateStmt = $db->prepare("UPDATE template_contract SET 
                                            date_template_contract = :data, 
                                            type_template = :type
                                            WHERE id_template_contract = :id");
                $updateStmt->execute([
                    ':data' => $jsonData,
                    ':type' => $data['type'],
                    ':id' => $data['updateId']
                ]);
                
                echo json_encode([
                    'status'       => 'success',
                    'message'      => '✅ تم تحديث القالب بنجاح!',
                    'file'         => $templateName,
                    'download_url' => '?download=' . urlencode($templateName)
                ]);
                exit;
            } else {
                throw new Exception('القالب المراد تحديثه غير موجود');
            }
        } else {
            // Insert the new template into the database
            $stmt = $db->prepare("INSERT INTO template_contract (name_template_contract, type_template, date_template_contract) VALUES (:name, :type, :data)");
            $stmt->execute([
                ':name' => $templateName,
                ':type' => $data['type'],
                ':data' => $jsonData
            ]);

            echo json_encode([
                'status'       => 'success',
                'message'      => '✅ تم حفظ القالب بنجاح!',
                'file'         => $templateName,
                'download_url' => '?download=' . urlencode($templateName)
            ]);
            exit;
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => '❌ ' . $e->getMessage()]);
        exit;
    }
}

// ---------------------------
// Updated GET handler to download the template from the database
// ---------------------------
if (isset($_GET['download'])) {
    try {
        $templateName = basename($_GET['download']);
        
        // Connect to the database
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('خطأ في قراءة ملف الإعدادات');
        }
        $servername = trim(fgets($file));
        $username   = trim(fgets($file));
        $password   = trim(fgets($file));
        $dbname     = trim(fgets($file));
        fclose($file);
        
        $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // First, check if this is an extended contract template
        $stmt = $db->prepare("SELECT ec.id_extension_contract, ec.id_contract, ec.version_date as version_date_extension, 
                                    ec.start_date_contract as start_date_contract_extension, 
                                    ec.end_date_contract as end_date_contract_extension,
                                    c.*, e.*, p.*, a.*, jt.*
                            FROM extension_contract ec
                            JOIN contract c ON ec.id_contract = c.id_contract
                            JOIN employees e ON c.id_employees = e.id_employees
                            JOIN project p ON c.id_Project = p.id_Project
                            JOIN administrators a ON p.id_administrators = a.id_administrators
                            JOIN job_titles jt ON c.name_Job = jt.name_Job
                            WHERE ec.id_extension_contract = :id");
        
        $stmt->execute([':id' => $templateName]);
        $contractData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$contractData) {
            // If not found in extension_contract, check regular contract
            $stmt = $db->prepare("SELECT c.*, e.*, p.*, a.*, jt.*
                                FROM contract c
                                JOIN employees e ON c.id_employees = e.id_employees
                                JOIN project p ON c.id_Project = p.id_Project
                                JOIN administrators a ON p.id_administrators = a.id_administrators
                                JOIN job_titles jt ON c.name_Job = jt.name_Job
                                WHERE c.id_contract = :id");
            $stmt->execute([':id' => $templateName]);
            $contractData = $stmt->fetch(PDO::FETCH_ASSOC);
        }

        if (!$contractData) {
            throw new Exception('الملف غير موجود');
        }

        // Process template content with the fetched data
        $templateData = [
            'content' => $row['date_template_contract'],
            'meta' => [
                'created' => date('Y-m-d H:i:s'),
                'version' => '1.1',
                'contract_data' => $contractData
            ]
        ];

        // Replace variables in the template
        foreach ($contractData as $key => $value) {
            $templateData['content'] = str_replace(
                '(' . $key . ')', 
                $value,
                $templateData['content']
            );
        }

        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="contract_template_' . date('Ymd') . '.json"');
        echo json_encode($templateData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_HEX_TAG);
        exit;
    } catch (Exception $e) {
        die($e->getMessage());
    }
}

// Get existing templates
$templates = [];
if (is_dir(TEMPLATE_DIR)) {
    $files = scandir(TEMPLATE_DIR, SCANDIR_SORT_DESCENDING);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'json') {
            $templates[] = $file;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📝 Professional Contract Authoring Suite</title>
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;700&display=swap" rel="stylesheet">
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/lib/quill/quill.snow.css" rel="stylesheet">
    <link href="../assets/lib/mdi/css/materialdesignicons.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --page-margin-top: 5mm;
            --page-margin-bottom: 5mm;
            --page-margin-left: 5mm;
            --page-margin-right: 5mm;
        }

        body {
            font-family: 'Tahoma', sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .editor-wrapper {
            display: flex;
            justify-content: center;
            padding: 20px 0;
            min-height: calc(100vh - 250px);
        }

        .editor-wrapper {
            display: flex;
            justify-content: center;
            padding: 20px 0;
            min-height: calc(100vh - 250px);
        }

        .format-panel {
            flex: 0 0 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
        }

        .format-section {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .format-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .format-section h3 {
            margin: 0 0 15px 0;
            color: var(--primary-color);
            font-size: 1rem;
        }

        .format-grid {
            display: grid;
            gap: 10px;
        }

        .format-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .format-control label {
            min-width: 100px;
            font-size: 0.9rem;
        }

        .format-control input,
        .format-control select {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .format-control input[type="number"] {
            width: 80px;
        }

        .a4-editor {
            flex: 0 0 auto;
            background: white;
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            box-shadow: 0 4px 24px rgba(0,0,0,0.15);
            position: relative;
            box-sizing: border-box;
            transform: scale(1.1);
            transform-origin: top center;
            height: auto;
        }

        .editor-content {
            position: relative;
            margin-top: var(--page-margin-top);
            margin-right: var(--page-margin-right);
            margin-bottom: var(--page-margin-bottom);
            margin-left: var(--page-margin-left);
            box-sizing: border-box;
            background: white;
            z-index: 1;
            min-height: calc(297mm - var(--page-margin-top) - var(--page-margin-bottom));
        }

        .ql-editor {
            padding: 0 !important;
            height: auto !important;
            min-height: calc(297mm - var(--page-margin-top) - var(--page-margin-bottom));
            direction: rtl;
            font-family: 'Times New Roman', 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
            -webkit-text-size-adjust: 100%;
            -moz-text-size-adjust: 100%;
            text-size-adjust: 100%;
            box-sizing: border-box;
            width: calc(210mm - var(--page-margin-left) - var(--page-margin-right));
            background: white;
            font-size: 12px !important; /* Default size */
            text-align: right !important;
            overflow-y: visible !important;
        }

        /* Ensure consistent font in all editor elements */
        .ql-editor p, 
        .ql-editor span, 
        .ql-editor strong, 
        .ql-editor em,
        .ql-editor u {
            font-family: 'Times New Roman', 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
        }

        /* Update the size classes with more granular sizes */
        .ql-editor .ql-size-small { 
            font-size: 8px !important; 
        }
        .ql-editor .ql-size-normal { 
            font-size: 12px !important; 
        }
        .ql-editor .ql-size-large { 
            font-size: 16px !important; 
        }
        .ql-editor .ql-size-huge { 
            font-size: 20px !important; 
        }

        /* Add specific style for all inline font sizes */
        .ql-editor [style*="font-size: 5px"] { font-size: 5px !important; }
        .ql-editor [style*="font-size: 6px"] { font-size: 6px !important; }
        .ql-editor [style*="font-size: 7px"] { font-size: 7px !important; }
        .ql-editor [style*="font-size: 8px"] { font-size: 8px !important; }
        .ql-editor [style*="font-size: 9px"] { font-size: 9px !important; }
        .ql-editor [style*="font-size: 10px"] { font-size: 10px !important; }
        .ql-editor [style*="font-size: 12px"] { font-size: 12px !important; }
        .ql-editor [style*="font-size: 14px"] { font-size: 14px !important; }
        .ql-editor [style*="font-size: 16px"] { font-size: 16px !important; }
        .ql-editor [style*="font-size: 18px"] { font-size: 18px !important; }
        .ql-editor [style*="font-size: 20px"] { font-size: 20px !important; }

        /* Add specific alignment classes */
        .ql-editor .ql-align-right {
            text-align: right !important;
        }

        .ql-editor .ql-align-left {
            text-align: left !important;
            direction: ltr !important;
        }

        .ql-editor .ql-align-center {
            text-align: center !important;
        }

        .ql-editor .ql-align-justify {
            text-align: justify !important;
        }

        .margin-guide {
            position: absolute;
            background: rgba(52, 152, 219, 0.08);
            pointer-events: none;
            z-index: 0;
            border: 1px dashed rgba(52, 152, 219, 0.3);
        }

        .margin-guide-top {
            top: 0;
            left: 0;
            right: 0;
            height: var(--page-margin-top);
        }

        .margin-guide-bottom {
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--page-margin-bottom);
        }

        .margin-guide-left {
            top: 0;
            left: 0;
            height: 100%;
            width: var(--page-margin-left);
        }

        .margin-guide-right {
            top: 0;
            right: 0;
            height: 100%;
            width: var(--page-margin-right);
        }

        /* Add page dimensions indicator */
        .a4-editor::after {
            content: "A4 (210mm × 297mm)";
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(44, 62, 80, 0.8);
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* Scroll and Zoom Buttons */
        .scroll-buttons {
            position: fixed;
            right: 20px;
            bottom: 20px;
            display: grid;
            grid-template-columns: repeat(2, 45px);
            gap: 10px;
            z-index: 1000;
        }

        .scroll-btn, .zoom-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: var(--secondary-color);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            opacity: 0.9;
        }

        .scroll-btn:hover, .zoom-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            opacity: 1;
        }

        .scroll-btn i, .zoom-btn i {
            font-size: 24px;
        }

        .scroll-btn:disabled, .zoom-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            opacity: 0.5;
        }

        .zoom-level {
            position: fixed;
            right: 75px;
            bottom: 20px;
            background: rgba(44, 62, 80, 0.8);
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .zoom-level.show {
            opacity: 1;
        }

        @media print {
            .format-panel,
            .toolbar,
            .template-list {
                display: none;
            }

            .editor-wrapper {
                display: block;
            }

            .a4-editor {
                box-shadow: none;
                margin: 0;
            }
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 1rem;
        }

        .btn-save {
            background: var(--success-color);
            color: white;
            width: 100%;
            justify-content: center;
        }

        .btn-save:hover {
            background: #219a52;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .top-toolbar {
            position: sticky;
            top: 20px;
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-items: center;
            background: #ffffff;
            padding: 8px 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            z-index: 999;
            gap: 8px;
            margin: 20px 0;
            border-radius: 8px;
            backdrop-filter: blur(8px);
        }

        .top-toolbar .toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap;
            padding: 4px 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .margin-controls, .format-controls {
            display: flex;
            gap: 4px;
        }

        .input-with-unit {
            display: flex;
            align-items: center;
            gap: 4px;
            background: white;
            border-radius: 4px;
            padding: 2px 4px;
        }

        .margin-icon {
            font-size: 18px;
            color: #6c757d;
            width: 20px;
            text-align: center;
        }

        .input-with-unit input[type="number"] {
            width: 40px;
            padding: 2px 4px;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            font-size: 0.85rem;
        }

        .input-with-unit select {
            padding: 2px 4px;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            font-size: 0.85rem;
            background: white;
        }

        .unit-label {
            color: #6c757d;
            font-size: 0.75rem;
            min-width: 20px;
        }

        .btn-save {
            padding: 6px 12px;
            font-size: 0.9rem;
        }

        #custom-quill-toolbar {
            padding: 4px !important;
        }

        #custom-quill-toolbar .ql-formats {
            margin-right: 6px !important;
            padding-right: 6px;
        }

        #custom-quill-toolbar button {
            width: 24px;
            height: 24px;
            padding: 2px;
            margin: 0 1px;
        }

        /* Style for the Quill toolbar */
        #custom-quill-toolbar {
            border: none !important;
            padding: 0 !important;
            background: #f8f9fa;
            border-radius: 6px;
        }

        #custom-quill-toolbar .ql-formats {
            margin-right: 10px !important;
            border-right: 1px solid #dee2e6;
            padding-right: 10px;
        }

        #custom-quill-toolbar .ql-formats:last-child {
            border-right: none;
        }

        #custom-quill-toolbar button {
            width: 28px;
            height: 28px;
            padding: 3px;
            margin: 0 2px;
        }

        #custom-quill-toolbar button:hover {
            background-color: #e9ecef;
        }

        #custom-quill-toolbar .ql-active {
            background-color: var(--secondary-color) !important;
            color: white !important;
        }

        #custom-quill-toolbar select.ql-size,
        #custom-quill-toolbar select.ql-color,
        #custom-quill-toolbar select.ql-background,
        #custom-quill-toolbar select.ql-align {
            width: auto !important;
            margin: 0 2px !important;
        }

        /* Save button styling */
        .btn-save {
            background: var(--success-color);
            color: white;
            padding: 8px 20px;
            border-radius: 6px;
            font-weight: 500;
            min-width: 140px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
        }

        .btn-save:hover {
            background: #219a52;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
        }

        .btn-save i {
            font-size: 1.2rem;
            margin-right: 8px;
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .top-toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-group {
                width: 100%;
                justify-content: flex-start;
            }
            
            .btn-save {
                width: 100%;
                justify-content: center;
            }
        }

        /* Add visual separation between toolbar sections */
        .toolbar-group + .toolbar-group {
            position: relative;
        }

        /* Improve spacing for number inputs with units */
        .top-toolbar .input-with-unit {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .top-toolbar .unit-label {
            color: #6c757d;
            font-size: 0.85rem;
        }

        /* Split page styles */
        .page-divider {
            position: absolute;
            left: 60%;  /* Change from 40% to 60% to swap the proportions */
            top: 0;
            bottom: 0;
            width: 1px;
            background-color: #ccc;
            z-index: 2;
        }

        .split-page .editor-content {
            display: flex;
            gap: 10px;
            min-height: calc(297mm - var(--page-margin-top) - var(--page-margin-bottom));
        }

        .split-page .ql-editor {
            min-height: calc(297mm - var(--page-margin-top) - var(--page-margin-bottom));
        }

        .split-page #editor-right .ql-editor {
            width: calc((210mm - var(--page-margin-left) - var(--page-margin-right)) * 0.4 - 5px) !important; /* Changed from 0.6 to 0.4 */
        }

        .split-page #editor-left .ql-editor {
            width: calc((210mm - var(--page-margin-left) - var(--page-margin-right)) * 0.6 - 5px) !important; /* Changed from 0.4 to 0.6 */
        }

        .split-page #editor-right,
        .split-page #editor-left {
            min-height: calc(297mm - var(--page-margin-top) - var(--page-margin-bottom));
            height: auto;
        }

        .split-page #editor-right {
            flex: 4; /* Changed from 6 to 4 */
        }

        .split-page #editor-left {
            flex: 6; /* Changed from 4 to 6 */
            direction: ltr !important;
        }

        .split-page #editor-left .ql-editor {
            direction: ltr !important;
            text-align: left !important;
        }

        .toolbar-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 8px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            color: #2c3e50;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toolbar-button:hover {
            background: #f8f9fa;
            border-color: #ced4da;
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .toolbar-button i {
            font-size: 1.2rem;
        }

        .toolbar-button:active {
            background: #e9ecef;
            transform: translateY(0);
        }

        #updateTemplateBtn {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        #updateTemplateBtn:hover {
            background-color: #45a049;
            border-color: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .template-buttons {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .template-buttons .toolbar-button {
            flex: 1;
            min-width: 40px;
            height: 40px;
        }
        
        .template-container {
            display: flex;
            flex-direction: column;
            width: 100%;
        }
        
        .template-input {
            margin-top: 8px;
            width: 100%;
        }

        /* Remove the old button styles */
        .btn-save {
            display: none;
        }

        /* Variable Suggestion Styles */
        #variableSuggestions {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            width: 450px;
            border: 1px solid #e9ecef;
            opacity: 1;
            transform: scale(1);
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            height: 380px;
            overflow: hidden;
        }

        .suggestion-search {
            padding: 8px 12px;
            background: #f8fafc;
            z-index: 2;
            flex-shrink: 0;
            margin: 0;
            border-bottom: 1px solid #edf2f7;
            position: relative;
        }

        .suggestion-search::before {
            content: '\F349'; /* MDI magnify icon */
            font-family: 'Material Design Icons';
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
            font-size: 1.2rem;
            pointer-events: none;
        }

        .suggestion-search input {
            width: 100%;
            padding: 6px 35px 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9rem;
            background: white;
            transition: all 0.2s ease;
            direction: rtl;
        }

        .suggestion-search input:focus {
            outline: none;
            border-color: var(--secondary-color);
            background: white;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
        }

        .suggestion-search input::placeholder {
            color: #94a3b8;
            font-size: 0.85rem;
        }

        .suggestion-list {
            height: calc(100% - 53px);
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: rgba(0,0,0,0.2) transparent;
            margin: 0;
            padding: 4px 0;
            background: white;
        }

        .suggestion-list::-webkit-scrollbar {
            width: 4px;
        }

        .suggestion-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .suggestion-list::-webkit-scrollbar-thumb {
            background-color: rgba(0,0,0,0.15);
            border-radius: 2px;
        }

        .suggestion-list::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0,0,0,0.25);
        }

        .suggestion-category {
            padding: 8px 12px;
            background: #f8fafc;
            font-weight: 600;
            color: #64748b;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #f1f5f9;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.15s ease;
            background: white;
            border-radius: 4px;
            margin: 0 4px;
        }

        .suggestion-item:hover {
            background: #f1f5f9;
        }

        .suggestion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            gap: 12px;
        }

        .suggestion-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 0.9rem;
            flex: 1;
            min-width: 0;
        }

        .suggestion-value {
            font-family: 'Consolas', monospace;
            color: var(--secondary-color);
            font-size: 0.8rem;
            padding: 2px 6px;
            background: rgba(52, 152, 219, 0.08);
            border-radius: 3px;
            direction: ltr;
            display: inline-block;
            white-space: nowrap;
            border: 1px solid rgba(52, 152, 219, 0.1);
        }

        .suggestion-description {
            color: #64748b;
            font-size: 0.85rem;
            line-height: 1.4;
            margin-top: 2px;
        }

        /* Smooth show/hide animation */
        #variableSuggestions.show {
            opacity: 1;
            transform: scale(1) translateY(0);
        }

        #variableSuggestions.hide {
            opacity: 0;
            transform: scale(0.98) translateY(-10px);
            pointer-events: none;
        }

        /* Add page break indicators */
        .ql-editor::after {
            content: '';
            display: block;
            height: 1px;
            background: repeating-linear-gradient(
                to right,
                rgba(52, 152, 219, 0.3) 0,
                rgba(52, 152, 219, 0.3) 4px,
                transparent 4px,
                transparent 8px
            );
            margin: var(--page-margin-bottom) calc(-1 * var(--page-margin-right)) var(--page-margin-top) calc(-1 * var(--page-margin-left));
            pointer-events: none;
        }

        /* Add sidebar and theme styles */
        body[data-theme="light"] {
            --bg-main: #f8f9fa;
            --bg-card: #ffffff;
            --text-primary: #2c3e50;
            --text-muted: #6c757d;
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        body[data-theme="dark"] {
            --bg-main: #1a1a1a;
            --bg-card: #2d2d2d;
            --text-primary: #ffffff;
            --text-muted: #a0a0a0;
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        #sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            right: 0;
            top: 0;
            background-color: var(--bg-sidebar);
            border-left: 1px solid var(--border-color);
            padding: 1rem;
            transition: all 0.3s ease;
            box-shadow: -4px 0 10px var(--shadow-color);
            z-index: 1000;
            overflow-y: auto;
            font-family: 'Cairo', 'Tahoma', sans-serif;
        }

        #content {
            margin-right: 250px;
            padding: 2rem;
            transition: margin 0.3s ease;
        }

        @media (max-width: 991.98px) {
            #sidebar {
                transform: translateX(100%);
            }
            
            #content {
                margin-right: 0;
            }
            
            body.sidebar-open #sidebar {
                transform: translateX(0);
            }
        }

        .logo {
            text-align: center;
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--shadow-color);
            font-family: 'Cairo', 'Tahoma', sans-serif;
            font-weight: 700;
        }

        .nav-link {
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', 'Tahoma', sans-serif;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--bg-main);
            color: var(--primary-color);
        }

        .nav-link i {
            font-size: 1.1rem;
            margin-left: 8px;
        }

        #theme-toggle {
            position: absolute;
            bottom: 1rem;
            left: 1rem;
            right: 1rem;
            padding: 0.75rem;
            background: var(--bg-main);
            color: var(--text-primary);
            border: none;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', 'Tahoma', sans-serif;
            font-weight: 500;
            font-size: 0.9rem;
        }

        #theme-toggle:hover {
            background: var(--shadow-color);
        }

        .dashboard {
            max-width: none;
            margin-right: 250px;
            padding: 20px;
        }

        @media (max-width: 991.98px) {
            .dashboard {
                margin-right: 0;
            }
        }

        /* Add these styles to the existing <style> section */
        .nav-item.dropdown {
            position: relative;
        }

        .nav-item.dropdown .dropdown-toggle {
            width: 100%;
            text-align: right;
            background: none;
            border: none;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', 'Tahoma', sans-serif;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .nav-item.dropdown .dropdown-toggle:hover {
            background: var(--bg-main);
            color: var(--primary-color);
        }

        .nav-item.dropdown .dropdown-menu {
            position: static;
            width: 100%;
            padding: 0.5rem 0;
            margin: 0;
            border: none;
            background: transparent;
            box-shadow: none;
        }

        .nav-item.dropdown .dropdown-item {
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            color: var(--text-muted);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .nav-item.dropdown .dropdown-item:hover {
            background: var(--bg-main);
            color: var(--primary-color);
        }

        .nav-item.dropdown .dropdown-item i {
            font-size: 1rem;
        }

        /* Add these styles in the existing <style> section */
        .btn-group {
            display: flex;
            gap: 8px;
        }

        .contract-type-btn {
            opacity: 0.7;
            transition: all 0.2s ease;
        }

        .contract-type-btn.active {
            background: var(--secondary-color);
            color: white;
            opacity: 1;
        }

        .contract-type-btn:hover {
            opacity: 1;
        }

        /* Add a red asterisk to indicate required selection */
        .contract-type-required::after {
            content: '*';
            color: #e74c3c;
            margin-right: 4px;
            font-size: 1.2em;
        }

        /* Add these styles in the existing <style> section */
        .contract-type-selection {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            justify-content: center;
        }

        .contract-type-selection .btn-group {
            display: flex;
            gap: 12px;
        }

        .contract-type-selection .toolbar-button {
            padding: 10px 20px;
            font-size: 1rem;
        }

        .contract-type-required {
            position: relative;
        }

        .contract-type-required::after {
            position: absolute;
            top: 15px;
            right: -15px;
        }

        /* Add these to your existing styles */
        .editor-disabled {
            position: relative;
            pointer-events: none;
            opacity: 0.7;
        }

        /* Hide the original message outside the A4 page */
        .editor-disabled::before {
            display: none;
        }

        /* Add the message inside the A4 page */
        .editor-disabled .a4-editor::after {
            content: 'يرجى اختيار نوع العقد أولاً';
            position: absolute;
            top: var(--page-margin-top, 20mm);
            left: 50%;
            transform: translateX(-50%);
            background: rgba(44, 62, 80, 0.9);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 1.1rem;
            z-index: 10;
            white-space: nowrap;
            width: auto;
            display: inline-block;
            box-sizing: content-box;
        }

        .editor-disabled .ql-toolbar {
            opacity: 0.5;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    $jsonFile = __DIR__ . '/../config/sidebar.json';
    $jsonContent = file_get_contents($jsonFile);
    if ($jsonContent === false) {
        die('Error: Unable to read sidebar configuration file');
    }
    $config = json_decode($jsonContent, true);
    if ($config === null) {
        die('Error: Invalid JSON in sidebar configuration file');
    }
    $currentPage = 'index.php';
    ?>
    <!-- Sidebar -->
    <div id="sidebar">
        <div class="logo">
            <div><?php echo htmlspecialchars($config['logo']); ?></div>
        </div>
        
        <nav class="nav flex-column">
            <?php if (isset($config['menu_items']) && is_array($config['menu_items'])): ?>
                <?php foreach ($config['menu_items'] as $item): ?>
                    <?php if (isset($item['type']) && $item['type'] === 'dropdown'): ?>
                        <div class="nav-item dropdown">
                            <button class="nav-link dropdown-toggle w-100" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi <?php echo htmlspecialchars($item['icon']); ?>"></i>
                                <?php echo htmlspecialchars($item['title']); ?>
                            </button>
                            <ul class="dropdown-menu">
                                <?php if (isset($item['items']) && is_array($item['items'])): ?>
                                    <?php foreach ($item['items'] as $subItem): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo htmlspecialchars($subItem['url']); ?>">
                                                <i class="bi <?php echo htmlspecialchars($subItem['icon']); ?>"></i>
                                                <?php echo htmlspecialchars($subItem['title']); ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" class="nav-link <?php echo $currentPage === $item['url'] ? 'active' : ''; ?>">
                            <i class="bi <?php echo htmlspecialchars($item['icon']); ?>"></i>
                            <?php echo htmlspecialchars($item['title']); ?>
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </nav>

        <button id="theme-toggle" class="btn">
            <i class="bi bi-moon"></i>
            <span>تغيير المظهر</span>
        </button>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button id="toggle-sidebar" class="btn btn-primary position-fixed d-lg-none" style="top: 1rem; right: 1rem; z-index: 1001;">
        <i class="bi bi-list"></i>
    </button>

    <!-- Main Content -->
    <div class="dashboard">
        <div class="contract-type-selection contract-type-required">
            <div class="btn-group" role="group" aria-label="نوع العقد">
                <button type="button" class="toolbar-button contract-type-btn" data-type="1">
                    <i class="mdi mdi-file-document"></i>
                    عقد أساسي
                </button>
                <button type="button" class="toolbar-button contract-type-btn" data-type="2">
                    <i class="mdi mdi-file-document-edit"></i>
                    عقد ممدد
                </button>
            </div>
        </div>

        <div class="top-toolbar">
            <div class="toolbar-group">
                <div id="custom-quill-toolbar">
                    <span class="ql-formats">
                        <button class="ql-bold" title="تخط عريض"></button>
                        <button class="ql-italic" title="خط مائل"></button>
                        <button class="ql-underline" title="تسطير"></button>
                        <button class="ql-strike" title="يتوسطه خط"></button>
                    </span>
                    <span class="ql-formats">
                        <select class="ql-size" title="حجم الخط">
                            <option value="5px">5px</option>
                            <option value="6px">6px</option>
                            <option value="7px">7px</option>
                            <option value="8px">8px</option>
                            <option value="9px">9px</option>
                            <option value="10px">10px</option>
                            <option value="12px" selected>12px</option>
                            <option value="14px">14px</option>
                            <option value="16px">16px</option>
                            <option value="18px">18px</option>
                            <option value="20px">20px</option>
                        </select>
                    </span>
                    <span class="ql-formats">
                        <select class="ql-color" title="لون الخط"></select>
                        <select class="ql-background" title="لون الخلفية"></select>
                    </span>
                    <span class="ql-formats">
                        <select class="ql-align" title="محاذاة">
                            <option value="right" selected></option>
                            <option value="center"></option>
                            <option value="left"></option>
                            <option value="justify"></option>
                        </select>
                    </span>
                    <span class="ql-formats">
                        <button class="ql-link" title="رابط"></button>
                        <button class="ql-blockquote" title="اقتباس"></button>
                        <button class="ql-clean" title="إزالة التنسيق"></button>
                    </span>
                </div>
            </div>
            
            <div class="toolbar-group margin-controls">
                <div class="input-with-unit">
                    <i class="mdi mdi-arrow-up margin-icon" title="الهامش العلوي"></i>
                    <input type="number" id="marginTop" value="5" min="0" max="100" onchange="updateFormat()">
                    <span class="unit-label">mm</span>
                </div>
                <div class="input-with-unit">
                    <i class="mdi mdi-arrow-down margin-icon" title="الهامش السفلي"></i>
                    <input type="number" id="marginBottom" value="5" min="0" max="100" onchange="updateFormat()">
                    <span class="unit-label">mm</span>
                </div>
                <div class="input-with-unit">
                    <i class="mdi mdi-arrow-right margin-icon" title="الهامش الأيمن"></i>
                    <input type="number" id="marginRight" value="5" min="0" max="100" onchange="updateFormat()">
                    <span class="unit-label">mm</span>
                </div>
                <div class="input-with-unit">
                    <i class="mdi mdi-arrow-left margin-icon" title="الهامش الأيسر"></i>
                    <input type="number" id="marginLeft" value="5" min="0" max="100" onchange="updateFormat()">
                    <span class="unit-label">mm</span>
                </div>
            </div>
            
            <div class="toolbar-group">
                <div class="template-container">
                    <div class="template-buttons">
                        <button onclick="saveTemplate()" id="saveTemplateBtn" class="toolbar-button" title="حفظ القالب">
                            <i class="mdi mdi-content-save"></i>
                        </button>
                        <button onclick="updateTemplate()" id="updateTemplateBtn" class="toolbar-button" title="تحديث القالب" style="display: none;">
                            <i class="mdi mdi-update"></i>
                        </button>
                        <button onclick="downloadTemplate()" class="toolbar-button" title="تنزيل القالب">
                            <i class="mdi mdi-download"></i>
                        </button>
                        <a href="template_restore.php" class="toolbar-button" style="text-decoration: none; display: inline-flex; align-items: center;" title="استعادة قالب">
                            <i class="mdi mdi-upload"></i>
                        </a>
                        <button onclick="toggleSplitPage()" class="toolbar-button" id="splitPageBtn" title="تقسيم الصفحة">
                            <i class="mdi mdi-format-columns"></i>
                        </button>
                    </div>
                    <div class="template-input">
                        <input type="text" id="templateFileName" placeholder="اسم القالب" style="padding: 6px; border: 1px solid #ddd; border-radius: 4px; width: 100%;">
                    </div>
                </div>
            </div>
        </div>

        <div class="editor-wrapper editor-disabled">
            <div class="a4-editor">
                <div class="margin-guide margin-guide-top"></div>
                <div class="margin-guide margin-guide-bottom"></div>
                <div class="margin-guide margin-guide-left"></div>
                <div class="margin-guide margin-guide-right"></div>
                <div class="editor-content">
                    <div id="editor-right"></div>
                    <div id="editor-left" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Scroll and Zoom Buttons -->
    <div class="scroll-buttons">
        <button class="scroll-btn" onclick="scrollPage('up')" title="تمرير لأعلى">
            <i class="mdi mdi-chevron-up"></i>
        </button>
        <button class="zoom-btn" onclick="zoomPage('in')" title="تكبير">
            <i class="mdi mdi-plus"></i>
        </button>
        <button class="scroll-btn" onclick="scrollPage('down')" title="تمرير لأسفل">
            <i class="mdi mdi-chevron-down"></i>
        </button>
        <button class="zoom-btn" onclick="zoomPage('out')" title="تصغير">
            <i class="mdi mdi-minus"></i>
        </button>
    </div>
    <div class="zoom-level">110%</div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/lib/quill/quill.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Theme Management
            const themeToggle = document.getElementById('theme-toggle');
            const body = document.body;
            const icon = themeToggle.querySelector('i');
            
            // Force light theme
            body.setAttribute('data-theme', 'light');
            icon.classList.remove('bi-sun');
            icon.classList.add('bi-moon');
            
            // Theme toggle handler - disabled
            themeToggle.style.display = 'none';
            
            // Mobile sidebar toggle
            document.getElementById('toggle-sidebar')?.addEventListener('click', () => {
                document.body.classList.toggle('sidebar-open');
            });
        });

        var Size = Quill.import('attributors/style/size');
        Size.whitelist = ['5px', '6px', '7px', '8px', '9px', '10px', '12px', '14px', '16px', '18px', '20px'];
        Quill.register(Size, true);

        // Added alignment registration
        var AlignStyle = Quill.import('attributors/style/align');
        AlignStyle.whitelist = ['left', 'center', 'right', 'justify'];
        Quill.register(AlignStyle, true);

        // After registering AlignStyle, add the icon override code
        var icons = Quill.import('ui/icons');
        icons['align'][''] = '<i class="mdi mdi-format-align-left"></i>';
        icons['align']['center'] = '<i class="mdi mdi-format-align-center"></i>';
        icons['align']['right'] = '<i class="mdi mdi-format-align-right"></i>';
        icons['align']['justify'] = '<i class="mdi mdi-format-align-justify"></i>';

        const quill = new Quill('#editor-right', {
            theme: 'snow',
            modules: {
                toolbar: {
                    container: '#custom-quill-toolbar',
                    handlers: {
                        'size': function(value) {
                            this.quill.format('size', false);
                            this.quill.format('size', value);
                        }
                    }
                }
            },
            formats: ['bold', 'italic', 'underline', 'strike', 'align', 'list', 'size', 'color', 'background', 'link', 'blockquote', 'code-block'],
        });

        // Initialize with default size
        quill.format('size', '12px');

        // Add this function to handle size changes consistently
        function initializeQuillEditor(editor) {
            editor.on('text-change', function(delta, oldDelta, source) {
                if (source === 'user') {
                    const selection = editor.getSelection();
                    if (selection) {
                        const format = editor.getFormat(selection);
                        if (format.size) {
                            // Ensure the size is applied correctly
                            editor.formatText(selection.index, selection.length, {
                                'size': format.size
                            }, 'api');
                        }
                    }
                }
            });
        }

        // Initialize the handlers for both editors
        initializeQuillEditor(quill);

        let quillLeft;

        function insertVariable(variable) {
            const range = quill.getSelection();
            const activeEditor = document.activeElement.closest('#editor-left') ? quillLeft : quill;
            activeEditor.insertText(range.index, variable + ' ');
            activeEditor.setSelection(range.index + variable.length + 1);
        }

        function updateFormat() {
            const marginTopEl = document.getElementById('marginTop');
            const marginBottomEl = document.getElementById('marginBottom');
            const marginRightEl = document.getElementById('marginRight');
            const marginLeftEl = document.getElementById('marginLeft');
            const top = marginTopEl ? marginTopEl.value : 5;
            const bottom = marginBottomEl ? marginBottomEl.value : 5;
            const right = marginRightEl ? marginRightEl.value : 5;
            const left = marginLeftEl ? marginLeftEl.value : 5;
            
            // Update CSS variables for margins
            document.documentElement.style.setProperty('--page-margin-top', `${top}mm`);
            document.documentElement.style.setProperty('--page-margin-bottom', `${bottom}mm`);
            document.documentElement.style.setProperty('--page-margin-right', `${right}mm`);
            document.documentElement.style.setProperty('--page-margin-left', `${left}mm`);
        }

        function toggleSplitPage() {
            const editorContent = document.querySelector('.editor-content');
            const editorRight = document.querySelector('#editor-right');
            const editorLeft = document.querySelector('#editor-left');
            const a4Editor = document.querySelector('.a4-editor');
            const splitPageBtn = document.querySelector('#splitPageBtn');
            
            if (!a4Editor.classList.contains('split-page')) {
                // Enable split page
                a4Editor.classList.add('split-page');
                editorLeft.style.display = 'block';
                splitPageBtn.innerHTML = '<i class="mdi mdi-format-columns"></i>';
                
                // Initialize left editor if not already initialized
                if (!quillLeft) {
                    quillLeft = new Quill('#editor-left', {
                        theme: 'snow',
                        modules: {
                            toolbar: {
                                container: '#custom-quill-toolbar',
                                handlers: {
                                    'size': function(value) {
                                        this.quill.format('size', false);
                                        this.quill.format('size', value);
                                    }
                                }
                            }
                        },
                        formats: ['bold', 'italic', 'underline', 'strike', 'align', 'list', 'size', 'color', 'background', 'link', 'blockquote', 'code-block'],
                    });
                }

                // Add divider line at 60% position
                const divider = document.createElement('div');
                divider.className = 'page-divider';
                editorContent.appendChild(divider);
            } else {
                // Disable split page
                a4Editor.classList.remove('split-page');
                editorLeft.style.display = 'none';
                splitPageBtn.innerHTML = '<i class="mdi mdi-format-columns"></i>';
                
                // Remove divider line
                const divider = document.querySelector('.page-divider');
                if (divider) {
                    divider.remove();
                }
            }
        }

        // Function to download the template as JSON
        function downloadTemplate() {
            try {
                // Check if contract type is selected
                const activeTypeBtn = document.querySelector('.contract-type-btn.active');
                if (!activeTypeBtn) {
                    throw new Error('يجب اختيار نوع العقد أولاً');
                }
                
                const contractType = activeTypeBtn.dataset.type;
                
                const formatSettings = {
                    margins: {
                        top: (document.getElementById('marginTop') ? document.getElementById('marginTop').value : 5),
                        bottom: (document.getElementById('marginBottom') ? document.getElementById('marginBottom').value : 5),
                        right: (document.getElementById('marginRight') ? document.getElementById('marginRight').value : 5),
                        left: (document.getElementById('marginLeft') ? document.getElementById('marginLeft').value : 5)
                    },
                    contentStyles: quill.getContents().ops,
                    isSplitPage: document.querySelector('.a4-editor').classList.contains('split-page'),
                    leftContent: quillLeft ? quillLeft.getContents().ops : null
                };
                
                const rightContent = quill.root.innerHTML;
                const leftContent = quillLeft ? quillLeft.root.innerHTML : '';
                const content = formatSettings.isSplitPage ? 
                    `<div class="split-content"><div class="right-content">${rightContent}</div><div class="left-content">${leftContent}</div></div>` :
                    rightContent;
                
                const fileName = document.getElementById('templateFileName').value.trim();
                // Check to ensure a name has been entered
                if (!fileName) {
                    alert('❌ يجب إدخال اسم القالب.');
                    return;
                }
                
                // Create the JSON data
                const jsonData = { 
                    template: content,
                    styles: formatSettings,
                    name: fileName,
                    type: contractType
                };
                
                // Convert to JSON string
                const jsonString = JSON.stringify(jsonData, null, 2);
                
                // Create a blob with the JSON data
                const blob = new Blob([jsonString], { type: 'application/json' });
                
                // Create a download link
                const downloadLink = document.createElement('a');
                downloadLink.href = URL.createObjectURL(blob);
                downloadLink.download = `${fileName}.json`;
                
                // Append to the document, click it, and remove it
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                
                alert('✅ تم تنزيل القالب بنجاح.');
            } catch (error) {
                alert('❌ فشل في التنزيل: ' + error.message);
            }
        }

        // Find the saveTemplate function and replace it with this updated version
        async function saveTemplate() {
            try {
                // Check if in update mode (shouldn't happen due to UI, but as a safeguard)
                if (document.getElementById('updateTemplateBtn').style.display !== 'none') {
                    throw new Error('القالب في وضع التحديث. يجب استخدام زر التحديث.');
                }
                
                // Check if contract type is selected
                const activeTypeBtn = document.querySelector('.contract-type-btn.active');
                if (!activeTypeBtn) {
                    throw new Error('يجب اختيار نوع العقد أولاً');
                }
                
                const contractType = activeTypeBtn.dataset.type;
                
                const formatSettings = {
                    margins: {
                        top: (document.getElementById('marginTop') ? document.getElementById('marginTop').value : 5),
                        bottom: (document.getElementById('marginBottom') ? document.getElementById('marginBottom').value : 5),
                        right: (document.getElementById('marginRight') ? document.getElementById('marginRight').value : 5),
                        left: (document.getElementById('marginLeft') ? document.getElementById('marginLeft').value : 5)
                    },
                    contentStyles: quill.getContents().ops,
                    isSplitPage: document.querySelector('.a4-editor').classList.contains('split-page'),
                    leftContent: quillLeft ? quillLeft.getContents().ops : null
                };
                
                const rightContent = quill.root.innerHTML;
                const leftContent = quillLeft ? quillLeft.root.innerHTML : '';
                const content = formatSettings.isSplitPage ? 
                    `<div class="split-content"><div class="right-content">${rightContent}</div><div class="left-content">${leftContent}</div></div>` :
                    rightContent;
                
                const fileName = document.getElementById('templateFileName').value.trim();
                // New check to ensure a name has been entered
                if (!fileName) {
                    alert('❌ يجب إدخال اسم القالب.');
                    return;
                }
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        template: content,
                        styles: formatSettings,
                        name: fileName,
                        type: contractType // Add contract type to the request
                    })
                });
                
                const result = await response.json();
                if (result.status === 'success') {
                    alert(result.message);
                    window.location.reload();
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                alert('❌ فشل في الحفظ: ' + error.message);
            }
        }
        
        // Add updateTemplate function
        async function updateTemplate() {
            try {
                // Get the template ID stored in the update button
                const updateTemplateId = document.getElementById('updateTemplateBtn').dataset.templateId;
                if (!updateTemplateId) {
                    throw new Error('معرف القالب غير موجود');
                }
                
                // Check if contract type is selected
                const activeTypeBtn = document.querySelector('.contract-type-btn.active');
                if (!activeTypeBtn) {
                    throw new Error('يجب اختيار نوع العقد أولاً');
                }
                
                const contractType = activeTypeBtn.dataset.type;
                
                const formatSettings = {
                    margins: {
                        top: (document.getElementById('marginTop') ? document.getElementById('marginTop').value : 5),
                        bottom: (document.getElementById('marginBottom') ? document.getElementById('marginBottom').value : 5),
                        right: (document.getElementById('marginRight') ? document.getElementById('marginRight').value : 5),
                        left: (document.getElementById('marginLeft') ? document.getElementById('marginLeft').value : 5)
                    },
                    contentStyles: quill.getContents().ops,
                    isSplitPage: document.querySelector('.a4-editor').classList.contains('split-page'),
                    leftContent: quillLeft ? quillLeft.getContents().ops : null
                };
                
                const rightContent = quill.root.innerHTML;
                const leftContent = quillLeft ? quillLeft.root.innerHTML : '';
                const content = formatSettings.isSplitPage ? 
                    `<div class="split-content"><div class="right-content">${rightContent}</div><div class="left-content">${leftContent}</div></div>` :
                    rightContent;
                
                const fileName = document.getElementById('templateFileName').value.trim();
                // New check to ensure a name has been entered
                if (!fileName) {
                    alert('❌ يجب إدخال اسم القالب.');
                    return;
                }
                
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        template: content,
                        styles: formatSettings,
                        name: fileName,
                        type: contractType,
                        updateId: updateTemplateId // Add the template ID for update
                    })
                });
                
                const result = await response.json();
                if (result.status === 'success') {
                    alert(result.message);
                    window.location.reload();
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                alert('❌ فشل في التحديث: ' + error.message);
            }
        }

        // Set default formatting when editor is initialized
        document.addEventListener('DOMContentLoaded', function() {
            updateFormat();
            
            // Set default font size for quill content
            quill.root.style.fontSize = '12px';
            quill.format('size', '12px');
            
            // Apply the font size to all existing content
            const delta = quill.getContents();
            quill.updateContents([
                { retain: delta.length(), attributes: { size: '12px' } }
            ]);
            
            // Set default alignment to right
            quill.format('align', 'right');
            
            // Check if we have a restored template to load
            <?php if ($restoredTemplate): ?>
            // Load the restored template after a short delay to ensure all components are initialized
            setTimeout(loadRestoredTemplate, 500);
            <?php endif; ?>
        });

        // Handle template deletion
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', async function() {
                try {
                    const response = await fetch('', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                        body: `delete=1&file=${encodeURIComponent(this.dataset.file)}`
                    });
                    
                    const result = await response.json();
                    if (result.status === 'success') {
                        this.parentElement.remove();
                    }
                    alert(result.message);
                } catch (error) {
                    alert('❌ فشل في الحذف: ' + error.message);
                }
            });
        });

        // Add scroll functionality
        function scrollPage(direction) {
            const editorWrapper = document.querySelector('.editor-wrapper');
            const scrollAmount = 200; // Reduced for smoother scrolling
            
            if (direction === 'up') {
                window.scrollBy({
                    top: -scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                window.scrollBy({
                    top: scrollAmount,
                    behavior: 'smooth'
                });
            }
        }

        // Update scroll buttons state
        window.addEventListener('scroll', function() {
            const upButton = document.querySelector('.scroll-btn[onclick="scrollPage(\'up\')"]');
            const downButton = document.querySelector('.scroll-btn[onclick="scrollPage(\'down\')"]');
            
            // Enable/disable up button based on scroll position
            upButton.disabled = window.scrollY === 0;
            
            // Enable/disable down button based on scroll position
            const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
            downButton.disabled = window.scrollY >= maxScroll;
        });

        // Add zoom functionality
        let currentScale = 1.1; // Starting scale at 110%
        const MIN_SCALE = 0.5;
        const MAX_SCALE = 1.5;
        const SCALE_STEP = 0.1;

        function zoomPage(direction) {
            const editor = document.querySelector('.a4-editor');
            const zoomLevel = document.querySelector('.zoom-level');
            
            if (direction === 'in' && currentScale < MAX_SCALE) {
                currentScale += SCALE_STEP;
            } else if (direction === 'out' && currentScale > MIN_SCALE) {
                currentScale -= SCALE_STEP;
            }

            editor.style.transform = `scale(${currentScale})`;
            
            // Update zoom level indicator
            const percentage = Math.round(currentScale * 100);
            zoomLevel.textContent = `${percentage}%`;
            zoomLevel.classList.add('show');
            
            // Hide zoom level after 2 seconds
            clearTimeout(window.zoomTimeout);
            window.zoomTimeout = setTimeout(() => {
                zoomLevel.classList.remove('show');
            }, 2000);

            // Update zoom buttons state
            updateZoomButtons();
        }

        function updateZoomButtons() {
            const zoomInBtn = document.querySelector('.zoom-btn[onclick="zoomPage(\'in\')"]');
            const zoomOutBtn = document.querySelector('.zoom-btn[onclick="zoomPage(\'out\')"]');
            
            zoomInBtn.disabled = currentScale >= MAX_SCALE;
            zoomOutBtn.disabled = currentScale <= MIN_SCALE;
        }

        // Initialize zoom buttons state
        document.addEventListener('DOMContentLoaded', function() {
            updateZoomButtons();
            // Show initial zoom level
            const zoomLevel = document.querySelector('.zoom-level');
            const percentage = Math.round(currentScale * 100);
            zoomLevel.textContent = `${percentage}%`;
            zoomLevel.classList.add('show');
            setTimeout(() => {
                zoomLevel.classList.remove('show');
            }, 2000);
        });

        // Begin variable suggestion feature
        (function() {
            // Create suggestion container and append to body
            const suggestionContainer = document.createElement('div');
            suggestionContainer.id = 'variableSuggestions';
            suggestionContainer.style.display = 'none';
            suggestionContainer.style.position = 'absolute';
            suggestionContainer.style.zIndex = '1500';
            document.body.appendChild(suggestionContainer);

            // List of available variables
            const availableVariables = [
                // Contract Extension Information (only shown for type 2)
                {
                    category: "معلومات تمديد العقد",
                    vars: [
                        { 
                            display: "تاريخ نسخة التمديد", 
                            value: "(version_date_extension)",
                            description: "تاريخ إصدار نسخة تمديد العقد",
                            extendedOnly: true
                        },
                        { 
                            display: "تاريخ بداية تمديد العقد", 
                            value: "(start_date_contract_extension)",
                            description: "تاريخ بدء فترة تمديد العقد",
                            extendedOnly: true
                        },
                        { 
                            display: "تاريخ نهاية تمديد العقد", 
                            value: "(end_date_contract_extension)",
                            description: "تاريخ انتهاء فترة تمديد العقد",
                            extendedOnly: true
                        }
                    ]
                },
                // Project Information
                {
                    category: "معلومات المشروع",
                    vars: [
                        { 
                            display: "رقم المشروع", 
                            value: "(id_Project)",
                            description: "الرقم التعريفي الخاص بالمشروع"
                        },
                        { 
                            display: "اسم المشروع بالعربية", 
                            value: "(Project_name)",
                            description: "اسم المشروع باللغة العربية"
                        },
                        { 
                            display: "اسم المشروع بالإنجليزية", 
                            value: "(Project_name_en)",
                            description: "اسم المشروع باللغة الإنجليزية"
                        },
                        { 
                            display: "تاريخ نهاية المشروع", 
                            value: "(Project_end_date)",
                            description: "التاريخ المحدد لانتهاء المشروع"
                        }
                    ]
                },
                // Contract Holder Information
                {
                    category: "معلومات صاحب العقد",
                    vars: [
                        { 
                            display: "الاسم باللغة العربية", 
                            value: "(name_ar_contract)",
                            description: "اسم صاحب العقد مكتوب باللغة العربية"
                        },
                        { 
                            display: "الاسم باللغة الإنجليزية", 
                            value: "(name_en_contract)",
                            description: "اسم صاحب العقد مكتوب باللغة الإنجليزية"
                        }
                    ]
                },
                // Identity Information
                {
                    category: "معلومات الهوية",
                    vars: [
                        { 
                            display: "نوع الهوية بالعربية", 
                            value: "(Identity_contract_ar)",
                            description: "نوع وثيقة الهوية باللغة العربية"
                        },
                        { 
                            display: "نوع الهوية بالإنجليزية", 
                            value: "(Identity_contract_en)",
                            description: "نوع وثيقة الهوية باللغة الإنجليزية"
                        },
                        { 
                            display: "رقم الهوية", 
                            value: "(Identity_number_contract)",
                            description: "الرقم التسلسلي لوثيقة الهوية"
                        },
                        { 
                            display: "تاريخ الإصدار", 
                            value: "(Identity_issue_date_contract)",
                            description: "تاريخ إصدار وثيقة الهوية"
                        },
                        { 
                            display: "مكان الإصدار بالعربية", 
                            value: "(Identity_issue_contract_ar)",
                            description: "مكان إصدار وثيقة الهوية باللغة العربية"
                        },
                        { 
                            display: "مكان الإصدار بالإنجليزية", 
                            value: "(Identity_issue_contract_en)",
                            description: "مكان إصدار وثيقة الهوية باللغة الإنجليزية"
                        }
                    ]
                },
                // Contract Details
                {
                    category: "تفاصيل العقد",
                    vars: [
                        { 
                            display: "تاريخ بداية العقد", 
                            value: "(start_date_contract)",
                            description: "التاريخ الفعلي لبدء سريان العقد"
                        },
                        { 
                            display: "تاريخ نهاية العقد", 
                            value: "(end_date_contract)",
                            description: "التاريخ المحدد لانتهاء العقد"
                        },
                        { 
                            display: "تاريخ النسخة", 
                            value: "(version_date)",
                            description: "تاريخ إصدار النسخة الحالية من العقد"
                        }
                    ]
                },
                // Financial Information
                {
                    category: "المعلومات المالية",
                    vars: [
                        { 
                            display: "قيمة الأجر", 
                            value: "(wage_contract)",
                            description: "قيمة الأجر/الراتب المتفق عليه بالأرقام"
                        },
                        { 
                            display: "المبلغ بالحروف العربية", 
                            value: "(amount_written_ar)",
                            description: "قيمة الأجر/الراتب مكتوبة بالحروف العربية"
                        },
                        { 
                            display: "المبلغ بالحروف الإنجليزية", 
                            value: "(amount_written_en)",
                            description: "قيمة الأجر/الراتب مكتوبة بالحروف الإنجليزية"
                        }
                    ]
                },
                // Additional Information
                {
                    category: "معلومات إضافية",
                    vars: [
                        { 
                            display: "قائمة المهام بالعربية", 
                            value: "(data_todo_list_contract_ar)",
                            description: "قائمة المهام والملاحظات بالعربية"
                        },
                        { 
                            display: "قائمة المهام بالإنجليزية", 
                            value: "(data_todo_list_contract_en)",
                            description: "قائمة المهام والملاحظات بالإنجليزية"
                        }
                    ]
                },
                // Job Information
                {
                    category: "معلومات الوظيفة",
                    vars: [
                        { 
                            display: "المسمى الوظيفي بالعربية", 
                            value: "(name_Job)",
                            description: "المسمى الوظيفي للموظف باللغة العربية"
                        },
                        { 
                            display: "المسمى الوظيفي بالإنجليزية", 
                            value: "(name_Job_en)",
                            description: "المسمى الوظيفي للموظف باللغة الإنجليزية"
                        }
                    ]
                },
                // Responsible Person Information
                {
                    category: "معلومات المسؤول",
                    vars: [
                        { 
                            display: "الاسم الكامل بالعربية", 
                            value: "(full_name_ar)",
                            description: "اسم الشخص المسؤول باللغة العربية"
                        },
                        { 
                            display: "الاسم الكامل بالإنجليزية", 
                            value: "(full_name_en)",
                            description: "اسم الشخص المسؤول باللغة الإنجليزية"
                        },
                        { 
                            display: "المنصب بالعربية", 
                            value: "(role)",
                            description: "منصب الشخص المسؤول باللغة العربية"
                        },
                        { 
                            display: "المنصب بالإنجليزية", 
                            value: "(role_en)",
                            description: "منصب الشخص المسؤول باللغة الإنجليزية"
                        }
                    ]
                }
            ];

            let lastMarkerIndex = null;
            let lastMarkerFormat = {};
            let activeQuill = null;
            
            // Function to show suggestions at a given position
            function showSuggestions(quill, index, bounds) {
                activeQuill = quill;
                suggestionContainer.innerHTML = '';
                
                // Get the editor's container position
                const editorRect = quill.container.getBoundingClientRect();
                
                // Add search box
                const searchBox = document.createElement('div');
                searchBox.className = 'suggestion-search';
                const searchInput = document.createElement('input');
                searchInput.placeholder = 'البحث في المتغيرات...';
                
                // Store current scroll position and prevent scroll on focus
                const scrollPosition = window.scrollY;
                searchInput.addEventListener('focus', function(e) {
                    e.preventDefault();
                    window.scrollTo(0, scrollPosition);
                });
                
                searchBox.appendChild(searchInput);
                suggestionContainer.appendChild(searchBox);

                const variablesList = document.createElement('div');
                variablesList.className = 'suggestion-list';

                function renderVariables(searchTerm = '') {
                    variablesList.innerHTML = '';
                    const contractType = document.querySelector('.contract-type-btn.active')?.dataset.type;
                    
                    availableVariables.forEach(function(category) {
                        const filteredVars = category.vars.filter(v => {
                            // Check if variable should be shown based on contract type
                            if (v.extendedOnly && contractType !== '2') {
                                return false;
                            }
                            
                            return v.display.includes(searchTerm) ||
                                   v.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                   v.description.includes(searchTerm);
                        });

                        if (filteredVars.length > 0) {
                            // Add category header
                            const categoryHeader = document.createElement('div');
                            categoryHeader.className = 'suggestion-category';
                            categoryHeader.textContent = category.category;
                            variablesList.appendChild(categoryHeader);

                            // Add variables in this category
                            filteredVars.forEach(function(variable) {
                                const item = document.createElement('div');
                                item.className = 'suggestion-item';
                                
                                const header = document.createElement('div');
                                header.className = 'suggestion-header';
                                
                                const name = document.createElement('div');
                                name.className = 'suggestion-name';
                                name.textContent = variable.display;
                                
                                const value = document.createElement('code');
                                value.className = 'suggestion-value';
                                value.textContent = variable.value;
                                
                                header.appendChild(name);
                                header.appendChild(value);
                                
                                const description = document.createElement('div');
                                description.className = 'suggestion-description';
                                description.textContent = variable.description;
                                
                                item.appendChild(header);
                                item.appendChild(description);
                                
                                item.onclick = function(e) {
                                    e.preventDefault();
                                    const currentScrollPosition = window.scrollY;
                                    activeQuill.deleteText(lastMarkerIndex, 1);
                                    activeQuill.insertText(lastMarkerIndex, variable.value + ' ', lastMarkerFormat);
                                    activeQuill.setSelection(lastMarkerIndex + variable.value.length + 1);
                                    hideSuggestions();
                                    // Restore scroll position after a brief delay to ensure DOM updates are complete
                                    requestAnimationFrame(() => {
                                        window.scrollTo(0, currentScrollPosition);
                                    });
                                };
                                
                                variablesList.appendChild(item);
                            });
                        }
                    });
                }

                searchInput.addEventListener('input', function(e) {
                    renderVariables(e.target.value);
                });

                renderVariables(); // Initial render
                suggestionContainer.appendChild(variablesList);

                // Position the suggestion container relative to the cursor
                const cursorPosition = quill.getBounds(index);
                
                // Calculate absolute position of the cursor in the viewport
                const absoluteTop = editorRect.top + cursorPosition.top + window.scrollY;
                const absoluteLeft = editorRect.left + cursorPosition.left;

                // Container dimensions
                const containerWidth = 500;
                const containerHeight = Math.min(400, window.innerHeight - 100);
                
                // Calculate available space
                const spaceBelow = window.innerHeight - (absoluteTop - window.scrollY + cursorPosition.height);
                const spaceAbove = absoluteTop - window.scrollY;

                // Determine vertical position
                let top;
                if (spaceBelow >= containerHeight || spaceBelow > spaceAbove) {
                    // Position below cursor
                    top = absoluteTop + cursorPosition.height;
                } else {
                    // Position above cursor
                    top = absoluteTop - containerHeight;
                }

                // Determine horizontal position
                let left = absoluteLeft;
                if (left + containerWidth > window.innerWidth) {
                    left = window.innerWidth - containerWidth - 20;
                }
                left = Math.max(20, left);

                // Apply positions
                suggestionContainer.style.top = `${top}px`;
                suggestionContainer.style.left = `${left}px`;
                suggestionContainer.style.maxHeight = `${containerHeight}px`;
                suggestionContainer.style.width = `${containerWidth}px`;

                // Show with animation
                suggestionContainer.style.opacity = '0';
                suggestionContainer.style.transform = 'scale(0.95)';
                suggestionContainer.style.display = 'block';
                suggestionContainer.offsetHeight; // Force reflow
                suggestionContainer.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
                suggestionContainer.style.opacity = '1';
                suggestionContainer.style.transform = 'scale(1)';

                // Focus search input without scrolling
                requestAnimationFrame(() => {
                    searchInput.focus();
                    window.scrollTo(0, scrollPosition);
                });
            }
            
            function hideSuggestions() {
                if (suggestionContainer.style.display !== 'none') {
                    suggestionContainer.style.opacity = '0';
                    suggestionContainer.style.transform = 'scale(0.95)';
                    
                    // Wait for animation to complete before hiding
                    setTimeout(() => {
                        suggestionContainer.style.display = 'none';
                        lastMarkerIndex = null;
                        activeQuill = null;
                    }, 200);
                }
            }
            
            // Function to setup variable suggestions for a Quill editor
            function setupVariableSuggestions(quill) {
                quill.on('text-change', function(delta, oldDelta, source) {
                    if (source === 'user') {
                        delta.ops.forEach(function(op) {
                            if (op.insert && op.insert === '(') {
                                const selection = quill.getSelection();
                                if (selection) {
                                    lastMarkerIndex = selection.index - 1; // index where '(' was inserted
                                    lastMarkerFormat = quill.getFormat(lastMarkerIndex, 1); // capture formatting of '('
                                    const bounds = quill.getBounds(lastMarkerIndex);
                                    showSuggestions(quill, lastMarkerIndex, bounds);
                                }
                            }
                        });
                    }
                });
            }
            
            // Setup variable suggestions for both editors
            setupVariableSuggestions(quill);
            
            // When left editor is initialized, setup variable suggestions for it too
            document.addEventListener('DOMContentLoaded', function() {
                if (quillLeft) {
                    setupVariableSuggestions(quillLeft);
                }
            });
            
            // Setup variable suggestions for left editor when it's created
            const originalToggleSplitPage = toggleSplitPage;
            toggleSplitPage = function() {
                originalToggleSplitPage();
                if (quillLeft && !quillLeft._variableSuggestionsSetup) {
                    setupVariableSuggestions(quillLeft);
                    quillLeft._variableSuggestionsSetup = true;
                }
            };
            
            // Hide suggestions when clicking outside the suggestion container
            document.addEventListener('click', function(e) {
                if (!suggestionContainer.contains(e.target)) {
                    hideSuggestions();
                }
            });
        })();
        // End variable suggestion feature
    </script>
    <!-- Add this script at the end of the file, before the closing </body> tag -->
    <script>
        // Contract type selection handling
        document.addEventListener('DOMContentLoaded', function() {
            const contractTypeBtns = document.querySelectorAll('.contract-type-btn');
            const editorWrapper = document.querySelector('.editor-wrapper');
            
            // Disable the editor initially
            editorWrapper.classList.add('editor-disabled');
            
            contractTypeBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // If already active, do nothing
                    if (this.classList.contains('active')) return;
                    
                    // Confirm if there's content in the editor
                    const hasContent = quill.getLength() > 1 || (quillLeft && quillLeft.getLength() > 1);
                    if (hasContent) {
                        if (confirm('سيؤدي تغيير نوع العقد إلى مسح جميع المحتوى. هل تريد المتابعة؟')) {
                            resetEditor();
                            updateContractTypeSelection(this);
                        }
                    } else {
                        updateContractTypeSelection(this);
                    }
                });
            });
            
            function updateContractTypeSelection(selectedBtn) {
                // Remove active class from all buttons
                contractTypeBtns.forEach(btn => btn.classList.remove('active'));
                // Add active class to selected button
                selectedBtn.classList.add('active');
                
                // Enable the editor
                editorWrapper.classList.remove('editor-disabled');
                
                // Refresh variables list if it's currently shown
                if (suggestionContainer.style.display !== 'none') {
                    const searchInput = suggestionContainer.querySelector('.suggestion-search input');
                    renderVariables(searchInput.value);
                }
            }
            
            function resetEditor() {
                // Reset main editor
                quill.setText('');
                
                // Reset left editor if it exists
                if (quillLeft) {
                    quillLeft.setText('');
                }
                
                // Reset split page if active
                const a4Editor = document.querySelector('.a4-editor');
                if (a4Editor.classList.contains('split-page')) {
                    toggleSplitPage();
                }
                
                // Reset margins to default
                document.getElementById('marginTop').value = '5';
                document.getElementById('marginBottom').value = '5';
                document.getElementById('marginRight').value = '5';
                document.getElementById('marginLeft').value = '5';
                updateFormat();
                
                // Reset filename
                document.getElementById('templateFileName').value = '';
            }
        });
        
        // Function to load a restored template
        function loadRestoredTemplate() {
            try {
                <?php if ($restoredTemplate): ?>
                // Get the restored template data
                const restoredData = <?php echo json_encode($restoredTemplate); ?>;
                
                // Set the template name
                document.getElementById('templateFileName').value = restoredData.name;
                
                // Select the correct contract type
                const contractType = restoredData.type;
                const contractTypeBtn = document.querySelector(`.contract-type-btn[data-type="${contractType}"]`);
                if (contractTypeBtn) {
                    // Simulate a click on the contract type button
                    contractTypeBtn.click();
                }
                
                // Set the margins
                if (restoredData.styles && restoredData.styles.margins) {
                    const margins = restoredData.styles.margins;
                    if (document.getElementById('marginTop')) document.getElementById('marginTop').value = margins.top;
                    if (document.getElementById('marginBottom')) document.getElementById('marginBottom').value = margins.bottom;
                    if (document.getElementById('marginRight')) document.getElementById('marginRight').value = margins.right;
                    if (document.getElementById('marginLeft')) document.getElementById('marginLeft').value = margins.left;
                    updateFormat();
                }
                
                // Check if it's a split page template
                const isSplitPage = restoredData.styles && restoredData.styles.isSplitPage;
                if (isSplitPage && !document.querySelector('.a4-editor').classList.contains('split-page')) {
                    toggleSplitPage();
                }
                
                // Load the content
                if (restoredData.template) {
                    // Check if it's a split page content
                    if (isSplitPage && restoredData.template.includes('split-content')) {
                        // Extract right and left content
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = restoredData.template;
                        
                        const rightContentDiv = tempDiv.querySelector('.right-content');
                        const leftContentDiv = tempDiv.querySelector('.left-content');
                        
                        if (rightContentDiv) {
                            quill.root.innerHTML = rightContentDiv.innerHTML;
                        }
                        
                        if (leftContentDiv && quillLeft) {
                            quillLeft.root.innerHTML = leftContentDiv.innerHTML;
                        }
                    } else {
                        // Regular content
                        quill.root.innerHTML = restoredData.template;
                    }
                }
                
                // Check if we're in update mode
                <?php if ($updateTemplateId): ?>
                // Configure the update button with the template ID
                const updateTemplateBtn = document.getElementById('updateTemplateBtn');
                updateTemplateBtn.dataset.templateId = <?php echo json_encode($updateTemplateId); ?>;
                
                // Show update button and hide save button
                updateTemplateBtn.style.display = 'inline-flex';
                document.getElementById('saveTemplateBtn').style.display = 'none';
                
                // Show a success message
                alert('✅ تم استعادة القالب بنجاح. يمكنك الآن تحريره وتحديثه.');
                <?php else: ?>
                // Show a success message for regular restore
                alert('✅ تم استعادة القالب بنجاح. يمكنك الآن تحريره وحفظه.');
                <?php endif; ?>
                <?php endif; ?>
            } catch (error) {
                console.error('Error loading restored template:', error);
                alert('❌ حدث خطأ أثناء استعادة القالب: ' + error.message);
            }
        }
    </script>
</body>
</html>
