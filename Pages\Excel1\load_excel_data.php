<?php
// Define the file paths
$dataFilePath = __DIR__ . '/data/excel_data.json';
$metadataFilePath = __DIR__ . '/data/excel_metadata.json';

// Initialize response array
$response = [
    'data' => [],
    'metadata' => null
];

// Load data
if (file_exists($dataFilePath)) {
    $tableData = file_get_contents($dataFilePath);
    $response['data'] = json_decode($tableData, true) ?: [];
}

// Load metadata
if (file_exists($metadataFilePath)) {
    $tableMetadata = file_get_contents($metadataFilePath);
    $response['metadata'] = json_decode($tableMetadata, true);
}

// Set the content type to JSON
header('Content-Type: application/json');

// Return the combined data
echo json_encode($response);
?> 