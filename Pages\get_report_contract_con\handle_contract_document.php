<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Handle file upload
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['contract_document'])) {
        $contract_id = $_POST['contract_id'];
        $type = $_POST['type'];
        $file = $_FILES['contract_document'];

        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload failed with error code: ' . $file['error']);
        }

        if ($file['type'] !== 'application/pdf') {
            throw new Exception('Only PDF files are allowed');
        }

        // Read file content
        $content = file_get_contents($file['tmp_name']);
        if ($content === false) {
            throw new Exception('Failed to read file content');
        }

        // Prepare and execute update query
        if ($type === 'main') {
            $stmt = $conn->prepare("UPDATE contract SET signed_contract_document = ? WHERE id_contract = ?");
        } else {
            $stmt = $conn->prepare("UPDATE extension_contract SET signed_extension_contract_document = ? WHERE id_extension_contract = ?");
        }

        if (!$stmt) {
            throw new Exception('Failed to prepare statement: ' . $conn->error);
        }

        $stmt->bind_param("si", $content, $contract_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to update database: ' . $stmt->error);
        }

        $stmt->close();

        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'تم رفع المستند بنجاح',
            'type' => $type,
            'contract_id' => $contract_id
        ]);

    } else if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['contract_id']) && isset($_GET['type'])) {
        // Handle file download
        $contract_id = $_GET['contract_id'];
        $type = $_GET['type'];
        $action = isset($_GET['action']) ? $_GET['action'] : 'download'; // Default to download

        if ($type === 'main') {
            $stmt = $conn->prepare("SELECT signed_contract_document FROM contract WHERE id_contract = ?");
        } else {
            $stmt = $conn->prepare("SELECT signed_extension_contract_document FROM extension_contract WHERE id_extension_contract = ?");
        }

        if (!$stmt) {
            throw new Exception('Failed to prepare statement: ' . $conn->error);
        }

        $stmt->bind_param("i", $contract_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to execute query: ' . $stmt->error);
        }

        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        if (!$row || !$row[$type === 'main' ? 'signed_contract_document' : 'signed_extension_contract_document']) {
            throw new Exception('No document found');
        }

        $document = $row[$type === 'main' ? 'signed_contract_document' : 'signed_extension_contract_document'];

        header('Content-Type: application/pdf');
        if ($action === 'view') {
            // For viewing in browser, use inline content disposition
            header('Content-Disposition: inline; filename="contract_document.pdf"');
        } else {
            // For downloading, use attachment content disposition
            header('Content-Disposition: attachment; filename="contract_document.pdf"');
        }
        echo $document;
        exit;
    }

} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?> 