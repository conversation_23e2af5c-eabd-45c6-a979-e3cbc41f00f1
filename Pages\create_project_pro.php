<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// معالجة طلب AJAX للحصول على معلومات المشروع
if (isset($_GET['action']) && $_GET['action'] === 'get_project_data') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid project ID');
        }

        $projectId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // الحصول على بيانات المشروع
        $stmt = $db->prepare("SELECT * FROM Project WHERE id_Project = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed");
        }

        $stmt->execute([$projectId]);
        $project = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($project) {
            // تنسيق التواريخ لعرضها في حقول التاريخ بالنموذج
            if (!empty($project['Project_start_date'])) {
                $project['Project_start_date'] = date('Y-m-d', strtotime($project['Project_start_date']));
            }
            if (!empty($project['Project_end_date'])) {
                $project['Project_end_date'] = date('Y-m-d', strtotime($project['Project_end_date']));
            }
            echo json_encode($project);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Project not found']);
        }
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

$error_message = '';
$success_message = '';
$project_name = '';
$project_name_en = '';
$start_date = '';
$end_date = '';
$project_description = '';
$project_description_en = '';
$selected_administrator = '';
$selected_organization = '';
$selected_area = '';
$selected_funder = '';

// Arrays to store dropdown options
$administrators = [];
$implementing_organizations = [];
$target_areas = [];
$funders = [];
// جلب قائمة المشاريع
$projects = [];

try {
    // Read the database connection details from the text file
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    // Create database connection
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch active administrators
    $stmt = $db->query("SELECT id_administrators, full_name_ar FROM administrators WHERE status = 1");
    $administrators = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch active implementing organizations
    $stmt = $db->query("SELECT id_implementing_organizations, name_ar FROM implementing_organizations WHERE status = 1");
    $implementing_organizations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch active target areas
    $stmt = $db->query("SELECT id_target_areas, name_ar FROM target_areas WHERE status = 1");
    $target_areas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch active funders
    $stmt = $db->query("SELECT id_funders, name_ar FROM funders WHERE status = 1");
    $funders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Fetch projects list for the dropdown
    $stmt = $db->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1 ORDER BY Project_name");
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Validate input data
        $project_id = isset($_POST['project_id']) ? intval($_POST['project_id']) : 0;
        $is_update = $project_id > 0;
        
        $project_name = trim($_POST['project_name'] ?? '');
        $project_name_en = trim($_POST['project_name_en'] ?? '');
        $start_date = trim($_POST['start_date'] ?? '');
        $end_date = trim($_POST['end_date'] ?? '');
        $project_description = trim($_POST['project_description'] ?? '');
        $project_description_en = trim($_POST['project_description_en'] ?? '');
        $selected_administrator = trim($_POST['administrator'] ?? '');
        $selected_organization = trim($_POST['organization'] ?? '');
        $selected_area = trim($_POST['area'] ?? '');
        $selected_funder = trim($_POST['funder'] ?? '');
        
        $validation_errors = [];
        
        // Validate required fields
        if (empty($project_name)) {
            $validation_errors[] = 'يرجى إدخال اسم المشروع بالعربية';
        }
        if (empty($project_name_en)) {
            $validation_errors[] = 'يرجى إدخال اسم المشروع بالإنجليزية';
        }
        if (empty($start_date)) {
            $validation_errors[] = 'يرجى إدخال تاريخ بدء المشروع';
        } elseif (!strtotime($start_date)) {
            $validation_errors[] = 'تاريخ بدء المشروع غير صالح';
        }
        if (!empty($end_date) && !strtotime($end_date)) {
            $validation_errors[] = 'تاريخ نهاية المشروع غير صالح';
        } elseif (!empty($end_date) && strtotime($end_date) < strtotime($start_date)) {
            $validation_errors[] = 'تاريخ نهاية المشروع يجب أن يكون بعد تاريخ البدء';
        }
        if (empty($selected_administrator)) {
            $validation_errors[] = 'يرجى اختيار المسؤول الإداري';
        }
        if (empty($selected_organization)) {
            $validation_errors[] = 'يرجى اختيار المنظمة المنفذة';
        }
        if (empty($selected_area)) {
            $validation_errors[] = 'يرجى اختيار المنطقة المستهدفة';
        }
        if (empty($selected_funder)) {
            $validation_errors[] = 'يرجى اختيار الجهة الممولة';
        }
        
        if (!empty($validation_errors)) {
            $error_message = "أخطاء التحقق:<br>" . implode("<br>", $validation_errors);
        } else {
            // Format dates
            $formatted_start_date = date('Y-m-d H:i:s', strtotime($start_date));
            $formatted_end_date = !empty($end_date) ? date('Y-m-d H:i:s', strtotime($end_date)) : null;
            
            if ($is_update) {
                // Update existing project
                $sql = "UPDATE Project SET 
                    Project_name = :name, 
                    Project_name_en = :name_en, 
                    Project_start_date = :start_date, 
                    Project_end_date = :end_date,
                    id_administrators = :administrator, 
                    id_implementing_organizations = :organization, 
                    id_target_areas = :area, 
                    id_funders = :funder,
                    Project_description = :description, 
                    Project_description_en = :description_en
                WHERE id_Project = :project_id";
                
                $stmt = $db->prepare($sql);
                $params = [
                    ':name' => $project_name,
                    ':name_en' => $project_name_en,
                    ':start_date' => $formatted_start_date,
                    ':end_date' => $formatted_end_date,
                    ':administrator' => $selected_administrator,
                    ':organization' => $selected_organization,
                    ':area' => $selected_area,
                    ':funder' => $selected_funder,
                    ':description' => $project_description,
                    ':description_en' => $project_description_en,
                    ':project_id' => $project_id
                ];
                
                if ($stmt->execute($params)) {
                    $success_message = 'تم تحديث المشروع بنجاح';
                    // Reset form data on success
                    $project_name = '';
                    $project_name_en = '';
                    $start_date = '';
                    $end_date = '';
                    $project_description = '';
                    $project_description_en = '';
                    $selected_administrator = '';
                    $selected_organization = '';
                    $selected_area = '';
                    $selected_funder = '';
                } else {
                    throw new Exception('فشل في تحديث المشروع');
                }
            } else {
                // Insert new project
                $sql = "INSERT INTO Project (
                    Project_name, Project_name_en, Project_status, Project_start_date, Project_end_date,
                    id_administrators, id_implementing_organizations, id_target_areas, id_funders,
                    Project_description, Project_description_en
                ) VALUES (
                    :name, :name_en, 1, :start_date, :end_date,
                    :administrator, :organization, :area, :funder,
                    :description, :description_en
                )";
                
                $stmt = $db->prepare($sql);
                $params = [
                    ':name' => $project_name,
                    ':name_en' => $project_name_en,
                    ':start_date' => $formatted_start_date,
                    ':end_date' => $formatted_end_date,
                    ':administrator' => $selected_administrator,
                    ':organization' => $selected_organization,
                    ':area' => $selected_area,
                    ':funder' => $selected_funder,
                    ':description' => $project_description,
                    ':description_en' => $project_description_en
                ];
                
                if ($stmt->execute($params)) {
                    $success_message = 'تم إنشاء المشروع بنجاح';
                    // Reset form data on success
                    $project_name = '';
                    $project_name_en = '';
                    $start_date = '';
                    $end_date = '';
                    $project_description = '';
                    $project_description_en = '';
                    $selected_administrator = '';
                    $selected_organization = '';
                    $selected_area = '';
                    $selected_funder = '';
                } else {
                    throw new Exception('فشل في حفظ المشروع');
                }
            }
        }
    }
} catch (PDOException $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء مشروع جديد - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Alert Info Styling */
        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
            background-color: var(--bg-info);
            color: var(--text-info);
            border-color: var(--border-info);
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Select2 Customization */
        .select2-container {
            width: 100% !important;
        }
        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 36px;
            padding-right: 12px;
            padding-left: 12px;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }
        .select2-dropdown {
            border: 1px solid #ced4da;
        }
        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }
        .select2-results__option {
            padding: 6px 12px;
        }

        /* Form Styling Improvements */
        .form-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(0,0,0,0.1);
        }

        [data-theme="dark"] .form-section {
            background-color: rgba(255,255,255,0.05);
            border-color: rgba(255,255,255,0.1);
        }

        .form-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--bs-primary);
            color: var(--bs-primary);
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-control, .form-select, .select2-container--default .select2-selection--single {
            border-radius: 8px;
            border: 1.5px solid #dee2e6;
            padding: 0.75rem 1rem;
            transition: all 0.2s ease-in-out;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--bs-primary);
            box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
        }

        .btn-primary {
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.2s ease-in-out;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .card {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
            border-radius: 12px;
        }

        .card-body {
            padding: 2rem;
        }

        .card-title {
            color: var(--bs-primary);
            font-weight: 700;
            margin-bottom: 2rem;
        }

        textarea.form-control {
            min-height: 120px;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Added informational message -->
                    <div class="alert alert-info mb-4" role="alert">
                        <i class="bi bi-info-circle me-2"></i>
                        مرحباً بك في صفحة إنشاء المشاريع. يمكنك من خلال هذه الصفحة إضافة مشاريع جديدة وتحديد تواريخ البدء والانتهاء لكل مشروع. المشاريع التي تقوم بإنشائها هنا ستكون متاحة لربطها بالعقود والتقارير لاحقاً.
                    </div>
                    
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title" id="page-title">إنشاء مشروع جديد</h5>
                            
                            <!-- Project Selection Section -->
                            <div class="form-section mb-4" id="project-selection-section">
                                <div class="form-section-title">
                                    <i class="bi bi-search me-2"></i>
                                    تحديث مشروع موجود
                                </div>
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="project_selector" class="form-label">اختر المشروع</label>
                                        <select class="form-select select2" id="project_selector">
                                            <option value="">اختر مشروعاً للتحديث</option>
                                            <?php foreach ($projects as $project): ?>
                                            <option value="<?php echo htmlspecialchars($project['id_Project']); ?>">
                                                <?php echo htmlspecialchars($project['Project_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3 d-flex align-items-end">
                                        <button type="button" id="viewProjectBtn" class="btn btn-info me-2" disabled>
                                            <i class="bi bi-eye-fill"></i> عرض البيانات
                                        </button>
                                        <button type="button" id="resetFormBtn" class="btn btn-secondary">
                                            <i class="bi bi-arrow-repeat"></i> إضافة جديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <form method="POST" action="">
                                <!-- Hidden field for project ID when updating -->
                                <input type="hidden" id="project_id" name="project_id" value="0">
                                
                                <!-- Basic Information Section -->
                                <div class="form-section">
                                    <div class="form-section-title">
                                        <i class="bi bi-info-circle me-2"></i>
                                        المعلومات الأساسية
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="project_name" class="form-label">اسم المشروع بالعربية</label>
                                            <input type="text" class="form-control" id="project_name" name="project_name" 
                                                   value="<?php echo htmlspecialchars($project_name); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="project_name_en" class="form-label">اسم المشروع بالإنجليزية</label>
                                            <input type="text" class="form-control" id="project_name_en" name="project_name_en" 
                                                   value="<?php echo htmlspecialchars($project_name_en); ?>" required>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="start_date" class="form-label">تاريخ بدء المشروع</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                                   value="<?php echo htmlspecialchars($start_date); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="end_date" class="form-label">تاريخ نهاية المشروع (اختياري)</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                                   value="<?php echo htmlspecialchars($end_date); ?>">
                                        </div>
                                    </div>
                                </div>

                                <!-- Project Description Section -->
                                <div class="form-section">
                                    <div class="form-section-title">
                                        <i class="bi bi-file-text me-2"></i>
                                        وصف المشروع
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="project_description" class="form-label">وصف المشروع بالعربية</label>
                                            <textarea class="form-control" id="project_description" name="project_description" 
                                                      rows="4"><?php echo htmlspecialchars($project_description); ?></textarea>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="project_description_en" class="form-label">وصف المشروع بالإنجليزية</label>
                                            <textarea class="form-control" id="project_description_en" name="project_description_en" 
                                                      rows="4"><?php echo htmlspecialchars($project_description_en); ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Project Stakeholders Section -->
                                <div class="form-section">
                                    <div class="form-section-title">
                                        <i class="bi bi-people me-2"></i>
                                        الجهات المعنية
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="administrator" class="form-label">المسؤول الإداري</label>
                                            <select class="form-select" id="administrator" name="administrator" required>
                                                <option value="">اختيار المسؤول الإداري</option>
                                                <?php foreach ($administrators as $administrator): ?>
                                                    <option value="<?php echo htmlspecialchars($administrator['id_administrators']); ?>"
                                                            <?php echo $selected_administrator == $administrator['id_administrators'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($administrator['full_name_ar']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="organization" class="form-label">المنظمة المنفذة</label>
                                            <select class="form-select" id="organization" name="organization" required>
                                                <option value="">اختيار المنظمة المنفذة</option>
                                                <?php foreach ($implementing_organizations as $organization): ?>
                                                    <option value="<?php echo htmlspecialchars($organization['id_implementing_organizations']); ?>"
                                                            <?php echo $selected_organization == $organization['id_implementing_organizations'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($organization['name_ar']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="area" class="form-label">المنطقة المستهدفة</label>
                                            <select class="form-select" id="area" name="area" required>
                                                <option value="">اختيار المنطقة المستهدفة</option>
                                                <?php foreach ($target_areas as $area): ?>
                                                    <option value="<?php echo htmlspecialchars($area['id_target_areas']); ?>"
                                                            <?php echo $selected_area == $area['id_target_areas'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($area['name_ar']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="funder" class="form-label">الجهة الممولة</label>
                                            <select class="form-select" id="funder" name="funder" required>
                                                <option value="">اختيار الجهة الممولة</option>
                                                <?php foreach ($funders as $funder): ?>
                                                    <option value="<?php echo htmlspecialchars($funder['id_funders']); ?>"
                                                            <?php echo $selected_funder == $funder['id_funders'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($funder['name_ar']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" id="saveBtn" class="btn btn-primary btn-lg">
                                        <i class="bi bi-plus-circle me-2"></i>
                                        إنشاء المشروع
                                    </button>
                                    <button type="submit" id="updateBtn" class="btn btn-success btn-lg" style="display:none;">
                                        <i class="bi bi-pencil-square me-2"></i>
                                        تحديث المشروع
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for all dropdown lists
            $('#administrator').select2({
                placeholder: 'اختيار المسؤول الإداري',
                allowClear: true,
                dir: 'rtl',
                language: 'ar'
            });
            
            $('#organization').select2({
                placeholder: 'اختيار المنظمة المنفذة',
                allowClear: true,
                dir: 'rtl',
                language: 'ar'
            });
            
            $('#area').select2({
                placeholder: 'اختيار المنطقة المستهدفة',
                allowClear: true,
                dir: 'rtl',
                language: 'ar'
            });
            
            $('#funder').select2({
                placeholder: 'اختيار الجهة الممولة',
                allowClear: true,
                dir: 'rtl',
                language: 'ar'
            });
            
            // Initialize project selector with Select2
            $('#project_selector').select2({
                placeholder: 'اختر مشروعاً للتحديث',
                allowClear: true,
                dir: 'rtl',
                language: 'ar'
            });
            
            // Enable View button when project is selected
            $('#project_selector').on('change', function() {
                if ($(this).val()) {
                    $('#viewProjectBtn').prop('disabled', false);
                } else {
                    $('#viewProjectBtn').prop('disabled', true);
                }
            });
            
            // View Project button click
            $('#viewProjectBtn').on('click', function() {
                const projectId = $('#project_selector').val();
                if (!projectId) return;
                
                // Show loading indicator
                $(this).html('<i class="bi bi-hourglass-split"></i> جاري التحميل...');
                $(this).prop('disabled', true);
                
                // Fetch project data
                $.ajax({
                    url: 'create_project_pro.php',
                    data: {
                        action: 'get_project_data',
                        id: projectId
                    },
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Populate form fields
                        $('#project_id').val(data.id_Project);
                        $('#project_name').val(data.Project_name);
                        $('#project_name_en').val(data.Project_name_en);
                        $('#start_date').val(data.Project_start_date);
                        $('#end_date').val(data.Project_end_date || '');
                        $('#project_description').val(data.Project_description);
                        $('#project_description_en').val(data.Project_description_en);
                        
                        // Set dropdown values
                        $('#administrator').val(data.id_administrators).trigger('change');
                        $('#organization').val(data.id_implementing_organizations).trigger('change');
                        $('#area').val(data.id_target_areas).trigger('change');
                        $('#funder').val(data.id_funders).trigger('change');
                        
                        // Update form UI
                        $('#saveBtn').hide();
                        $('#updateBtn').show();
                        $('#page-title').text('تحديث بيانات المشروع');
                        
                        // Reset view button
                        $('#viewProjectBtn').html('<i class="bi bi-eye-fill"></i> عرض البيانات');
                        $('#viewProjectBtn').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        // Show error
                        alert('حدث خطأ أثناء استرجاع بيانات المشروع: ' + (xhr.responseJSON?.error || error));
                        
                        // Reset view button
                        $('#viewProjectBtn').html('<i class="bi bi-eye-fill"></i> عرض البيانات');
                        $('#viewProjectBtn').prop('disabled', false);
                    }
                });
            });
            
            // Reset Form button click
            $('#resetFormBtn').on('click', function() {
                resetForm();
            });
            
            // Helper function to reset form
            function resetForm() {
                // Reset project ID
                $('#project_id').val('0');
                
                // Reset form fields
                document.querySelector('form').reset();
                
                // Reset Select2 dropdowns
                $('#administrator').val('').trigger('change');
                $('#organization').val('').trigger('change');
                $('#area').val('').trigger('change');
                $('#funder').val('').trigger('change');
                $('#project_selector').val('').trigger('change');
                
                // Reset button visibility
                $('#saveBtn').show();
                $('#updateBtn').hide();
                
                // Reset form title
                $('#page-title').text('إنشاء مشروع جديد');
            }
        });
    </script>
</body>
</html>


