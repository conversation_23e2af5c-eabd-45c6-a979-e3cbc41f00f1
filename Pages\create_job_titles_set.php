<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

$error_message = '';
$success_message = '';
$name_Job = '';
$name_Job_en = '';
$tasks = [['taskName' => '', 'taskNameEn' => '']];
$job_titles = [];
$editing_mode = false;
$edit_id = null;

// Get database connection
function getDBConnection() {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    return $conn;
}

// Load job titles for dropdown
try {
    $conn = getDBConnection();
    $result = $conn->query("SELECT id_Job_titles, name_Job, name_Job_en FROM Job_titles ORDER BY name_Job");
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $job_titles[] = [
                'id' => $row['id_Job_titles'],
                'name' => $row['name_Job'],
                'name_en' => $row['name_Job_en']
            ];
        }
        $result->free();
    }
    $conn->close();
} catch (Exception $e) {
    $error_message = "خطأ في قراءة المسميات الوظيفية: " . $e->getMessage();
}

// Handle GET request to load a job title
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    try {
        $edit_id = intval($_GET['id']);
        $conn = getDBConnection();
        
        $stmt = $conn->prepare("SELECT * FROM Job_titles WHERE id_Job_titles = ?");
        $stmt->bind_param("i", $edit_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $editing_mode = true;
            $name_Job = $row['name_Job'];
            $name_Job_en = $row['name_Job_en'];
            
            // Parse the saved tasks from JSON
            $data = json_decode($row['data_todo_list_Job'], true);
            if (isset($data['jobDetails']['tasks']) && is_array($data['jobDetails']['tasks'])) {
                $tasks = array_map(function($task) {
                    return [
                        'taskName' => $task['taskName'],
                        'taskNameEn' => $task['taskNameEn']
                    ];
                }, $data['jobDetails']['tasks']);
            }
        } else {
            $error_message = "المسمى الوظيفي غير موجود";
        }
        
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $error_message = "خطأ في قراءة بيانات المسمى الوظيفي: " . $e->getMessage();
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $conn = getDBConnection();
        
        // Validate input data
        $name_Job = trim($_POST['name_Job'] ?? '');
        $name_Job_en = trim($_POST['name_Job_en'] ?? '');
        $tasks_ar = isset($_POST['tasks_ar']) ? array_filter($_POST['tasks_ar'], 'trim') : [];
        $tasks_en = isset($_POST['tasks_en']) ? array_filter($_POST['tasks_en'], 'trim') : [];
        $edit_id = isset($_POST['edit_id']) ? intval($_POST['edit_id']) : null;
        
        $validation_errors = [];
        
        // Validate job names
        if (empty($name_Job)) {
            $validation_errors[] = 'يرجى إدخال المسمى الوظيفي بالعربية';
        }
        if (empty($name_Job_en)) {
            $validation_errors[] = 'يرجى إدخال المسمى الوظيفي بالإنجليزية';
        }
        
        // Validate tasks
        if (empty($tasks_ar) || empty($tasks_en)) {
            $validation_errors[] = 'يرجى إضافة مهمة واحدة على الأقل';
        } else {
            foreach ($tasks_ar as $index => $task) {
                if (empty(trim($task))) {
                    $validation_errors[] = "المهمة رقم " . ($index + 1) . " فارغة باللغة العربية";
                }
            }
            foreach ($tasks_en as $index => $task) {
                if (empty(trim($task))) {
                    $validation_errors[] = "المهمة رقم " . ($index + 1) . " فارغة باللغة الإنجليزية";
                }
            }
        }
        
        if (!empty($validation_errors)) {
            $error_message = "أخطاء التحقق:<br>" . implode("<br>", $validation_errors);
        } else {
            // Prepare the JSON data structure
            $data_todo_list_job = [
                'evaluation' => [
                    'percentageEvaluation' => '-',
                    'workDaysEvaluation' => '-'
                ],
                'jobDetails' => [
                    'tasks' => array_map(function($ar, $en) {
                        return [
                            'taskName' => trim($ar),
                            'taskNameEn' => trim($en),
                            'completionRate' => 0
                        ];
                    }, $tasks_ar, $tasks_en)
                ]
            ];
            
            $jsonData = json_encode($data_todo_list_job);
            if ($jsonData === false) {
                throw new Exception("خطأ في تحويل البيانات إلى JSON: " . json_last_error_msg());
            }
            
            // Check if we're updating or inserting
            if ($edit_id) {
                // Update existing record
                $stmt = $conn->prepare("UPDATE Job_titles SET name_Job = ?, name_Job_en = ?, data_todo_list_Job = ? WHERE id_Job_titles = ?");
                if (!$stmt) {
                    throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                }
                
                $stmt->bind_param("sssi", $name_Job, $name_Job_en, $jsonData, $edit_id);
                if (!$stmt->execute()) {
                    throw new Exception("خطأ في تحديث البيانات: " . $stmt->error);
                }
                
                $success_message = 'تم تحديث المسمى الوظيفي بنجاح';
            } else {
                // Insert new record
                $stmt = $conn->prepare("INSERT INTO Job_titles (name_Job, name_Job_en, data_todo_list_Job) VALUES (?, ?, ?)");
                if (!$stmt) {
                    throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                }
                
                $stmt->bind_param("sss", $name_Job, $name_Job_en, $jsonData);
                if (!$stmt->execute()) {
                    throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
                }
                
                $success_message = 'تم إضافة المسمى الوظيفي بنجاح';
            }
            
            // Reset form data on success if not in edit mode
            if (!$edit_id) {
                $name_Job = '';
                $name_Job_en = '';
                $tasks = [['taskName' => '', 'taskNameEn' => '']];
                $editing_mode = false;
            }
            
            $stmt->close();
        }
    } catch (Exception $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    } finally {
        if (isset($conn)) {
            $conn->close();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المسميات الوظيفية - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css" rel="stylesheet">

    <style>
        :root {
            --font-family: 'Cairo', sans-serif;
        }
        
        body {
            font-family: var(--font-family);
        }
        
        /* Alert styling */
        .alert-info {
            margin: -0.5rem; /* Align with card body padding */
            border-radius: 0 0 8px 8px; /* Round only bottom corners */
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        .task-list-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: var(--bg-input);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .task-list-scrollable {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .task-list-scrollable::-webkit-scrollbar {
            width: 8px;
        }

        .task-list-scrollable::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        .task-item {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-item:last-child {
            margin-bottom: 0;
        }

        .task-input-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex: 1;
        }

        .task-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        /* Search dropdown style */
        .select-job-container {
            margin-bottom: 2rem;
            padding: 1rem;
            background-color: var(--bg-card);
            border-radius: 0.375rem;
            border: 1px solid var(--border-color);
        }
        
        .select-job-container .form-select {
            max-width: 100%;
        }
        
        .select-job-container .input-group {
            max-width: 600px;
        }
        
        /* Custom styling for the search input */
        .search-select-container {
            position: relative;
        }
        
        .search-input {
            padding-right: 40px;
        }
        
        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            pointer-events: none;
        }
        
        .job-selection-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        /* Select2 customization */
        .select2-container--bootstrap-5 .select2-selection {
            border-color: var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--text-color);
            padding-right: 10px;
        }
        
        .select2-container--bootstrap-5 .select2-dropdown {
            border-color: var(--border-color);
            background-color: var(--bg-input);
        }
        
        .select2-container--bootstrap-5 .select2-results__option {
            color: var(--text-color);
            padding: 8px 12px;
        }
        
        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--primary-color);
            color: #fff;
        }
        
        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: var(--secondary-color);
            color: #fff;
        }
        
        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            border-color: var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
        }
        
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection {
            background-color: var(--dark-bg-input);
            border-color: var(--dark-border-color);
            color: var(--dark-text-color);
        }
        
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--dark-bg-input);
            border-color: var(--dark-border-color);
        }
        
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--dark-text-color);
        }
        
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__option {
            color: var(--dark-text-color);
        }
        
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            background-color: var(--dark-bg-input);
            border-color: var(--dark-border-color);
            color: var(--dark-text-color);
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Updated informational message -->
                    <div class="alert alert-info mb-4" role="alert">
                        <i class="bi bi-info-circle me-2"></i>
                        مرحباً بك في صفحة إدارة المسميات الوظيفية. يمكنك من خلال هذه الصفحة إضافة مسميات وظيفية جديدة أو تعديل المسميات الموجودة وتحديد المهام والمسؤوليات المرتبطة بكل وظيفة باللغتين العربية والإنجليزية.
                    </div>
                    
                    <!-- Job Title Selection Section -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title mb-4">تحديد مسمى وظيفي للتعديل</h5>
                            
                            <div class="select-job-container">
                                <div class="row align-items-end">
                                    <div class="col-md-8">
                                        <label for="jobTitleSelect" class="form-label">اختر مسمى وظيفي</label>
                                        <div class="search-select-container mb-3">
                                            <select class="form-select select2-search" id="jobTitleSelect">
                                                <option value="">اختر مسمى وظيفي...</option>
                                                <?php foreach ($job_titles as $job): ?>
                                                <option value="<?php echo $job['id']; ?>" <?php echo ($edit_id == $job['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($job['name']); ?> (<?php echo htmlspecialchars($job['name_en']); ?>)
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4 job-selection-controls">
                                        <button type="button" id="selectJobBtn" class="btn btn-primary">
                                            <i class="bi bi-check-lg"></i> اختيار
                                        </button>
                                        <button type="button" id="clearFormBtn" class="btn btn-outline-secondary">
                                            <i class="bi bi-x-lg"></i> إلغاء التحديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title mb-4" id="formTitle">
                                <?php echo $editing_mode ? 'تعديل المسمى الوظيفي' : 'إضافة مسمى وظيفي جديد'; ?>
                            </h5>
                            
                            <form method="POST" action="" id="jobTitleForm">
                                <?php if ($editing_mode): ?>
                                <input type="hidden" name="edit_id" value="<?php echo $edit_id; ?>">
                                <?php endif; ?>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="name_Job" class="form-label">المسمى الوظيفي (عربي)</label>
                                        <input type="text" class="form-control" id="name_Job" name="name_Job" dir="rtl"
                                               value="<?php echo htmlspecialchars($name_Job ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="name_Job_en" class="form-label">Job Title (English)</label>
                                        <input type="text" class="form-control" id="name_Job_en" name="name_Job_en" dir="ltr"
                                               value="<?php echo htmlspecialchars($name_Job_en ?? ''); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">المهام الوظيفية</label>
                                    <div class="task-list-container">
                                        <div id="tasksContainer" class="task-list-scrollable">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary mt-2" id="addTask">
                                        <i class="bi bi-plus-lg"></i> إضافة مهمة جديدة
                                    </button>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <?php if ($editing_mode): ?>
                                    <button type="submit" class="btn btn-success" id="updateBtn">
                                        <i class="bi bi-save"></i> تحديث المعلومات
                                    </button>
                                    <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-outline-secondary">
                                        <i class="bi bi-plus-lg"></i> إضافة مسمى جديد
                                    </a>
                                    <?php else: ?>
                                    <button type="submit" class="btn btn-primary" id="saveBtn">
                                        <i class="bi bi-save"></i> حفظ المسمى الوظيفي
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Select2
            $(document).ready(function() {
                $('#jobTitleSelect').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    dir: 'rtl',
                    containerCssClass: 'form-select',
                    dropdownCssClass: 'select2-dropdown-rtl',
                    placeholder: 'اختر أو ابحث عن مسمى وظيفي...',
                    allowClear: true
                });
                
                // Update Select2 theme when the page theme changes
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.attributeName === 'data-theme') {
                            const theme = document.body.getAttribute('data-theme');
                            updateSelect2Theme(theme);
                        }
                    });
                });
                
                observer.observe(document.body, { attributes: true });
                
                function updateSelect2Theme(theme) {
                    // Force Select2 to re-render with new theme
                    $('#jobTitleSelect').select2('destroy').select2({
                        theme: 'bootstrap-5',
                        width: '100%',
                        dir: 'rtl',
                        containerCssClass: 'form-select',
                        dropdownCssClass: 'select2-dropdown-rtl',
                        placeholder: 'اختر أو ابحث عن مسمى وظيفي...',
                        allowClear: true
                    });
                }
            });

            // Task Management
            const addTaskBtn = document.getElementById('addTask');
            const tasksContainer = document.getElementById('tasksContainer');
            const selectJobBtn = document.getElementById('selectJobBtn');
            const jobTitleSelect = document.getElementById('jobTitleSelect');
            const clearFormBtn = document.getElementById('clearFormBtn');
            const formTitle = document.getElementById('formTitle');

            function createTaskItem(index, taskAr = '', taskEn = '') {
                const taskDiv = document.createElement('div');
                taskDiv.className = 'task-item';
                
                const taskLabel = document.createElement('div');
                taskLabel.className = 'task-number';
                taskLabel.textContent = `المهمة ${index + 1}`;
                taskLabel.style.marginBottom = '0.5rem';
                taskLabel.style.fontWeight = 'bold';
                taskLabel.style.color = 'var(--primary-color)';

                const inputContainer = document.createElement('div');
                inputContainer.className = 'task-input-container';

                const arInput = document.createElement('input');
                arInput.type = 'text';
                arInput.className = 'form-control';
                arInput.name = 'tasks_ar[]';
                arInput.value = taskAr;
                arInput.placeholder = 'اسم المهمة بالعربية';
                arInput.dir = 'rtl';
                arInput.required = true;

                const enInput = document.createElement('input');
                enInput.type = 'text';
                enInput.className = 'form-control';
                enInput.name = 'tasks_en[]';
                enInput.value = taskEn;
                enInput.placeholder = 'Task name in English';
                enInput.dir = 'ltr';
                enInput.required = true;

                const actionsContainer = document.createElement('div');
                actionsContainer.className = 'task-actions';

                const deleteBtn = document.createElement('button');
                deleteBtn.type = 'button';
                deleteBtn.className = 'btn btn-danger btn-sm';
                deleteBtn.innerHTML = '<i class="bi bi-trash"></i> حذف';
                deleteBtn.onclick = () => {
                    if (tasksContainer.children.length > 1) {
                        taskDiv.remove();
                        updateTaskNumbers();
                    }
                };

                inputContainer.appendChild(taskLabel);
                inputContainer.appendChild(arInput);
                inputContainer.appendChild(enInput);
                actionsContainer.appendChild(deleteBtn);
                inputContainer.appendChild(actionsContainer);
                taskDiv.appendChild(inputContainer);

                return taskDiv;
            }

            function updateTaskNumbers() {
                const taskItems = tasksContainer.children;
                Array.from(taskItems).forEach((item, index) => {
                    const label = item.querySelector('.task-number');
                    if (label) {
                        label.textContent = `المهمة ${index + 1}`;
                    }
                });
            }
            
            function loadTasks() {
                // Clear existing tasks
                tasksContainer.innerHTML = '';
                
                // Create tasks based on PHP data
                <?php if (!empty($tasks)): ?>
                <?php foreach ($tasks as $index => $task): ?>
                tasksContainer.appendChild(createTaskItem(
                    <?php echo $index; ?>, 
                    <?php echo json_encode($task['taskName'] ?? ''); ?>, 
                    <?php echo json_encode($task['taskNameEn'] ?? ''); ?>
                ));
                <?php endforeach; ?>
                <?php else: ?>
                // Add one empty task if no tasks exist
                tasksContainer.appendChild(createTaskItem(0));
                <?php endif; ?>
            }

            // Add event listener for select job button
            selectJobBtn.addEventListener('click', () => {
                const selectedId = jobTitleSelect.value;
                if (selectedId) {
                    window.location.href = `?id=${selectedId}`;
                }
            });
            
            // Add event listener for clear form button
            clearFormBtn.addEventListener('click', () => {
                window.location.href = window.location.pathname;
            });

            // Initialize tasks
            loadTasks();

            // Add task button event listener
            addTaskBtn.addEventListener('click', () => {
                const index = tasksContainer.children.length;
                tasksContainer.appendChild(createTaskItem(index));
            });
        });
    </script>
</body>
</html>


