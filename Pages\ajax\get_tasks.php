<?php
session_start();
header('Content-Type: application/json');

try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) throw new Exception('Error reading configuration file');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    $projectId = intval($_GET['project_id']);
    $assigneeType = $_GET['assignee_type'];
    $assigneeId = intval($_GET['assignee_id']);

    $sql = "SELECT id_TASKS, name_TASKS, name_TASKS_en 
            FROM tasks 
            WHERE id_Project = ? AND ";

    if ($assigneeType === 'employee') {
        $sql .= "id_employees = ?";
    } else {
        $sql .= "id_assigned = ?";
    }

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $projectId, $assigneeId);
    $stmt->execute();
    $result = $stmt->get_result();

    $tasks = [];
    while ($row = $result->fetch_assoc()) {
        $tasks[] = $row;
    }

    echo json_encode($tasks);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($conn)) $conn->close();
}
?> 