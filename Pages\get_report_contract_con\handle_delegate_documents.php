<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Check if all required parameters are provided
    if (!isset($_GET['delegate_id']) || !isset($_GET['document_type'])) {
        throw new Exception("Missing required parameters");
    }

    $delegate_id = (int)$_GET['delegate_id'];
    $document_type = $_GET['document_type'];
    $action = isset($_GET['action']) ? $_GET['action'] : 'download';

    // Validate document type
    $allowed_document_types = [
        'Identity_document',
        'test_results_document',
        'Interview_results_document',
        'guarantee_document',
        'medical_examination_document',
        'cv_document',
        'request_for_resignation_document',
        'reconciliation_document',
        'resignation_approval_document',
        'code_conduct'
    ];

    if (!in_array($document_type, $allowed_document_types)) {
        throw new Exception("Invalid document type");
    }

    // Get the document binary data
    $sql = "SELECT {$document_type} FROM assigned WHERE id_assigned = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Query preparation failed: " . $conn->error);
    }
    
    $stmt->bind_param("i", $delegate_id);
    
    if (!$stmt->execute()) {
        throw new Exception("Query execution failed: " . $stmt->error);
    }
    
    $stmt->bind_result($document_data);
    
    if (!$stmt->fetch()) {
        throw new Exception("Document not found");
    }
    
    $stmt->close();
    
    // Check if document exists
    if (!$document_data) {
        throw new Exception("Document not available");
    }
    
    // Stream the document
    $document_name = "document_{$delegate_id}_{$document_type}.pdf";
    
    if ($action === 'view') {
        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $document_name . '"');
    } else {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $document_name . '"');
    }
    
    header('Content-Length: ' . strlen($document_data));
    echo $document_data;
    exit;

} catch (Exception $e) {
    // Handle error
    if (isset($_GET['action']) && $_GET['action'] === 'view') {
        header('Content-Type: text/html; charset=utf-8');
        echo '<div style="text-align: center; font-family: Arial, sans-serif; margin-top: 50px;">';
        echo '<h2 style="color: #e74c3c;">خطأ</h2>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p>الرجاء المحاولة مرة أخرى لاحقاً أو الاتصال بمسؤول النظام.</p>';
        echo '</div>';
    } else {
        header('Content-Type: text/plain; charset=utf-8');
        echo 'Error: ' . $e->getMessage();
    }
} finally {
    if (isset($conn)) {
        $conn->close();
    }
} 