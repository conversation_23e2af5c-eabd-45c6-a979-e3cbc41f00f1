<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    // Read database connection details
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    // Create database connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Validate input parameters
    if (!isset($_GET['project_id']) || !isset($_GET['type'])) {
        throw new Exception('Missing required parameters');
    }

    $project_id = intval($_GET['project_id']);
    $type = $_GET['type'];

    if ($type === 'employees') {
        // Get employees that are not already assigned to tasks in this project
        $sql = "SELECT DISTINCT e.* 
                FROM employees e 
                WHERE e.status = 1 
                AND NOT EXISTS (
                    SELECT 1 
                    FROM tasks t 
                    WHERE t.id_Project = ? 
                    AND t.id_employees = e.id_employees 
                    AND t.status_TASKS = 1
                )";
    } elseif ($type === 'delegates') {
        // Get delegates that are not already assigned to tasks in this project
        $sql = "SELECT DISTINCT a.* 
                FROM assigned a 
                WHERE a.status = 1 
                AND a.id_Project = ?
                AND NOT EXISTS (
                    SELECT 1 
                    FROM tasks t 
                    WHERE t.id_Project = ? 
                    AND t.id_assigned = a.id_assigned 
                    AND t.status_TASKS = 1
                )";
    } else {
        throw new Exception('Invalid type parameter');
    }

    $stmt = $conn->prepare($sql);
    
    if ($type === 'employees') {
        $stmt->bind_param("i", $project_id);
    } else {
        $stmt->bind_param("ii", $project_id, $project_id);
    }

    if (!$stmt->execute()) {
        throw new Exception("Query execution failed: " . $stmt->error);
    }

    $result = $stmt->get_result();
    $data = [];

    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode($data);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?> 