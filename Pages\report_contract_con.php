<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Fetch all projects for the search dropdown
    $projects = [];
    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }

    // Fetch all job titles for the search dropdown
    $job_titles = [];
    $result = $conn->query("SELECT DISTINCT c.name_Job FROM contract c ORDER BY c.name_Job");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $job_titles[] = $row['name_Job'];
        }
    }

    $contract_data = null;
    $error_message = '';
    $success_message = '';

    // Handle search request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search'])) {
        $conditions = [];
        $params = [];
        $types = '';

        // Build search conditions
        if (!empty($_POST['project_id'])) {
            $conditions[] = "tmp.id_Project = ?";
            $params[] = $_POST['project_id'];
            $types .= 'i';
        }

        if (!empty($_POST['contract_type'])) {
            $conditions[] = "tmp.contract_type = ?";
            $params[] = $_POST['contract_type'];
            $types .= 'i';
        }

        if (!empty($_POST['name_Job'])) {
            $conditions[] = "tmp.name_Job = ?";
            $params[] = $_POST['name_Job'];
            $types .= 's';
        }

        if (!empty($_POST['name_ar_contract'])) {
            $searchTerm = '%' . $_POST['name_ar_contract'] . '%';
            $conditions[] = "(
                tmp.id_contract LIKE ? OR 
                tmp.name_ar_contract LIKE ? OR 
                DATE_FORMAT(tmp.version_date, '%d-%m-%Y') LIKE ? OR
                tmp.id_extension_contract LIKE ? OR 
                DATE_FORMAT(tmp.version_date, '%d-%m-%Y') LIKE ?
            )";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            $types .= 'sssss';
        }

        if (!empty($_POST['Identity_number_contract'])) {
            $conditions[] = "tmp.Identity_number_contract = ?";
            $params[] = $_POST['Identity_number_contract'];
            $types .= 's';
        }

        // Add date range conditions
        if (!empty($_POST['start_date_from'])) {
            $conditions[] = "tmp.start_date_contract >= ?";
            $params[] = $_POST['start_date_from'];
            $types .= 's';
        }

        if (!empty($_POST['start_date_to'])) {
            $conditions[] = "tmp.start_date_contract <= ?";
            $params[] = $_POST['start_date_to'];
            $types .= 's';
        }

        if (!empty($_POST['end_date_from'])) {
            $conditions[] = "(tmp.end_date_contract >= ? OR tmp.end_date_contract IS NULL)";
            $params[] = $_POST['end_date_from'];
            $types .= 's';
        }

        if (!empty($_POST['end_date_to'])) {
            $conditions[] = "(tmp.end_date_contract <= ? OR tmp.end_date_contract IS NULL)";
            $params[] = $_POST['end_date_to'];
            $types .= 's';
        }

        // Add condition to filter out contracts with status_contract = 0
        $conditions[] = "tmp.status_contract != 0";

        // Update the main query to include all necessary fields
        $sql = "SELECT DISTINCT 
                CASE 
                    WHEN tmp.type = 'main' THEN 
                        CONCAT('contract_', tmp.id_contract)
                    ELSE 
                        CONCAT('extension_', tmp.id_extension_contract)
                END as item_id,
                CASE 
                    WHEN tmp.type = 'main' THEN 
                        CONCAT('عقد رقم ', tmp.id_contract, ' - ', tmp.name_ar_contract, ' - ', DATE_FORMAT(tmp.version_date, '%d-%m-%Y'))
                    ELSE 
                        CONCAT('| تمديد رقم ', tmp.id_extension_contract, ' - ', 
                              DATE_FORMAT(tmp.ext_start_date, '%d-%m-%Y'), ' إلى ', 
                              COALESCE(DATE_FORMAT(tmp.ext_end_date, '%d-%m-%Y'), 'مفتوح'))
                END as display_text,
                tmp.*
                FROM (
                    -- Main contracts
                    SELECT 
                        c.id_contract,
                        NULL as id_extension_contract,
                        e.name_ar_contract,
                        e.name_en_contract,
                        c.name_Job,
                        e.Identity_number_contract,
                        p.Project_name,
                        c.version_date,
                        c.start_date_contract,
                        c.end_date_contract,
                        NULL as ext_start_date,
                        NULL as ext_end_date,
                        c.version_date as sort_date,
                        'main' as type,
                        c.id_contract as main_contract_id,
                        0 as is_extension,
                        c.contract_type,
                        c.id_Project,
                        c.wage_contract,
                        c.status_contract
                    FROM contract c
                    LEFT JOIN employees e ON c.id_employees = e.id_employees 
                    LEFT JOIN Project p ON c.id_Project = p.id_Project
                    WHERE c.status_contract != 0

                    UNION ALL

                    -- Extension contracts
                    SELECT 
                        c.id_contract,
                        ec.id_extension_contract,
                        e.name_ar_contract,
                        e.name_en_contract,
                        c.name_Job,
                        e.Identity_number_contract,
                        p.Project_name,
                        ec.version_date,
                        ec.start_date_contract,
                        ec.end_date_contract,
                        ec.start_date_contract as ext_start_date,
                        ec.end_date_contract as ext_end_date,
                        ec.version_date as sort_date,
                        'extension' as type,
                        c.id_contract as main_contract_id,
                        1 as is_extension,
                        c.contract_type,
                        c.id_Project,
                        CAST(ec.wage_contract AS DECIMAL(18,2)) as wage_contract,
                        ec.status_contract
                    FROM contract c
                    LEFT JOIN employees e ON c.id_employees = e.id_employees 
                    LEFT JOIN Project p ON c.id_Project = p.id_Project
                    INNER JOIN extension_contract ec ON c.id_contract = ec.id_contract
                    WHERE c.status_contract != 0 AND ec.status_contract != 0
                ) tmp";

        // Add search conditions if any
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Order by main contract ID, then by extension (main contracts first), then by date
        $sql .= " ORDER BY tmp.main_contract_id ASC, tmp.is_extension ASC, tmp.sort_date DESC";

        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $search_results = [];
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $search_results[] = $row;
            }
            $success_message = 'تم العثور على ' . count($search_results) . ' عقد';
        } else {
            $error_message = 'لم يتم العثور على أي عقود تطابق معايير البحث';
        }

        $stmt->close();

        // If a specific contract is selected
        if (isset($_POST['selected_contract'])) {
            $selected_contract = $_POST['selected_contract'];
            if (strpos($selected_contract, 'extension_') === 0) {
                // This is an extension contract; extract the extension contract id
                $ext_id = (int) str_replace('extension_', '', $selected_contract);

                // Fetch the complete contract data along with extension details
                $sql = "SELECT c.*, e.*, p.Project_name, 
                       ec.id_extension_contract,
                       ec.version_date AS ext_version_date, 
                       ec.start_date_contract AS ext_start_date_contract, 
                       ec.end_date_contract AS ext_end_date_contract, 
                       ec.wage_contract AS ext_wage_contract,
                       ec.signed_extension_contract_document  -- <== Added column
                FROM contract c 
                LEFT JOIN employees e ON c.id_employees = e.id_employees 
                LEFT JOIN Project p ON c.id_Project = p.id_Project
                LEFT JOIN extension_contract ec ON c.id_contract = ec.id_contract
                WHERE ec.id_extension_contract = ? AND c.status_contract != 0 AND ec.status_contract != 0";
                $stmt = $conn->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Query preparation failed: " . $conn->error);
                }
                $stmt->bind_param("i", $ext_id);
            } elseif (strpos($selected_contract, 'contract_') === 0) {
                // This is a main contract; extract the contract id
                $contract_id = (int) str_replace('contract_', '', $selected_contract);

                $sql = "SELECT c.*, e.*, p.Project_name 
                FROM contract c 
                LEFT JOIN employees e ON c.id_employees = e.id_employees 
                LEFT JOIN Project p ON c.id_Project = p.id_Project
                WHERE c.id_contract = ? AND c.status_contract != 0";
                $stmt = $conn->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Query preparation failed: " . $conn->error);
                }
                $stmt->bind_param("i", $contract_id);
            } else {
                throw new Exception("Invalid contract identifier");
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Query execution failed: " . $stmt->error);
            }

            $result = $stmt->get_result();
            if ($result && $result->num_rows > 0) {
                $contract_data = $result->fetch_assoc();
                // If extension details are present, override the main contract fields for display
                if (isset($contract_data['ext_version_date'])) {
                    $contract_data['version_date'] = $contract_data['ext_version_date'];
                    $contract_data['start_date_contract'] = $contract_data['ext_start_date_contract'];
                    $contract_data['end_date_contract'] = $contract_data['ext_end_date_contract'];
                    $contract_data['wage_contract'] = (float)$contract_data['ext_wage_contract'];
                }
                $success_message = 'تم العثور على بيانات العقد';
            } else {
                $error_message = 'لم يتم العثور على بيانات العقد';
            }
            $stmt->close();
        }
    }

    // Fetch attendance records (permanent diapers)
    $attendance_records = [];
    $attendance_sql = "SELECT id_permanent_diapers, 
                             start_date_permanent_diapers, 
                             end_date_permanent_diapers,
                             add_permanent_diapers 
                      FROM permanent_diapers 
                      WHERE id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $attendance_sql .= " AND id_extension_contract = ?";
        $stmt = $conn->prepare($attendance_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $attendance_sql .= " AND (id_extension_contract IS NULL OR id_extension_contract = 0)";
        $stmt = $conn->prepare($attendance_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $attendance_records[] = $row;
    }

    // Fetch achievement reports
    $achievement_reports = [];
    $achievement_sql = "SELECT ar.id_achievement_reports,
                              ar.start_date_achievement_reports,
                              ar.end_date_achievement_reports,
                              ar.actual_working_days,
                              ar.add_achievement_reports,
                              pd.id_permanent_diapers
                       FROM achievement_reports ar
                       JOIN permanent_diapers pd ON ar.id_permanent_diapers = pd.id_permanent_diapers
                       WHERE ar.id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $achievement_sql .= " AND ar.id_extension_contract = ?";
        $stmt = $conn->prepare($achievement_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $achievement_sql .= " AND (ar.id_extension_contract IS NULL OR ar.id_extension_contract = 0)";
        $stmt = $conn->prepare($achievement_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        // Calculate working days if actual_working_days is 0
        if ($row['actual_working_days'] == 0) {
            $start = new DateTime($row['start_date_achievement_reports']);
            $end = new DateTime($row['end_date_achievement_reports']);
            $interval = $start->diff($end);
            $row['actual_working_days'] = $interval->days + 1;
        }
        $achievement_reports[] = $row;
    }

    // Fetch merit reports
    $merit_reports = [];
    $merit_sql = "SELECT mr.id_merit_reports,
                         mr.actual_working_days,
                         mr.today_wage,
                         mr.total,
                         mr.total_after_discount,
                         mr.tax_rate,
                         mr.predecessor,
                         mr.Insurance,
                         ar.id_achievement_reports,
                         ar.start_date_achievement_reports,
                         ar.end_date_achievement_reports
                  FROM merit_reports mr
                  JOIN achievement_reports ar ON mr.id_achievement_reports = ar.id_achievement_reports
                  WHERE mr.id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $merit_sql .= " AND mr.id_extension_contract = ?";
        $stmt = $conn->prepare($merit_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $merit_sql .= " AND (mr.id_extension_contract IS NULL OR mr.id_extension_contract = 0)";
        $stmt = $conn->prepare($merit_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $merit_reports[] = $row;
    }

    // Fetch extended contracts if this is a main contract
    $extended_contracts = [];
    if (isset($_POST['selected_contract']) && strpos($_POST['selected_contract'], 'contract_') === 0) {
        $contract_id = (int) str_replace('contract_', '', $_POST['selected_contract']);
        
        $ext_sql = "SELECT ec.*, 
                           DATE_FORMAT(ec.version_date, '%d-%m-%Y') as formatted_version_date,
                           DATE_FORMAT(ec.start_date_contract, '%d-%m-%Y') as formatted_start_date,
                           COALESCE(DATE_FORMAT(ec.end_date_contract, '%d-%m-%Y'), 'مفتوح') as formatted_end_date
                    FROM extension_contract ec 
                    WHERE ec.id_contract = ? AND ec.status_contract != 0
                    ORDER BY ec.version_date DESC";
        
        $stmt = $conn->prepare($ext_sql);
        $stmt->bind_param("i", $contract_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $extended_contracts[] = $row;
        }
        $stmt->close();
    }

} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

function formatDate($date) {
    if (empty($date)) return "غير محدد";
    return date("Y/m/d", strtotime($date));
}

function getContractTypeName($type) {
    switch ($type) {
        case 1: return "راتب شهري";
        case 2: return "أجر يومي";
        default: return "غير محدد";
    }
}

// First, add this helper function near the other helper functions at the top of the file
function getCurrencyTypeName($type) {
    switch ($type) {
        case 1: return "دولار";
        case 2: return "ريال";
        default: return "غير محدد";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العقود - نظام إدارة الموارد البشرية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Updated styles for better dark theme compatibility */
        .search-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 4px 20px var(--shadow-color);
            padding: 1.75rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filters {
            padding: 1rem;
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filter-item {
            min-width: 150px;
            position: relative;
        }

        .quick-filter-item .form-select,
        .quick-filter-item .select2-container .select2-selection--single {
            border-radius: 20px;
            padding: 0.4rem 2rem 0.4rem 1rem;
            border: 1px solid var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .quick-filter-item .form-select:hover,
        .quick-filter-item .select2-container .select2-selection--single:hover {
            border-color: var(--primary-color);
        }

        .quick-filter-item .form-select:focus,
        .quick-filter-item .select2-container .select2-selection--single:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .advanced-filters-container {
            padding: 1.5rem;
            background: var(--bg-main);
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .filter-group {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            box-shadow: 0 1px 3px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .filter-group-title {
            color: var(--text-color);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-group-title i {
            color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .form-control {
            background-color: var(--bg-input);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .form-control:focus {
            background-color: var(--bg-input);
            border-color: var(--primary-color);
            color: var(--text-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
            border-radius: 20px;
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Results styles with dark theme support */
        .results-container {
            margin-top: 1.5rem;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            max-height: 500px;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .results-header {
            position: sticky;
            top: 0;
            background: var(--bg-main);
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
            z-index: 1;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .results-search input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-input);
            color: var(--text-color);
            transition: all 0.2s ease;
            font-size: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .results-search input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .result-item {
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-card);
        }

        .result-item:hover {
            background: var(--hover-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }

        .result-item-name {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .result-item-info {
            font-size: 0.95rem;
            color: var(--text-muted);
            margin-top: 0.5rem;
            line-height: 1.5;
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .select-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Select2 Dark Theme Adjustments */
        .select2-container--bootstrap-5 .select2-selection {
            background-color: var(--bg-input) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--bg-card) !important;
            border-color: var(--border-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            color: var(--text-color) !important;
            background-color: var(--bg-card) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--hover-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: var(--primary-color) !important;
            color: white !important;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .search-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .quick-filters {
            background: var(--bg-card);
        }

        [data-theme="dark"] .advanced-filters-container {
            background: var(--bg-main);
        }

        [data-theme="dark"] .filter-group {
            background: var(--bg-card);
        }

        [data-theme="dark"] input[type="date"] {
            color-scheme: dark;
        }

        /* Scrollbar styling */
        .results-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .results-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .quick-filter-item {
                min-width: 120px;
            }
            
            .filter-group {
                margin-bottom: 1rem;
            }
            
            .advanced-filters-container {
                padding: 1rem;
            }
            
            .filter-actions {
                justify-content: center;
            }
        }

        /* Add these styles for the Contract Details section */
        .report-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 2px 15px var(--shadow-color);
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            color: var(--text-color);
            margin: 0;
            font-weight: 600;
        }

        .report-header .btn-print {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .report-header .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .report-section {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1.25rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            background: var(--bg-card);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        .info-value.text-primary {
            color: var(--primary-color) !important;
        }

        .info-value.text-success {
            color: var(--success-color) !important;
        }

        .badge {
            padding: 0.5em 0.75em;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .badge-primary {
            background: var(--primary-color);
            color: white;
        }

        .badge-success {
            background: var(--success-color);
            color: white;
        }

        .task-list {
            display: grid;
            gap: 1rem;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .task-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .task-name-en {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .task-progress {
            width: 100px;
            background: var(--bg-main);
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-value {
            height: 6px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .progress-label {
            color: var(--text-muted);
            font-size: 0.75rem;
            text-align: center;
            margin-top: 0.25rem;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .report-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .report-section {
            background: var(--bg-main);
        }

        [data-theme="dark"] .info-item {
            background: var(--bg-card);
        }

        [data-theme="dark"] .task-item {
            background: var(--bg-card);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .report-container {
                margin: 0;
                padding: 0;
                box-shadow: none;
            }

            .report-section {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }

        .extension-item {
            padding-right: 2rem !important;
            position: relative;
            font-size: 0.95em;
            background-color: var(--bg-secondary) !important;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: 1rem;
            top: 50%;
            width: 0.5rem;
            height: 1px;
            background-color: var(--text-muted);
        }

        .result-item-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .result-item:not(.extension-item) {
            margin-top: 0.5rem;
        }

        .result-item:not(.extension-item) + .extension-item {
            margin-top: 0.25rem;
        }

        .tasks-container {
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.5rem;
        }

        .task-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-color);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-size: 0.95rem;
            line-height: 1.4;
        }

        /* Scrollbar styling */
        .tasks-container::-webkit-scrollbar {
            width: 6px;
        }

        .tasks-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Add these styles to your existing style section */
        .scrollable-section {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--bg-main);
        }

        .scrollable-section::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-section::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .scrollable-section::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .search-box {
            margin-left: 1rem;
        }

        .list-group-item {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            background-color: var(--hover-color);
        }

        .list-group-item h6 {
            color: var(--text-color);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .list-group-item small {
            font-size: 0.8rem;
        }

        /* Add these new styles */
        .inner-section {
            background: var(--bg-main);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .inner-section .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.35em 0.65em;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        /* Add these new styles */
        .stats-pills {
            display: flex;
            gap: 1rem;
        }

        .stat-pill {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.35rem 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 0.875rem;
        }

        .stat-pill i {
            color: var(--primary-color);
        }

        .stat-value {
            font-weight: 600;
            color: var(--text-color);
        }

        .stat-label {
            color: var(--text-muted);
        }

        .nav-tabs-custom {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background: var(--bg-main);
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .tab-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: var(--text-muted);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .tab-btn:hover {
            color: var(--text-color);
            background: var(--hover-color);
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .tab-content-wrapper {
            background: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .tab-content {
            display: none;
            height: 400px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .search-box {
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .search-input {
            padding-left: 2.5rem;
            background: var(--bg-main);
        }

        .scrollable-section {
            height: calc(100% - 65px);
            overflow-y: auto;
            padding: 1rem;
        }

        .list-item {
            padding: 1rem;
            border-radius: 8px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
        }

        .item-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .item-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 8px;
            flex-shrink: 0;
        }

        .item-details {
            flex: 1;
        }

        .item-title {
            color: var(--text-color);
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .item-meta {
            color: var(--text-muted);
            font-size: 0.8rem;
            display: flex;
            gap: 1rem;
        }

        .item-meta i {
            margin-right: 0.25rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
        }

        .btn-icon:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        /* Add to the existing style section */
        .extension-list-item {
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .extension-list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .extension-list-item .item-icon {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .extension-list-item .item-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .extension-list-item .btn-icon:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Add to your existing styles */
        .result-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .result-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-left: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        .extension-item {
            margin-left: 2rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            left: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .result-item-details {
            flex: 1;
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            border-radius: 4px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .select-contract-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.25em 0.5em;
            border-radius: 4px;
        }

        /* Update these styles in your existing style section */

        .extension-item {
            margin-right: 2rem;
            margin-bottom: 0.5rem;
            border-right: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-right: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        /* Add new style for the arrow icon */
        .extension-item .bi-arrow-return-right {
            transform: scaleX(-1);  /* Flip the arrow icon horizontally */
        }

        /* Optional: Add a connecting line from bottom extension to top extension */
        .extension-item::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -2rem;
            width: 2px;
            height: calc(-100% - 0.5rem);
            background-color: var(--border-color);
            z-index: 0;
        }

        /* Remove the vertical line from the first extension */
        .extension-item:first-child::after {
            display: none;
        }

        /* Add hover effect for the connection lines */
        .extension-item:hover::before,
        .extension-item:hover::after {
            background-color: var(--primary-color);
        }

        /* Add these styles to make the modal content more readable */
        .modal-xl {
            max-width: 90%; /* Makes modal wider */
        }
        
        .modal-body {
            padding: 2rem;
        }
        
        .report-details .section {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .info-item {
            background: var(--bg-main);
            padding: 1rem;
            border-radius: 6px;
            height: 100%;
        }
        
        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        /* Modal Styling */
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Modal Header Styling */
        .modal-header {
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header .modal-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            color: var(--text-color);
            margin: 0;
            order: 1; /* Changed from 2 to 1 */
        }

        .modal-header .modal-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .modal-header .btn-close {
            order: 2; /* Changed from 1 to 2 */
            margin: 0;
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-main);
            border-radius: 8px;
            opacity: 1;
            transition: all 0.2s ease;
            position: relative;
        }

        /* Custom close button design */
        .modal-header .btn-close::before,
        .modal-header .btn-close::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 2px;
            background-color: var(--text-color);
            border-radius: 1px;
            transition: all 0.2s ease;
        }

        .modal-header .btn-close::before {
            transform: rotate(45deg);
        }

        .modal-header .btn-close::after {
            transform: rotate(-45deg);
        }

        .modal-header .btn-close:hover {
            background-color: var(--hover-color);
            transform: rotate(90deg);
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .modal-header .btn-close {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] .modal-header .btn-close::before,
        [data-theme="dark"] .modal-header .btn-close::after {
            background-color: var(--text-muted);
        }

        [data-theme="dark"] .modal-header .btn-close:hover {
            background-color: var(--primary-color);
        }

        [data-theme="dark"] .modal-header .btn-close:hover::before,
        [data-theme="dark"] .modal-header .btn-close:hover::after {
            background-color: white;
        }

        .modal-body {
            padding: 2rem;
            background: var(--bg-main);
        }

        /* Report Details Styling */
        .report-details {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .report-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .report-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-header i {
            color: var(--primary-color);
            font-size: 1.25rem;
            background: var(--primary-light);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .section-header h6 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--text-color);
        }

        /* Info Grid Styling */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            background: var(--hover-color);
            border-color: var(--primary-color);
        }

        .info-card .label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-card .value {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 500;
        }

        /* Task List Styling */
        .task-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .task-item {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.75rem;
        }

        .progress-wrapper {
            background: var(--bg-card);
            border-radius: 6px;
            padding: 0.75rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-muted);
            font-size: 0.75rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 48px;
            height: 48px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Achievement Timeline Report Table Styles */
        .achievement-schedule-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 0;
            border: 1px solid var(--border-color);
        }

        .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            white-space: nowrap;
            position: relative;
        }

        .achievement-schedule-table tbody td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
            transition: all 0.2s ease;
        }

        .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Task name column specific styling */
        .achievement-schedule-table td:first-child {
            text-align: right;
            font-weight: 500;
            color: var(--primary-color);
        }

        /* Percentage/Days values styling */
        .achievement-schedule-table td:not(:first-child):not(:last-child) {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Notes column styling */
        .achievement-schedule-table td:last-child {
            color: var(--text-muted);
            font-style: italic;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .achievement-schedule-table {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border-bottom-color: var(--primary-color);
        }

        [data-theme="dark"] .achievement-schedule-table tbody td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Responsive table container */
        .achievement-schedule-container {
            width: 100%;
            overflow-x: auto;
            border-radius: 8px;
            position: relative;
        }

        /* Custom scrollbar styling */
        .achievement-schedule-container::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .achievement-schedule-container::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Value highlighting */
        .value-percentage {
            color: var(--success-color);
            background: var(--success-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        .value-days {
            color: var(--info-color);
            background: var(--info-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .achievement-schedule-table thead th,
            .achievement-schedule-table tbody td {
                padding: 0.75rem;
                font-size: 0.9rem;
            }
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            margin-bottom: 0.25rem;
        }

        .status-badge:empty::after {
            content: "-";
            color: var(--text-muted);
        }

        .status-badge.present {
            background-color: var(--success-bg);
            color: var(--success-color);
        }

        .time-display {
            font-size: 0.875rem;
            color: var(--text-primary);
            text-align: center;
        }

        .time-display:empty::after {
            content: "00:00";
            color: var(--text-muted);
        }

        .table th {
            text-align: center;
            vertical-align: middle;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            white-space: nowrap;
        }

        .table td {
            text-align: center;
            vertical-align: middle;
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        /* Attendance Tables Styling */
        .attendance-summary-table,
        .attendance-details-table {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
        }

        .attendance-summary-table th,
        .attendance-details-table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            border-color: var(--border-color);
            text-align: center;
            white-space: nowrap;
        }

        .attendance-summary-table td,
        .attendance-details-table td {
            background-color: var(--bg-card);
            color: var(--text-primary);
            border-color: var(--border-color);
            padding: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .attendance-details-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Status Badge Styling */
        .status-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.35rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            min-width: 80px;
        }

        .status-badge.present {
            background-color: var(--success-light);
            color: var(--success-color);
        }

        .status-badge.absent {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }

        .status-badge.leave {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }

        .status-badge.day-off {
            background-color: var(--info-light);
            color: var(--info-color);
        }

        /* Time Display Styling */
        .time-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .time-value {
            font-family: monospace;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        /* Dark Theme Specific Adjustments */
        [data-theme="dark"] .attendance-summary-table,
        [data-theme="dark"] .attendance-details-table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] .attendance-summary-table th,
        [data-theme="dark"] .attendance-details-table th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .attendance-summary-table td,
        [data-theme="dark"] .attendance-details-table td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.absent {
            background-color: rgba(var(--danger-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.leave {
            background-color: rgba(var(--warning-rgb), 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .attendance-summary-table,
            .attendance-details-table {
                font-size: 0.875rem;
            }

            .attendance-summary-table th,
            .attendance-details-table th,
            .attendance-summary-table td,
            .attendance-details-table td {
                padding: 0.625rem;
            }

            .status-badge {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                min-width: 60px;
            }
        }

        /* Add these styles to the existing style section */
        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .report-header .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .report-header .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .report-header .btn i {
            font-size: 1rem;
        }

        /* Document Section Styles */
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .document-card {
            position: relative;
            background-color: var(--bg-main);
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-color);
        }

        .document-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 10px 10px 0 0;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .document-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .document-card:hover::before {
            opacity: 1;
        }

        .document-icon {
            text-align: center;
            margin-bottom: 1rem;
        }

        .document-icon i {
            font-size: 2rem;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .document-card:hover .document-icon {
            transform: scale(1.1);
        }

        .document-card:hover .document-icon i {
            color: var(--primary-dark);
        }

        .document-title {
            text-align: center;
            margin-bottom: 1rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .document-status {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }

        .document-status.uploaded {
            background-color: #2ecc71;
        }

        .document-status.not-uploaded {
            background-color: #e74c3c;
        }

        .document-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: auto;
        }

        .upload-zone {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-zone:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .upload-zone input[type="file"] {
            display: none;
        }

        .upload-zone-text {
            text-align: center;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .upload-zone:hover .upload-zone-text {
            color: var(--primary-color);
        }

        .btn-document {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .btn-document.btn-upload {
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
        }

        .btn-document.btn-upload:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-document.btn-download {
            background: var(--bg-main);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-document.btn-download:hover {
            background-color: #27ae60;
            color: white;
        }
        
        .btn-document.btn-view {
            background-color: #3498db;
            color: white;
        }
        
        .btn-document.btn-view:hover {
            background-color: #2980b9;
        }
        
        .document-upload-form.uploading .btn-upload {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Loading animation */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .uploading .document-icon {
            animation: pulse 1s infinite;
        }

        /* Add event listener to show file name when a file is selected */
        .document-upload-form input[type="file"]').on('change', function() {
            const fileName = $(this).val().split('\\').pop();
            if (fileName) {
                const uploadZone = $(this).closest('.upload-zone');
                const uploadZoneText = uploadZone.find('.upload-zone-text');
                uploadZone.addClass('file-selected');
                uploadZoneText.html(`
                    <i class="bi bi-file-earmark-pdf text-success"></i>
                    <span class="selected-file-name">${fileName}</span>
                `);
            } else {
                const uploadZone = $(this).closest('.upload-zone');
                const uploadZoneText = uploadZone.find('.upload-zone-text');
                uploadZone.removeClass('file-selected');
                uploadZoneText.html('<i class="bi bi-cloud-arrow-up"></i><span>اختر ملف PDF للرفع</span>');
            }
        });

        // Add this CSS to style the spinning icon and selected file name
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .selected-file-name {
                font-weight: 500;
                color: var(--primary-color);
                word-break: break-word;
                max-width: 100%;
                display: block;
                margin-top: 5px;
            }
            .upload-zone.file-selected {
                border-color: var(--primary-color);
                background-color: rgba(46, 115, 252, 0.05);
                border-style: solid;
            }
            .upload-zone.file-selected .upload-zone-text {
                color: var(--primary-color);
            }
        `;
        document.head.appendChild(style);

        /* Select2 Field Size and Scrolling */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
            height: 38px;
            line-height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            color: var(--text-color);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-dropdown {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            max-height: 300px;
            overflow-y: auto;
        }

        .select2-results {
            max-height: 300px;
        }

        .select2-container--default .select2-results__option {
            color: var(--text-color);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color);
            color: white;
        }

        /* Select2 Search Dropdown Styling */
        .select2-container--bootstrap-5 .select2-dropdown {
            max-height: 300px;
            overflow-y: auto;
        }

        /* Dark Theme Support for Select2 */
        [data-theme="dark"] .select2-container--default .select2-selection--single {
            background-color: var(--dark-bg);
            border-color: var(--dark-border);
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__rendered {
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-dropdown {
            background-color: #1a1d20 !important;
            border-color: #2c3034 !important;
        }

        [data-theme="dark"] .select2-container--default .select2-search--dropdown .select2-search__field {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
            border-color: #2c3034 !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        /* Custom scrollbar for Select2 dropdown */
        .select2-results__options::-webkit-scrollbar {
            width: 6px;
        }

        .select2-results__options::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 3px;
        }

        .select2-results__options::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        [data-theme="dark"] .select2-results__options::-webkit-scrollbar-track {
            background: var(--dark-bg);
        }

        [data-theme="dark"] .select2-results__options::-webkit-scrollbar-thumb {
            background: var(--dark-primary);
        }

        /* Specific styles for the scrollable dropdown */
        .select2-dropdown-scrollable {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .select2-dropdown-scrollable .select2-results {
            max-height: none;
        }
        
        .select2-dropdown-scrollable .select2-results__options {
            max-height: none;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Main Container -->
                    <div class="search-container">
                        <!-- Quick Filters -->
                        <div class="quick-filters">
                            <form method="post" id="searchForm" class="mb-0">
                                <div class="d-flex flex-column gap-3">
                                    <!-- Primary Filters -->
                                    <div class="d-flex align-items-center gap-2 flex-wrap">
                                        <div class="quick-filter-item">
                                            <select class="form-select form-select-sm select2" id="project_id" name="project_id">
                                                <option value="">اختر المشروع</option>
                                                <?php foreach ($projects as $project): ?>
                                                    <option value="<?php echo htmlspecialchars($project['id_Project']); ?>">
                                                        <?php echo htmlspecialchars($project['Project_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="quick-filter-item">
                                            <select class="form-select form-select-sm select2" id="contract_type" name="contract_type">
                                                <option value="">نوع العقد</option>
                                                <option value="1">راتب شهري</option>
                                                <option value="2">أجر يومي</option>
                                            </select>
                                        </div>
                                        <div class="quick-filter-item">
                                            <select class="form-select form-select-sm select2" id="name_Job" name="name_Job">
                                                <option value="">المسمى الوظيفي</option>
                                                <?php foreach ($job_titles as $title): ?>
                                                    <option value="<?php echo htmlspecialchars($title); ?>">
                                                        <?php echo htmlspecialchars($title); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                                            <i class="bi bi-funnel"></i> فلترة متقدمة
                                        </button>
                                    </div>

                                    <!-- Employee Information -->
                                    <div class="filter-group mb-0">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-person"></i> معلومات صاحب العقد
                                        </h6>
                                        <div class="d-flex gap-2 flex-wrap">
                                            <div class="flex-grow-1">
                                                <input type="text" class="form-control form-control-sm" id="name_ar_contract" name="name_ar_contract" placeholder="البحث عن العقد (رقم العقد، اسم صاحب العقد، التاريخ)">
                                            </div>
                                            <div>
                                                <button type="submit" name="search" class="btn btn-primary btn-sm">
                                                    <i class="bi bi-search"></i> بحث
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced Filters -->
                                <div class="collapse" id="advancedFilters">
                                    <div class="advanced-filters-container">
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <div class="filter-group">
                                                    <h6 class="filter-group-title">
                                                        <i class="bi bi-calendar-range"></i> تاريخ البداية
                                                    </h6>
                                                    <div class="input-group input-group-sm">
                                                        <input type="date" name="start_date_from" class="form-control" placeholder="من">
                                                        <input type="date" name="start_date_to" class="form-control" placeholder="إلى">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="filter-group">
                                                    <h6 class="filter-group-title">
                                                        <i class="bi bi-calendar-range"></i> تاريخ النهاية
                                                    </h6>
                                                    <div class="input-group input-group-sm">
                                                        <input type="date" name="end_date_from" class="form-control" placeholder="من">
                                                        <input type="date" name="end_date_to" class="form-control" placeholder="إلى">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="filter-group">
                                                    <h6 class="filter-group-title">
                                                        <i class="bi bi-calendar-plus"></i> تاريخ الإضافة
                                                    </h6>
                                                    <div class="input-group input-group-sm">
                                                        <input type="date" name="add_date_from" class="form-control" placeholder="من">
                                                        <input type="date" name="add_date_to" class="form-control" placeholder="إلى">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Results Section -->
                        <?php if (isset($search_results) && !empty($search_results)): ?>
                        <div class="results-container">
                            <div class="results-header">
                                <div class="results-search">
                                    <input type="text" id="resultSearch" placeholder="البحث في النتائج..." class="form-control">
                                </div>
                            </div>
                            <div class="results-list">
                                <?php 
                                $current_main_contract = null;
                                $grouped_results = [];

                                // Group results by main contract
                                foreach ($search_results as $result) {
                                    if ($result['is_extension']) {
                                        $grouped_results[$result['main_contract_id']]['extensions'][] = $result;
                                    } else {
                                        $grouped_results[$result['id_contract']] = [
                                            'main' => $result,
                                            'extensions' => []
                                        ];
                                    }
                                }

                                // Display grouped results
                                foreach ($grouped_results as $main_contract_id => $group):
                                    $main_contract = $group['main'];
                                ?>
                                    <!-- Main Contract -->
                                    <div class="result-item main-contract" 
                                         data-search-text="<?= htmlspecialchars($main_contract['display_text']) ?>">
                                        <div class="result-item-details">
                                            <div class="result-item-name">
                                                <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                                <?= htmlspecialchars($main_contract['display_text']) ?>
                                                <?php if (!empty($group['extensions'])): ?>
                                                    <span class="badge bg-secondary ms-2">
                                                        <?= count($group['extensions']) ?> تمديد
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="result-item-info small text-muted mt-1">
                                                <span class="me-3">
                                                    <i class="bi bi-person"></i>
                                                    <?= htmlspecialchars($main_contract['name_Job']) ?>
                                                </span>
                                                <span>
                                                    <i class="bi bi-building"></i>
                                                    <?= htmlspecialchars($main_contract['Project_name']) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <form method="post" style="display: inline;">
                                            <input type="hidden" name="search" value="1">
                                            <?php foreach ($_POST as $key => $value): ?>
                                                <?php if ($key !== 'selected_contract'): ?>
                                                    <input type="hidden" name="<?= htmlspecialchars($key) ?>" 
                                                           value="<?= htmlspecialchars($value) ?>">
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                            <input type="hidden" name="selected_contract" value="<?= $main_contract['item_id'] ?>">
                                            <button type="submit" class="select-contract-btn">
                                                <i class="bi bi-eye me-1"></i>عرض
                                            </button>
                                        </form>
                                    </div>

                                    <!-- Extensions -->
                                    <?php foreach ($group['extensions'] as $extension): ?>
                                        <div class="result-item extension-item" 
                                             data-search-text="<?= htmlspecialchars($extension['display_text']) ?>">
                                            <div class="result-item-details">
                                                <div class="result-item-name">
                                                    <i class="bi bi-arrow-return-left text-muted me-2"></i>
                                                    <?= htmlspecialchars($extension['display_text']) ?>
                                                </div>
                                            </div>
                                            <form method="post" style="display: inline;">
                                                <input type="hidden" name="search" value="1">
                                                <?php foreach ($_POST as $key => $value): ?>
                                                    <?php if ($key !== 'selected_contract'): ?>
                                                        <input type="hidden" name="<?= htmlspecialchars($key) ?>" 
                                                               value="<?= htmlspecialchars($value) ?>">
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                                <input type="hidden" name="selected_contract" value="<?= $extension['item_id'] ?>">
                                                <button type="submit" class="select-contract-btn">
                                                    <i class="bi bi-eye me-1"></i>عرض
                                                </button>
                                            </form>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if (isset($_POST['selected_contract']) && $contract_data): ?>
                    <div class="row">
                        <!-- Right Half - Contract Details -->
                        <div class="col-md-6">
                            <!-- Report Container -->
                            <div class="report-container">
                                <!-- Remove the Save button from the report header -->
                                <div class="report-header">
                                    <h5 class="mb-0">
                                        <?php if (strpos($_POST['selected_contract'], 'extension_') === 0 && isset($contract_data['id_extension_contract'])): ?>
                                            <i class="bi bi-file-earmark-plus text-primary me-2"></i>
                                            تفاصيل العقد الممدد - عقد رقم <?php echo htmlspecialchars($contract_data['id_contract']); ?> 
                                            <span class="text-muted small">
                                                (تمديد رقم <?php echo htmlspecialchars($contract_data['id_extension_contract']); ?>)
                                            </span>
                                        <?php else: ?>
                                            <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                            تفاصيل العقد الرئيسي - عقد رقم <?php echo htmlspecialchars($contract_data['id_contract']); ?>
                                        <?php endif; ?>
                                    </h5>
                                </div>

                                <!-- Contract Information -->
                                <div class="report-section">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="section-title mb-0">معلومات العقد</h6>
                                        <?php if (isset($contract_data)): ?>
                                            <a href="offer.php?template_type=<?php 
                                                echo (isset($contract_data['id_extension_contract'])) ? '2' : '1'; 
                                            ?>&contract_id=<?php 
                                                echo (isset($contract_data['id_extension_contract'])) ? 
                                                    htmlspecialchars($contract_data['id_extension_contract']) : 
                                                    htmlspecialchars($contract_data['id_contract']); 
                                            ?>" 
                                               target="_blank" 
                                               class="btn btn-primary btn-sm">
                                                <i class="bi bi-printer"></i> طباعة
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                    <div class="row">
                                        <?php if (strpos($_POST['selected_contract'], 'extension_') === 0 && isset($contract_data['id_extension_contract'])): ?>
                                            <!-- Extension Contract Display -->
                                            <div class="col-md-3 col-sm-6">
                                                <div class="info-group">
                                                    <div class="info-label">رقم العقد الرئيسي</div>
                                                    <div class="info-value"><?php echo htmlspecialchars($contract_data['id_contract']); ?></div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 col-sm-6">
                                                <div class="info-group">
                                                    <div class="info-label">رقم التمديد</div>
                                                    <div class="info-value"><?php echo htmlspecialchars($contract_data['id_extension_contract']); ?></div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <!-- Main Contract Display -->
                                            <div class="col-md-3 col-sm-6">
                                                <div class="info-group">
                                                    <div class="info-label">رقم العقد</div>
                                                    <div class="info-value"><?php echo htmlspecialchars($contract_data['id_contract']); ?></div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 col-sm-6">
                                                <div class="info-group">
                                                    <div class="info-label">عدد التمديدات</div>
                                                    <div class="info-value"><?php echo htmlspecialchars($contract_data['extension']); ?></div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="col-md-2 col-sm-6">
                                            <div class="info-group">
                                                <div class="info-label">رقم المشروع</div>
                                                <div class="info-value"><?php echo htmlspecialchars($contract_data['id_Project']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 col-sm-6">
                                            <div class="info-group">
                                                <div class="info-label">رقم الموظف</div>
                                                <div class="info-value"><?php echo htmlspecialchars($contract_data['id_employees']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-6">
                                            <div class="info-group">
                                                <div class="info-label">نوع العقد</div>
                                                <div class="info-value"><?php echo getContractTypeName($contract_data['contract_type']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                   
                                   <!-- Contract Document Section -->
                                   <div class="mt-4">
                                       <h6 class="section-title mb-3">
                                           <i class="bi bi-file-earmark-pdf"></i>
                                           المستند الموقع
                                       </h6>
                                       <div class="documents-grid">
                                           <?php if (strpos($_POST['selected_contract'], 'extension_') === 0): ?>
                                               <!-- Extension Contract Document -->
                                               <div class="document-card">
                                                   <div class="document-status <?php echo isset($contract_data['signed_extension_contract_document']) && $contract_data['signed_extension_contract_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                                   <div class="document-icon">
                                                       <i class="bi bi-file-earmark-pdf"></i>
                                                   </div>
                                                   <h6 class="document-title">مستند العقد الموقع</h6>
                                                   <form id="documentUploadForm" class="document-upload-form">
                                                       <input type="hidden" name="contract_id" value="<?php echo $contract_data['id_extension_contract']; ?>">
                                                       <input type="hidden" name="type" value="extension">
                                                       <label class="upload-zone">
                                                           <input type="file" class="form-control" name="contract_document" accept="application/pdf" required>
                                                           <div class="upload-zone-text">
                                                               <i class="bi bi-cloud-arrow-up"></i>
                                                               <span>اختر ملف PDF للرفع</span>
                                                           </div>
                                                       </label>
                                                       <div class="document-actions">
                                                           <button type="submit" class="btn-document btn-upload">
                                                               <i class="bi bi-upload"></i> رفع
                                                           </button>
                                                           <?php if (isset($contract_data['signed_extension_contract_document']) && $contract_data['signed_extension_contract_document']): ?>
                                                               <a href="get_report_contract_con/handle_contract_document.php?contract_id=<?php echo $contract_data['id_extension_contract']; ?>&type=extension&action=view" 
                                                                  class="btn-document btn-view" target="_blank">
                                                                   <i class="bi bi-eye"></i> عرض
                                                               </a>
                                                               <a href="get_report_contract_con/handle_contract_document.php?contract_id=<?php echo $contract_data['id_extension_contract']; ?>&type=extension" 
                                                                  class="btn-document btn-download">
                                                                   <i class="bi bi-download"></i> تحميل
                                                               </a>
                                                           <?php endif; ?>
                                                       </div>
                                                   </form>
                                               </div>
                                           <?php else: ?>
                                               <!-- Main Contract Document -->
                                               <div class="document-card">
                                                   <div class="document-status <?php echo isset($contract_data['signed_contract_document']) && $contract_data['signed_contract_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                                   <div class="document-icon">
                                                       <i class="bi bi-file-earmark-pdf"></i>
                                                   </div>
                                                   <h6 class="document-title">مستند العقد الموقع</h6>
                                                   <form id="documentUploadForm" class="document-upload-form">
                                                       <input type="hidden" name="contract_id" value="<?php echo $contract_data['id_contract']; ?>">
                                                       <input type="hidden" name="type" value="main">
                                                       <label class="upload-zone">
                                                           <input type="file" class="form-control" name="contract_document" accept="application/pdf" required>
                                                           <div class="upload-zone-text">
                                                               <i class="bi bi-cloud-arrow-up"></i>
                                                               <span>اختر ملف PDF للرفع</span>
                                                           </div>
                                                       </label>
                                                       <div class="document-actions">
                                                           <button type="submit" class="btn-document btn-upload">
                                                               <i class="bi bi-upload"></i> رفع
                                                           </button>
                                                           <?php if (isset($contract_data['signed_contract_document']) && $contract_data['signed_contract_document']): ?>
                                                               <a href="get_report_contract_con/handle_contract_document.php?contract_id=<?php echo $contract_data['id_contract']; ?>&type=main&action=view" 
                                                                  class="btn-document btn-view" target="_blank">
                                                                   <i class="bi bi-eye"></i> عرض
                                                               </a>
                                                               <a href="get_report_contract_con/handle_contract_document.php?contract_id=<?php echo $contract_data['id_contract']; ?>&type=main" 
                                                                  class="btn-document btn-download">
                                                                   <i class="bi bi-download"></i> تحميل
                                                               </a>
                                                           <?php endif; ?>
                                                       </div>
                                                   </form>
                                               </div>
                                           <?php endif; ?>
                                       </div>
                                   </div>
                                </div>

                                <!-- Employee Information -->
                                <div class="report-section">
                                    <h6 class="section-title">معلومات الموظف</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <div class="info-label">الاسم بالعربية</div>
                                                <div class="info-value"><?php echo htmlspecialchars($contract_data['name_ar_contract']); ?></div>
                                            </div>
                                            <div class="info-group mt-3">
                                                <div class="info-label">الاسم بالإنجليزية</div>
                                                <div class="info-value"><?php echo htmlspecialchars($contract_data['name_en_contract']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <div class="info-label">المسمى الوظيفي</div>
                                                <div class="info-value"><?php echo htmlspecialchars($contract_data['name_Job']); ?></div>
                                            </div>
                                            <div class="info-group mt-3">
                                                <div class="info-label">الراتب</div>
                                                <div class="info-value">
                                                    <?php 
                                                    $currency = getCurrencyTypeName($contract_data['Type_currency']);
                                                    echo number_format($contract_data['wage_contract'], 2) . ' ' . $currency; 
                                                    ?>
                                                    <div class="small text-muted">
                                                        <?php echo htmlspecialchars($contract_data['amount_written_ar']); ?>
                                                    </div>
                                                </div>
                                                <div class="info-group mt-3">
                                                    <div class="info-label">نوع العملة</div>
                                                    <div class="info-value">
                                                        <?php echo getCurrencyTypeName($contract_data['Type_currency']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Identity Information -->
                                <div class="report-section">
                                    <h6 class="section-title">معلومات الهوية</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">نوع الهوية</div>
                                                <div class="info-value">
                                                    <?php echo htmlspecialchars($contract_data['Identity_contract_ar']); ?>
                                                    <div class="small text-muted">
                                                        <?php echo htmlspecialchars($contract_data['Identity_contract_en']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">رقم الهوية</div>
                                                <div class="info-value"><?php echo htmlspecialchars($contract_data['Identity_number_contract']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">مكان الإصدار</div>
                                                <div class="info-value">
                                                    <?php echo htmlspecialchars($contract_data['Identity_issue_contract_ar']); ?>
                                                    <div class="small text-muted">
                                                        <?php echo htmlspecialchars($contract_data['Identity_issue_contract_en']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="info-group">
                                                <div class="info-label">تاريخ إصدار الهوية</div>
                                                <div class="info-value"><?php echo formatDate($contract_data['Identity_issue_date_contract']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contract Dates -->
                                <div class="report-section">
                                    <h6 class="section-title">تواريخ العقد</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">تاريخ إصدار العقد</div>
                                                <div class="info-value"><?php echo formatDate($contract_data['version_date']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">تاريخ بداية العقد</div>
                                                <div class="info-value"><?php echo formatDate($contract_data['start_date_contract']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">تاريخ نهاية العقد</div>
                                                <div class="info-value">
                                                    <?php 
                                                    if (empty($contract_data['end_date_contract'])) {
                                                        echo '<span class="badge bg-primary">عقد مفتوح</span>';
                                                    } else {
                                                        echo formatDate($contract_data['end_date_contract']);
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tasks Section -->
                                <div class="report-section">
                                    <h6 class="section-title">المهام الوظيفية</h6>
                                    <div class="tasks-container">
                                        <?php
                                        $tasks = json_decode($contract_data['data_todo_list_contract'], true);
                                        if ($tasks && isset($tasks['jobDetails']) && isset($tasks['jobDetails']['tasks'])):
                                        ?>
                                        <div class="task-list">
                                            <?php foreach ($tasks['jobDetails']['tasks'] as $index => $task): ?>
                                            <div class="task-item">
                                                <div class="task-number"><?php echo ($index + 1); ?></div>
                                                <div class="task-content">
                                                    <div class="task-name">
                                                        <?php echo htmlspecialchars($task['taskName']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php else: ?>
                                        <p class="text-muted">لا توجد مهام محددة</p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <?php if (!empty($contract_data['contractcol'])): ?>
                                <!-- Identity Document -->
                                <div class="report-section">
                                    <h6 class="section-title">مستند الهوية</h6>
                                    <div class="text-center">
                                        <a href="download_identity.php?id=<?php echo $contract_data['id_contract']; ?>" 
                                           class="btn btn-primary">
                                            <i class="bi bi-download me-2"></i>
                                            تحميل مستند الهوية
                                        </a>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Left Half - Reports Sections -->
                        <div class="col-md-6">
                            <!-- Related Data Container -->
                            <div class="report-container">
                                <div class="report-header d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center gap-3">
                                        <h5 class="mb-0">
                                            <i class="bi bi-diagram-3"></i>
                                            البيانات المرتبطة
                                        </h5>
                                        <div class="stats-pills">
                                            <span class="stat-pill">
                                                <i class="bi bi-calendar-check"></i>
                                                <span class="stat-value"><?php echo count($attendance_records); ?></span>
                                                <span class="stat-label">سجل حضور</span>
                                            </span>
                                            <span class="stat-pill">
                                                <i class="bi bi-graph-up"></i>
                                                <span class="stat-value"><?php echo count($achievement_reports); ?></span>
                                                <span class="stat-label">تقرير إنجاز</span>
                                            </span>
                                            <span class="stat-pill">
                                                <i class="bi bi-cash-stack"></i>
                                                <span class="stat-value"><?php echo count($merit_reports); ?></span>
                                                <span class="stat-label">تقرير استحقاق</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Inner Sections Container -->
                                <div class="report-section">
                                    <div class="tabs-container">
                                        <div class="nav-tabs-custom">
                                            <button class="tab-btn active" data-tab="attendance">
                                                <i class="bi bi-calendar-check"></i>
                                                سجلات الحضور
                                            </button>
                                            <button class="tab-btn" data-tab="achievements">
                                                <i class="bi bi-graph-up"></i>
                                                تقارير الإنجاز
                                            </button>
                                            <button class="tab-btn" data-tab="merits">
                                                <i class="bi bi-cash-stack"></i>
                                                تقارير الاستحقاق
                                            </button>
                                        </div>

                                        <!-- Tab Contents -->
                                        <div class="tab-content-wrapper">
                                            <!-- Attendance Records Tab -->
                                            <div class="tab-content active" id="attendance-tab">
                                                <div class="tab-header">
                                                    <div class="search-box">
                                                        <i class="bi bi-search search-icon"></i>
                                                        <input type="text" class="form-control form-control-sm search-input" 
                                                               placeholder="بحث في سجلات الحضور..." 
                                                               data-search-target="attendance-list">
                                                    </div>
                                                </div>
                                                <div class="scrollable-section custom-scrollbar">
                                                    <div class="list-group list-group-flush" id="attendance-list">
                                                        <?php if (empty($attendance_records)): ?>
                                                            <div class="empty-state">
                                                                <i class="bi bi-calendar-x"></i>
                                                                <p>لا توجد سجلات حضور</p>
                                                            </div>
                                                        <?php else: ?>
                                                            <?php foreach ($attendance_records as $record): ?>
                                                            <div class="list-item" 
                                                                 data-search-text="<?php echo date('Y-m-d', strtotime($record['start_date_permanent_diapers'])); ?>">
                                                                <div class="item-content">
                                                                    <div class="item-icon">
                                                                        <i class="bi bi-calendar-week"></i>
                                                                    </div>
                                                                    <div class="item-details">
                                                                        <h6 class="item-title mb-2">
                                                                            سجل رقم: <?php echo htmlspecialchars($record['id_permanent_diapers']); ?>
                                                                        </h6>
                                                                        <div class="item-meta d-flex flex-wrap gap-3">
                                                                            <span class="small">
                                                                                <i class="bi bi-calendar-range"></i>
                                                                                <?php echo date('Y-m-d', strtotime($record['start_date_permanent_diapers'])); ?> 
                                                                                <i class="bi bi-arrow-left mx-1"></i>
                                                                                <?php echo date('Y-m-d', strtotime($record['end_date_permanent_diapers'])); ?>
                                                                            </span>
                                                                            <span class="small">
                                                                                <i class="bi bi-clock"></i>
                                                                                <?php echo date('Y-m-d', strtotime($record['add_permanent_diapers'])); ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="btn-group">
                                                                        <button type="button" class="btn btn-icon" onclick="viewAttendanceReport(<?php echo $record['id_permanent_diapers']; ?>)">
                                                                            <i class="bi bi-eye"></i>
                                                                        </button>
                                                                        <a href="amendment_permanent_diapers_con.php?record_id=<?php echo $record['id_permanent_diapers']; ?>" 
                                                                           class="btn btn-icon ms-2">
                                                                            <i class="bi bi-pencil"></i>
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Achievement Reports Tab -->
                                            <div class="tab-content" id="achievements-tab">
                                                <div class="tab-header">
                                                    <div class="search-box">
                                                        <i class="bi bi-search search-icon"></i>
                                                        <input type="text" class="form-control form-control-sm search-input" 
                                                               placeholder="بحث في تقارير الإنجاز..." 
                                                               data-search-target="achievement-list">
                                                    </div>
                                                </div>
                                                <div class="scrollable-section custom-scrollbar">
                                                    <div class="list-group list-group-flush" id="achievement-list">
                                                        <?php if (empty($achievement_reports)): ?>
                                                            <div class="empty-state">
                                                                <i class="bi bi-graph-down"></i>
                                                                <p>لا توجد تقارير إنجاز</p>
                                                            </div>
                                                        <?php else: ?>
                                                            <?php foreach ($achievement_reports as $report): ?>
                                                            <div class="list-item" 
                                                                 data-search-text="<?php echo date('Y-m-d', strtotime($report['start_date_achievement_reports'])); ?>">
                                                                <div class="item-content">
                                                                    <div class="item-icon">
                                                                        <i class="bi bi-graph-up-arrow"></i>
                                                                    </div>
                                                                    <div class="item-details">
                                                                        <h6 class="item-title mb-2">
                                                                            تقرير رقم: <?php echo htmlspecialchars($report['id_achievement_reports']); ?>
                                                                        </h6>
                                                                        <div class="item-meta d-flex flex-wrap gap-3">
                                                                            <span class="small">
                                                                                <i class="bi bi-calendar-range"></i>
                                                                                <?php echo date('Y-m-d', strtotime($report['start_date_achievement_reports'])); ?> 
                                                                                <i class="bi bi-arrow-left mx-1"></i>
                                                                                <?php echo date('Y-m-d', strtotime($report['end_date_achievement_reports'])); ?>
                                                                            </span>
                                                                            <span class="small">
                                                                                <i class="bi bi-calendar-check"></i>
                                                                                أيام العمل: <?php echo $report['actual_working_days']; ?>
                                                                            </span>
                                                                            <span class="small">
                                                                                <i class="bi bi-clock-history"></i>
                                                                                <?php echo date('Y-m-d', strtotime($report['add_achievement_reports'])); ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <a href="view_achievement.php?id=<?php echo $report['id_achievement_reports']; ?>" 
                                                                       class="btn btn-icon">
                                                                        <i class="bi bi-eye"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Merit Reports Tab -->
                                            <div class="tab-content" id="merits-tab">
                                                <div class="tab-header">
                                                    <div class="search-box">
                                                        <i class="bi bi-search search-icon"></i>
                                                        <input type="text" class="form-control form-control-sm search-input" 
                                                               placeholder="بحث في تقارير الاستحقاق..." 
                                                               data-search-target="merit-list">
                                                    </div>
                                                </div>
                                                <div class="scrollable-section custom-scrollbar">
                                                    <div class="list-group list-group-flush" id="merit-list">
                                                        <?php if (empty($merit_reports)): ?>
                                                            <div class="empty-state">
                                                                <i class="bi bi-cash-coin"></i>
                                                                <p>لا توجد تقارير استحقاق</p>
                                                            </div>
                                                        <?php else: ?>
                                                            <?php foreach ($merit_reports as $report): ?>
                                                            <div class="list-item" 
                                                                 data-search-text="<?php echo date('Y-m-d', strtotime($report['start_date_achievement_reports'])); ?>">
                                                                <div class="item-content">
                                                                    <div class="item-icon">
                                                                        <i class="bi bi-cash"></i>
                                                                    </div>
                                                                    <div class="item-details">
                                                                        <h6 class="item-title">
                                                                            <?php echo date('Y-m-d', strtotime($report['start_date_achievement_reports'])); ?> 
                                                                            <i class="bi bi-arrow-left mx-2"></i>
                                                                            <?php echo date('Y-m-d', strtotime($report['end_date_achievement_reports'])); ?>
                                                                        </h6>
                                                                        <div class="item-meta">
                                                                            <span>
                                                                                <i class="bi bi-file-text"></i>
                                                                                تقرير إنجاز رقم: <?php echo $report['id_achievement_reports']; ?>
                                                                            </span>
                                                                            <span>
                                                                                <i class="bi bi-currency-dollar"></i>
                                                                                الأجر اليومي: <?php echo number_format($report['today_wage'], 2); ?> <?php echo getCurrencyTypeName($contract_data['Type_currency']); ?>
                                                                            </span>
                                                                            <span>
                                                                                <i class="bi bi-cash-stack"></i>
                                                                                الإجمالي: <?php echo number_format($report['total'], 2); ?> <?php echo getCurrencyTypeName($contract_data['Type_currency']); ?>
                                                                            </span>
                                                                            <span class="text-success">
                                                                                <i class="bi bi-coin"></i>
                                                                                الصافي: <?php echo number_format($report['total_after_discount'], 2); ?> <?php echo getCurrencyTypeName($contract_data['Type_currency']); ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <a href="view_merit.php?id=<?php echo $report['id_merit_reports']; ?>" 
                                                                       class="btn btn-icon">
                                                                        <i class="bi bi-eye"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if (strpos($_POST['selected_contract'], 'contract_') === 0 && !empty($extended_contracts)): ?>
                                <div class="report-section mt-4">
                                    <h6 class="section-title mb-3">
                                        <i class="bi bi-diagram-2"></i>
                                        تمديدات العقد
                                    </h6>
                                    
                                    <div class="extensions-list">
                                        <?php foreach ($extended_contracts as $extension): ?>
                                            <div class="list-item extension-list-item">
                                                <div class="item-content">
                                                    <div class="item-icon">
                                                        <i class="bi bi-file-earmark-text"></i>
                                                    </div>
                                                    <div class="item-details">
                                                        <h6 class="item-title">
                                                            تمديد رقم <?php echo htmlspecialchars($extension['id_extension_contract']); ?>
                                                        </h6>
                                                        <div class="item-meta">
                                                            <span>
                                                                <i class="bi bi-calendar-event"></i>
                                                                <?php echo htmlspecialchars($extension['formatted_start_date']); ?> 
                                                                <i class="bi bi-arrow-left mx-2"></i>
                                                                <?php echo htmlspecialchars($extension['formatted_end_date']); ?>
                                                            </span>
                                                            <span>
                                                                <i class="bi bi-currency-dollar"></i>
                                                                <?php echo htmlspecialchars($extension['wage_contract']); ?> دولار
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <form method="post" style="display: inline;">
                                                        <input type="hidden" name="search" value="1">
                                                        <?php foreach ($_POST as $key => $value): ?>
                                                            <?php if ($key !== 'selected_contract'): ?>
                                                                <input type="hidden" name="<?= htmlspecialchars($key) ?>" 
                                                                       value="<?= htmlspecialchars($value) ?>">
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                        <input type="hidden" name="selected_contract" 
                                                               value="extension_<?php echo $extension['id_extension_contract']; ?>">
                                                        <button type="submit" class="btn btn-icon">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php elseif (isset($search_results) && !empty($search_results)): ?>
                    <div class="text-center mt-4 text-muted">
                        <i class="bi bi-arrow-up-circle fs-1"></i>
                        <p class="mt-2">الرجاء اختيار عقد من القائمة أعلاه لعرض التفاصيل</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for general select elements
            $('.select2').not('#project_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                language: {
                    noResults: function() {
                        return "لا توجد نتائج";
                    }
                }
            });

            // Special initialization for project_id dropdown with improved scrolling
            $('#project_id').select2({
                placeholder: 'اختر المشروع',
                allowClear: true,
                width: '100%',
                language: { 
                    noResults: () => "لا توجد نتائج"
                },
                dir: "rtl",
                dropdownCssClass: 'select2-dropdown-scrollable'
            });

            // Search within results functionality
            $('#resultSearch').on('input', function() {
                const searchText = $(this).val().toLowerCase();
                $('.result-item').each(function() {
                    const itemText = $(this).data('search-text').toLowerCase();
                    $(this).toggle(itemText.includes(searchText));
                });
            });

            // Search functionality for all sections
            $('.search-input').on('input', function() {
                const searchText = $(this).val().toLowerCase();
                const targetList = $(this).data('search-target');
                
                $(`#${targetList} .list-item`).each(function() {
                    const itemText = $(this).data('search-text').toLowerCase();
                    $(this).toggle(itemText.includes(searchText));
                });
            });

            // Custom scrollbar styling
            $('.scrollable-section').addClass('custom-scrollbar');

            // Tab switching functionality
            $('.tab-btn').click(function() {
                const tabId = $(this).data('tab');
                
                // Update active states
                $('.tab-btn').removeClass('active');
                $(this).addClass('active');
                
                // Show selected tab content
                $('.tab-content').removeClass('active');
                $(`#${tabId}-tab`).addClass('active');
            });

            // Enhance hover effects
            $('.list-item').hover(
                function() {
                    $(this).find('.btn-icon').css('transform', 'scale(1.1)');
                },
                function() {
                    $(this).find('.btn-icon').css('transform', 'scale(1)');
                }
            );

            // Auto-scroll to contract details when a contract is selected
            <?php if (isset($_POST['selected_contract']) && $contract_data): ?>
                $('html, body').animate({
                    scrollTop: $('.report-container').offset().top - 20
                }, 300);  // Changed to 300ms for faster scrolling
            <?php endif; ?>
        });
    </script>

    <!-- Achievement Report Modal -->
    <div class="modal fade" id="achievementReportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <h5 class="modal-title">
                            <i class="bi bi-graph-up me-2"></i>
                            تفاصيل تقرير الإنجاز
                        </h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary" onclick="printAchievementReport()">
                                <i class="bi bi-printer me-1"></i>
                                طباعة التقرير
                            </button>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="achievement-details"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Merit Report Modal -->
    <div class="modal fade" id="meritReportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <h5 class="modal-title">
                            <i class="bi bi-cash-stack me-2"></i>
                            تفاصيل تقرير الاستحقاق
                        </h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary" onclick="printMeritReport()">
                                <i class="bi bi-printer me-1"></i>
                                طباعة التقرير
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="printMeritReportWithDiscount()">
                                <i class="bi bi-printer me-1"></i>
                                طباعة دون تفاصيل الخصومات
                            </button>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="merit-details"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Report Modal -->
    <div class="modal fade" id="attendanceReportModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <h5 class="modal-title">تفاصيل تقرير الحضور</h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary print-attendance" onclick="printAttendanceRecord()">
                                <i class="bi bi-printer me-1"></i>
                                طباعة التقرير
                            </button>
                            <button type="button" class="btn btn-secondary print-summary" onclick="printAttendanceSummary()">
                                <i class="bi bi-printer me-1"></i>
                                طباعة الملخص
                            </button>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="attendance-details"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Add this to your existing JavaScript
    function viewAchievementReport(id) {
        $.get('get_report_contract_con/get_achievement_report.php', { id: id })
            .done(function(response) {
                if (response.success) {
                    const data = response.data;
                    // Store the data for printing
                    currentAchievementData = data;
                    
                    // Format dates using Gregorian calendar
                    const startDate = new Date(data.start_date_achievement_reports).toLocaleDateString('en-GB', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
                    const endDate = new Date(data.end_date_achievement_reports).toLocaleDateString('en-GB', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
    
                    // Calculate working days if actual_working_days is 0 or null
                    let workingDays = data.actual_working_days;
                    if (!workingDays || workingDays == 0) {
                        const start = new Date(data.start_date_achievement_reports);
                        const end = new Date(data.end_date_achievement_reports);
                        const diffTime = Math.abs(end - start);
                        workingDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // Add 1 to include both start and end dates
                    }
    
                    let html = `
                        <div class="report-details">
                            <!-- Stats Overview -->
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${workingDays}</div>
                                        <div class="stat-label">أيام العمل</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-person-workspace"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${data.name_Job}</div>
                                        <div class="stat-label">المسمى الوظيفي</div>
                                    </div>
                                </div>
                            </div>
    
                            <!-- Employee Information -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-person"></i>
                                    <h6>معلومات الموظف</h6>
                                </div>
                                <div class="info-grid">
                                    <div class="info-card">
                                        <div class="label">الاسم</div>
                                        <div class="value">${data.name_ar_contract}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">المشروع</div>
                                        <div class="value">${data.Project_name}</div>
                                    </div>
                                </div>
                            </div>
    
                            <!-- Time Period -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-calendar-range"></i>
                                    <h6>الفترة الزمنية</h6>
                                </div>
                                <div class="info-grid">
                                    <div class="info-card">
                                        <div class="label">تاريخ البداية</div>
                                        <div class="value">${startDate}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">تاريخ النهاية</div>
                                        <div class="value">${endDate}</div>
                                    </div>
                                </div>
                            </div>`;
    
                    if (data.data_todo_list_achievement && data.data_todo_list_achievement.tasks) {
                        html += `
                            <!-- Tasks Section -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-check2-square"></i>
                                    <h6>المهام المنجزة</h6>
                                </div>
                                <div class="task-list">
                                    ${data.data_todo_list_achievement.tasks.map((task, index) => {
                                        // Remove dash prefix from task name if it exists
                                        const cleanTaskName = task.taskName.replace(/^[\s\-–—]+/, '');
                                        return `
                                        <div class="task-item">
                                            <div class="task-number">${index + 1}</div>
                                            <div class="task-content">
                                                <div class="task-name">${cleanTaskName}</div>
                                                <div class="progress-wrapper">
                                                    <div class="progress">
                                                        <div class="progress-bar" role="progressbar" 
                                                             style="width: ${task.completionRate}%;" 
                                                             aria-valuenow="${task.completionRate}" 
                                                             aria-valuemin="0" 
                                                             aria-valuemax="100"></div>
                                                    </div>
                                                    <div class="progress-label">
                                                        <span>نسبة الإنجاز</span>
                                                        <span>${task.completionRate}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>`}).join('')}
                                </div>
                            </div>`;
                    }
    
                    // New Section: Achievement Schedule Table
                    if (data.data_todo_list_achievement && 
                        data.data_todo_list_achievement.jobDetails && 
                        data.data_todo_list_achievement.jobDetails.tasks &&
                        data.data_todo_list_achievement.jobDetails.tasks.length > 0) {
    
                        const isPercentageEvaluation = data.data_todo_list_achievement.evaluation.percentageEvaluation === "yes";
                        
                        html += `
                        <div class="report-section">
                            <div class="section-header">
                                <i class="bi bi-table"></i>
                                <h6>جدول التقرير الزمني للإنجاز</h6>
                            </div>
                            <div class="achievement-schedule-container">
                                <table class="achievement-schedule-table">
                                    <thead>
                                        <tr>
                                            <th>اسم المهمة</th>
                                            ${isPercentageEvaluation ? `
                                                <th>نسبة الإنجاز حتى الآن</th>
                                                <th>نسبة الإنجاز للفترة المحددة</th>
                                                <th>إجمالي نسبة الإنجاز</th>
                                            ` : `
                                                <th>أيام العمل المنجزة حتى الآن</th>
                                                <th>أيام العمل للفترة المحددة</th>
                                                <th>إجمالي أيام العمل</th>
                                            `}
                                            <th>ملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;
    
                        data.data_todo_list_achievement.jobDetails.tasks.forEach(task => {
                            // Remove dash prefix from task name if it exists
                            const cleanTaskName = task.taskName.replace(/^[\s\-–—]+/, '');
                            
                            html += `
                                <tr>
                                    <td>${cleanTaskName}</td>
                                    ${isPercentageEvaluation ? `
                                        <td>${task.total}%</td>
                                        <td>${task.completionRate}%</td>
                                        <td>${task.total}%</td>
                                    ` : `
                                        <td>${task.total} يوم</td>
                                        <td>${task.completionRate} يوم</td>
                                        <td>${task.total} يوم</td>
                                    `}
                                    <td>${task.notes || '-'}</td>
                                </tr>
                            `;
                        });
    
                        html += `
                                </tbody>
                            </table>
                        </div>
                        </div>`;
                    }
    
                    html += `</div>`;
    
                    $('.achievement-details').html(html);
                    $('#achievementReportModal').modal('show');
                }
            });
    }

    // Add this variable to store current merit report data
    let currentMeritData = null;

    // Update the viewMeritReport function to store the data
    function viewMeritReport(id) {
        $.get('get_report_contract_con/get_merit_report.php', { id: id })
            .done(function(response) {
                if (response.success) {
                    const data = response.data;
                    // Store the data for printing
                    currentMeritData = data;
                    
                    // Format dates using Gregorian calendar
                    const startDate = new Date(data.start_date_achievement_reports).toLocaleDateString('en-GB', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
                    const endDate = new Date(data.end_date_achievement_reports).toLocaleDateString('en-GB', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });

                    let html = `
                        <div class="report-details">
                            <!-- Stats Overview -->
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${data.actual_working_days}</div>
                                        <div class="stat-label">أيام العمل</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${parseFloat(data.today_wage).toFixed(2)} ${data.currency_type_name}</div>
                                        <div class="stat-label">الأجر اليومي</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-cash-stack text-success"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${parseFloat(data.total).toFixed(2)} ${data.currency_type_name}</div>
                                        <div class="stat-label">إجمالي الاستحقاق قبل الخصومات</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-cash-stack text-primary"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${parseFloat(data.total_after_discount).toFixed(2)} ${data.currency_type_name}</div>
                                        <div class="stat-label">صافي مبلغ الاستحقاق بعد الخصومات</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-file-text"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${data.id_achievement_reports}</div>
                                        <div class="stat-label">رقم تقرير الإنجاز</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Employee Information -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-person"></i>
                                    <h6>معلومات الموظف</h6>
                                </div>
                                <div class="info-grid">
                                    <div class="info-card">
                                        <div class="label">الاسم</div>
                                        <div class="value">${data.name_ar_contract}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">المشروع</div>
                                        <div class="value">${data.Project_name}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">المسمى الوظيفي</div>
                                        <div class="value">${data.name_Job}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">نوع العملة</div>
                                        <div class="value">${data.currency_type_name}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Financial Information -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-cash"></i>
                                    <h6>المعلومات المالية</h6>
                                </div>
                                <div class="info-grid">
                                    <div class="info-card">
                                        <div class="label">نسبة الضريبة</div>
                                        <div class="value">${parseFloat(data.tax_rate).toFixed(2)}%</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">السلف</div>
                                        <div class="value">${parseFloat(data.predecessor).toFixed(2)} ${data.currency_type_name}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">التأمين</div>
                                        <div class="value">${parseFloat(data.Insurance).toFixed(2)} ${data.currency_type_name}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">إجمالي الاستحقاق بعد الخصومات</div>
                                        <div class="value">${parseFloat(data.total_after_discount).toFixed(2)} ${data.currency_type_name}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Time Period -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-calendar-range"></i>
                                    <h6>الفترة الزمنية</h6>
                                </div>
                                <div class="info-grid">
                                    <div class="info-card">
                                        <div class="label">تاريخ البداية</div>
                                        <div class="value">${startDate}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">تاريخ النهاية</div>
                                        <div class="value">${endDate}</div>
                                    </div>
                                </div>
                            </div>`;

                        if (data.data_todo_list_merit && data.data_todo_list_merit.tasks) {
                            html += `
                                <!-- Tasks Section -->
                                <div class="report-section">
                                    <div class="section-header">
                                        <i class="bi bi-check2-square"></i>
                                        <h6>المهام المنجزة</h6>
                                    </div>
                                    <div class="task-list">`;
                            
                            data.data_todo_list_merit.tasks.forEach((task, index) => {
                                // Remove dash prefix from task name if it exists
                                const cleanTaskName = task.taskName.replace(/^[\s\-–—]+/, '');
                                
                                html += `
                                    <div class="task-item">
                                        <div class="task-number">${index + 1}</div>
                                        <div class="task-content">
                                            <div class="task-name">${cleanTaskName}</div>
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: ${task.completion}%;" 
                                                         aria-valuenow="${task.completion}" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100"></div>
                                                </div>
                                                <div class="progress-label">
                                                    <span>نسبة الإنجاز</span>
                                                    <span>${task.completion}%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>`;
                            });
                            
                            html += `
                                    </div>
                                </div>`;
                        }

                        html += `</div>`;
                        
                        $('.merit-details').html(html);
                        $('#meritReportModal').modal('show');
                    } else {
                        alert('خطأ في تحميل البيانات');
                    }
                })
                .fail(function() {
                    alert('خطأ في الاتصال بالخادم');
                });
    }

    // Update the view buttons to use the new functions
    $(document).ready(function() {
        // Update achievement report links
        $('a[href^="view_achievement.php"]').each(function() {
            const id = $(this).attr('href').split('=')[1];
            $(this).attr('href', 'javascript:void(0)');
            $(this).attr('onclick', `viewAchievementReport(${id})`);
        });

        // Update merit report links
        $('a[href^="view_merit.php"]').each(function() {
            const id = $(this).attr('href').split('=')[1];
            $(this).attr('href', 'javascript:void(0)');
            $(this).attr('onclick', `viewMeritReport(${id})`);
        });

        // Update attendance record links
        $('a[href^="view_attendance.php"]').each(function() {
            const id = $(this).attr('href').split('=')[1];
            $(this).attr('href', 'javascript:void(0)');
            $(this).attr('onclick', `viewAttendanceReport(${id})`);
        });
    });

    // Add this variable at the top of your script section to store the current attendance data
    let currentAttendanceData = null;

    // Update the viewAttendanceReport function to store the data
    function viewAttendanceReport(id) {
        $.get('get_report_contract_con/get_attendance_record.php', { id: id })
            .done(function(response) {
                if (response.success) {
                    const data = response.data;
                    currentAttendanceData = data;
                    
                    // Format dates
                    const startDate = new Date(data.start_date_permanent_diapers).toLocaleDateString('en-GB');
                    const endDate = new Date(data.end_date_permanent_diapers).toLocaleDateString('en-GB');
                    
                    // Parse the JSON data if it's a string
                    let attendanceData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
                    
                    // Ensure we have the correct structure
                    attendanceData = attendanceData || { AttendanceData: {} };
                    
                    // Calculate statistics correctly - similar to print_attendance_summary.php
                    let totalDays = 0;
                    let presentDays = 0;
                    let absentDays = 0;
                    let daysOff = 0;
                    let lateDays = 0;
                    let overtimeDays = 0;
                    
                    // Function to check if a day is a weekend (Friday or Saturday)
                    function isWeekendDay(dayName) {
                        return dayName === 'الجمعة' || dayName === 'السبت' || 
                               dayName === 'Fri' || dayName === 'Friday' || 
                               dayName === 'Sat' || dayName === 'Saturday';
                    }
                    
                    if (attendanceData && attendanceData.AttendanceData) {
                        totalDays = Object.keys(attendanceData.AttendanceData).length;
                        
                        Object.values(attendanceData.AttendanceData).forEach(dayData => {
                            // Check if it's a day off (Friday or Saturday)
                            const dayName = dayData.BasicInfo.DayName;
                            const isWeekend = isWeekendDay(dayName);
                            
                            if (isWeekend) {
                                daysOff++;
                                // Don't count weekend days as absent days
                            } else if (dayData.RegularShift.CheckIn.Status === 'حاضر') {
                                presentDays++;
                                // Check if late - uncomment if needed
                                // if (dayData.RegularShift.CheckIn.Time > '08:00:00') {
                                //    lateDays++;
                                // }
                            } else {
                                absentDays++;
                            }
                            
                            // Check overtime - uncomment if needed
                            // if (dayData.OvertimeShift.CheckIn.Time && 
                            //    dayData.OvertimeShift.CheckIn.Time !== '00:00' && 
                            //    dayData.OvertimeShift.CheckIn.Time !== '--:--') {
                            //    overtimeDays++;
                            // }
                        });
                    }
                    
                    let html = `
                        <div class="report-details">
                            <!-- Stats Overview -->
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-range"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${totalDays}</div>
                                        <div class="stat-label">إجمالي الأيام</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-check text-success"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${presentDays}</div>
                                        <div class="stat-label">أيام الحضور</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-x text-danger"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${absentDays}</div>
                                        <div class="stat-label">أيام الغياب</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-week text-info"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-value">${daysOff}</div>
                                        <div class="stat-label">أيام العطلة</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Employee Information -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-person"></i>
                                    <h6>معلومات الموظف</h6>
                                </div>
                                <div class="info-grid">
                                    <div class="info-card">
                                        <div class="label">الاسم</div>
                                        <div class="value">${data.name_ar_contract}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">المشروع</div>
                                        <div class="value">${data.Project_name}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">المسمى الوظيفي</div>
                                        <div class="value">${data.name_Job}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Time Period -->
                            <div class="report-section">
                                <div class="section-header">
                                    <i class="bi bi-calendar-range"></i>
                                    <h6>الفترة الزمنية</h6>
                                </div>
                                <div class="info-grid">
                                    <div class="info-card">
                                        <div class="label">تاريخ البداية</div>
                                        <div class="value">${startDate}</div>
                                    </div>
                                    <div class="info-card">
                                        <div class="label">تاريخ النهاية</div>
                                        <div class="value">${endDate}</div>
                                    </div>
                                </div>
                            </div>`;

                    // Add Summary Section
                    html += `
                        <div class="report-section">
                            <div class="section-header">
                                <i class="bi bi-clipboard-data"></i>
                                <h6>ملخص الحضور والغياب</h6>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered attendance-summary-table">
                                    <thead>
                                        <tr>
                                            <th>إجمالي أيام الفترة</th>
                                            <th>أيام الحضور</th>
                                            <th>أيام الغياب</th>
                                            <th>أيام العطلة</th>
                                            <th>نسبة الحضور</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>${totalDays}</td>
                                            <td>
                                                <span class="badge bg-success-subtle text-success">
                                                    ${presentDays}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger-subtle text-danger">
                                                    ${absentDays}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info-subtle text-info">
                                                    ${daysOff}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary-subtle text-primary">
                                                    ${(totalDays - daysOff) > 0 ? ((presentDays / (totalDays - daysOff)) * 100).toFixed(2) : 0}%
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>`;

                    // Add Attendance Details Table
                    html += `
                        <div class="report-section">
                            <div class="section-header">
                                <i class="bi bi-calendar-check"></i>
                                <h6>تفاصيل الحضور والغياب</h6>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered attendance-details-table">
                                    <thead>
                                        <tr>
                                            <th>اليوم</th>
                                            <th>التاريخ</th>
                                            <th colspan="2">الدوام العادي</th>
                                            <th colspan="2">الدوام الإضافي</th>
                                            <th>الملاحظات</th>
                                        </tr>
                                        <tr>
                                            <th></th>
                                            <th></th>
                                            <th>وقت الحضور</th>
                                            <th>وقت الانصراف</th>
                                            <th>وقت الحضور</th>
                                            <th>وقت الانصراف</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>`;

                    // Add attendance data rows
                    if (attendanceData && attendanceData.AttendanceData) {
                        Object.entries(attendanceData.AttendanceData).forEach(([dayNumber, dayData]) => {
                            html += `
                                                                    <tr>
                                            <td>${dayData.BasicInfo.DayName}</td>
                                            <td>${dayData.BasicInfo.DayNumber}/${dayData.BasicInfo.MonthNumber}</td>
                                            <td>
                                                <div class="time-display">
                                                    <span class="status-badge ${isWeekendDay(dayData.BasicInfo.DayName) ? 'day-off' : 
                                                           dayData.RegularShift.CheckIn.Status === 'حاضر' ? 'present' : 
                                                           dayData.RegularShift.CheckIn.Status === 'غائب' ? 'absent' : 
                                                           dayData.RegularShift.CheckIn.Status === 'إجازة' ? 'leave' : ''}">
                                                        ${isWeekendDay(dayData.BasicInfo.DayName) ? 'يوم عطلة' : dayData.RegularShift.CheckIn.Status}
                                                    </span>
                                                    <span class="time-value">${dayData.RegularShift.CheckIn.Time}</span>
                                                </div>
                                            </td>
                                    <td>
                                        <div class="time-display">
                                            <span class="status-badge ${dayData.RegularShift.CheckOut.Status === 'منصرف' ? 'present' : 
                                                   dayData.RegularShift.CheckOut.Status === 'غير منصرف' ? 'absent' : ''}">
                                                ${dayData.RegularShift.CheckOut.Status}
                                            </span>
                                            <span class="time-value">${dayData.RegularShift.CheckOut.Time}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="time-display">
                                            <span class="status-badge ${dayData.OvertimeShift.CheckIn.Status === 'حاضر' ? 'present' : 
                                                   dayData.OvertimeShift.CheckIn.Status === 'غائب' ? 'absent' : ''}">
                                                ${dayData.OvertimeShift.CheckIn.Status}
                                            </span>
                                            <span class="time-value">${dayData.OvertimeShift.CheckIn.Time}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="time-display">
                                            <span class="status-badge ${dayData.OvertimeShift.CheckOut.Status === 'منصرف' ? 'present' : 
                                                   dayData.OvertimeShift.CheckOut.Status === 'غير منصرف' ? 'absent' : ''}">
                                                ${dayData.OvertimeShift.CheckOut.Status}
                                            </span>
                                            <span class="time-value">${dayData.OvertimeShift.CheckOut.Time}</span>
                                        </div>
                                    </td>
                                    <td>${dayData.AdditionalNotes}</td>
                                </tr>`;
                        });
                    }

                    html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>`;

                    html += `</div>`;

                    $('.attendance-details').html(html);
                    $('#attendanceReportModal').modal('show'); // Changed from achievementReportModal
                }
            });
    }

    // Add the print function
    function printAttendanceRecord() {
        if (!currentAttendanceData) {
            alert('لا توجد بيانات متاحة للطباعة');
            return;
        }

        // Create a form and submit it to the print PHP file
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'print/print_attendance_record.php';
        form.target = '_blank';

        // Add the data as a hidden input
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'data';
        input.value = JSON.stringify(currentAttendanceData);
        form.appendChild(input);

        // Add the form to the document and submit it
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    // Add this function after the existing printAttendanceRecord function
    function printAttendanceSummary() {
        if (!currentAttendanceData) {
            alert('لا توجد بيانات متاحة للطباعة');
            return;
        }

        // Create a form and submit it to the print PHP file
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'print/print_attendance_summary.php';
        form.target = '_blank';

        // Add the data as a hidden input
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'data';
        input.value = JSON.stringify(currentAttendanceData);
        form.appendChild(input);

        // Add the form to the document and submit it
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    // Add the print function for merit reports
    function printMeritReport() {
        if (!currentMeritData) {
            alert('لا توجد بيانات متاحة للطباعة');
            return;
        }

        // Create a form and submit it to the print PHP file
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'print/print_merit_report.php';
        form.target = '_blank';

        // Add the data as a hidden input
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'data';
        input.value = JSON.stringify(currentMeritData);
        form.appendChild(input);

        // Add the form to the document and submit it
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    // Add the print function for merit reports with discount details
    function printMeritReportWithDiscount() {
        if (!currentMeritData) {
            alert('لا توجد بيانات متاحة للطباعة');
            return;
        }

        // Create a form and submit it to the print PHP file
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'print/print_merit_report_with_discount.php';
        form.target = '_blank';

        // Add the data as a hidden input
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'data';
        input.value = JSON.stringify(currentMeritData);
        form.appendChild(input);

        // Add the form to the document and submit it
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    // Add this function to handle printing achievement reports
    function printAchievementReport() {
        if (!currentAchievementData) {
            alert('لا توجد بيانات متاحة للطباعة');
            return;
        }

        console.log('Printing achievement report with data:', currentAchievementData); // Add logging

        // Create a form and submit it to the print PHP file
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'print/print_achievement_report.php';
        form.target = '_blank';

        // Add the data as a hidden input
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'data';
        input.value = JSON.stringify(currentAchievementData);
        form.appendChild(input);

        try {
            // Add the form to the document and submit it
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        } catch (error) {
            console.error('Error submitting print form:', error);
            alert('حدث خطأ أثناء محاولة الطباعة');
        }
    }
    </script>

    <!-- Add this JavaScript at the end of the file -->
    <script>
    $(document).ready(function() {
        // Function to show notification
        function showNotification(message, type = 'success') {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            
            // Remove any existing alerts
            $('.alert').remove();
            
            // Add the new alert at the top of the document-upload-area
            $('.document-upload-area').prepend(alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                $('.alert').fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000);
        }

        $('#documentUploadForm').on('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const form = $(this);
            const submitBtn = form.find('button[type="submit"]');
            const originalText = submitBtn.html();
            
            form.addClass('uploading');
            submitBtn.html('<i class="bi bi-arrow-repeat spin me-2"></i>جاري الرفع...').prop('disabled', true);
            
            var formData = new FormData(this);
            
            $.ajax({
                url: 'get_report_contract_con/handle_contract_document.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    try {
                        const result = typeof response === 'string' ? JSON.parse(response) : response;
                        if (result.success) {
                            showNotification(result.message, 'success');
                            
                            // Reset the file input
                            form[0].reset();
                            // Reset the file name display
                            form.find('.upload-zone').removeClass('file-selected');
                            form.find('.upload-zone-text').html('<i class="bi bi-cloud-arrow-up"></i><span>اختر ملف PDF للرفع</span>');
                            
                            // Reload page to show new document with view/download buttons
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            showNotification(result.message, 'error');
                        }
                    } catch (e) {
                        showNotification('حدث خطأ غير متوقع', 'error');
                    }
                },
                error: function() {
                    showNotification('حدث خطأ في الاتصال بالخادم', 'error');
                },
                complete: function() {
                    // Reset loading state
                    form.removeClass('uploading');
                    submitBtn.html(originalText).prop('disabled', false);
                }
            });
        });

        // Add event listener to show file name when a file is selected
        $('.document-upload-form input[type="file"]').on('change', function() {
            const fileName = $(this).val().split('\\').pop();
            if (fileName) {
                const uploadZone = $(this).closest('.upload-zone');
                const uploadZoneText = uploadZone.find('.upload-zone-text');
                uploadZone.addClass('file-selected');
                uploadZoneText.html(`
                    <i class="bi bi-file-earmark-pdf text-success"></i>
                    <span class="selected-file-name">${fileName}</span>
                `);
            } else {
                const uploadZone = $(this).closest('.upload-zone');
                const uploadZoneText = uploadZone.find('.upload-zone-text');
                uploadZone.removeClass('file-selected');
                uploadZoneText.html('<i class="bi bi-cloud-arrow-up"></i><span>اختر ملف PDF للرفع</span>');
            }
        });

        // Add this CSS to style the spinning icon and selected file name
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .selected-file-name {
                font-weight: 500;
                color: var(--primary-color);
                word-break: break-word;
                max-width: 100%;
                display: block;
                margin-top: 5px;
            }
            .upload-zone.file-selected {
                border-color: var(--primary-color);
                background-color: rgba(46, 115, 252, 0.05);
                border-style: solid;
            }
            .upload-zone.file-selected .upload-zone-text {
                color: var(--primary-color);
            }
        `;
        document.head.appendChild(style);
    });
    </script>
</body>
</html> 
