// Sidebar Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar sections
    initSidebarSections();
    
    // Initialize mobile sidebar toggle
    initMobileSidebar();
    
    // Initialize theme toggle
    initThemeToggle();
});

// Initialize sidebar sections
function initSidebarSections() {
    const sectionToggles = document.querySelectorAll('.section-toggle');
    
    sectionToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const sectionId = this.getAttribute('data-section');
            const sectionItems = document.getElementById(`${sectionId}-section`);
            
            // Toggle active class on the button
            this.classList.toggle('active');
            
            // Toggle open class on the section items
            sectionItems.classList.toggle('open');
            
            // If this section is being opened, close others
            if (sectionItems.classList.contains('open')) {
                // Close other sections
                sectionToggles.forEach(otherToggle => {
                    if (otherToggle !== this) {
                        const otherSectionId = otherToggle.getAttribute('data-section');
                        const otherSectionItems = document.getElementById(`${otherSectionId}-section`);
                        
                        otherToggle.classList.remove('active');
                        otherSectionItems.classList.remove('open');
                    }
                });
            }
        });
    });
    
    // Open the section that contains the active page
    const activeSubItem = document.querySelector('.sub-item.active');
    if (activeSubItem) {
        const parentSection = activeSubItem.closest('.section-items');
        const sectionToggle = document.querySelector(`[data-section="${parentSection.id.replace('-section', '')}"]`);
        
        if (sectionToggle && parentSection) {
            sectionToggle.classList.add('active');
            parentSection.classList.add('open');
        }
    }
}

// Initialize mobile sidebar
function initMobileSidebar() {
    const toggleButton = document.getElementById('toggle-sidebar');
    
    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            document.body.classList.toggle('sidebar-open');
        });
    }
}

// Initialize theme toggle
function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;
    const icon = themeToggle.querySelector('i');
    
    // Load saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    body.setAttribute('data-theme', savedTheme);
    updateThemeIcon(icon, savedTheme);
    
    // Theme toggle handler
    themeToggle.addEventListener('click', function() {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeIcon(icon, newTheme);
        
        // Dispatch theme change event
        const event = new CustomEvent('themeChanged', { 
            detail: { theme: newTheme } 
        });
        document.dispatchEvent(event);
    });
}

// Update theme icon
function updateThemeIcon(icon, theme) {
    if (theme === 'dark') {
        icon.classList.remove('bi-moon');
        icon.classList.add('bi-sun');
    } else {
        icon.classList.remove('bi-sun');
        icon.classList.add('bi-moon');
    }
} 