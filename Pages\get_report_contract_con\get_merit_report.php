<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    // Read database connection details
    $file = fopen(dirname(__DIR__) . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    if (!isset($_GET['id'])) {
        throw new Exception("Report ID is required");
    }

    $id = (int)$_GET['id'];

    $sql = "SELECT mr.*, 
                   ar.start_date_achievement_reports,
                   ar.end_date_achievement_reports,
                   ar.data_todo_list_achievement,
                   c.name_Job,
                   c.contract_type,
                   c.Type_currency,
                   e.name_ar_contract,
                   p.Project_name
            FROM merit_reports mr
            JOIN achievement_reports ar ON mr.id_achievement_reports = ar.id_achievement_reports
            JOIN contract c ON mr.id_contract = c.id_contract
            JOIN employees e ON c.id_employees = e.id_employees
            JOIN Project p ON mr.id_Project = p.id_Project
            WHERE mr.id_merit_reports = ?";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception("Report not found");
    }

    $report = $result->fetch_assoc();
    
    // Add currency type name
    function getCurrencyTypeName($type) {
        switch ($type) {
            case 1: return "دولار";
            case 2: return "ريال";
            default: return "غير محدد";
        }
    }

    $report['currency_type_name'] = getCurrencyTypeName($report['Type_currency']);
    
    // Decode the JSON data for tasks
    $report['data_todo_list_achievement'] = json_decode($report['data_todo_list_achievement'], true);

    echo json_encode([
        'success' => true,
        'data' => $report
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} 