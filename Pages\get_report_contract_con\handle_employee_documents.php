<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// List of valid document types
$valid_document_types = [
    'Identity_document',
    'test_results_document',
    'Interview_results_document',
    'guarantee_document',
    'medical_examination_document',
    'cv_document',
    'request_for_resignation_document',
    'reconciliation_document',
    'resignation_approval_document'
];

// Read database connection details
try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Handle file upload
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isset($_POST['employee_id']) || !isset($_POST['document_type']) || !isset($_FILES['document_file'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing required parameters']);
            exit;
        }

        $employee_id = $_POST['employee_id'];
        $document_type = $_POST['document_type'];
        $file = $_FILES['document_file'];

        // Validate document type
        if (!in_array($document_type, $valid_document_types)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid document type']);
            exit;
        }

        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            http_response_code(400);
            echo json_encode(['error' => 'File upload failed']);
            exit;
        }

        if ($file['type'] !== 'application/pdf') {
            http_response_code(400);
            echo json_encode(['error' => 'Only PDF files are allowed']);
            exit;
        }

        // Read file content
        $content = file_get_contents($file['tmp_name']);
        if ($content === false) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to read file content']);
            exit;
        }

        // Update database
        $sql = "UPDATE employees SET " . $document_type . " = ? WHERE id_employees = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("si", $content, $employee_id);

        if (!$stmt->execute()) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to save document']);
            exit;
        }

        echo json_encode(['success' => true]);
        exit;
    }

    // Handle file download
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        if (!isset($_GET['employee_id']) || !isset($_GET['document_type'])) {
            http_response_code(400);
            echo 'Missing required parameters';
            exit;
        }

        $employee_id = $_GET['employee_id'];
        $document_type = $_GET['document_type'];
        $action = isset($_GET['action']) ? $_GET['action'] : 'download'; // Default to download

        // Validate document type
        if (!in_array($document_type, $valid_document_types)) {
            http_response_code(400);
            echo 'Invalid document type';
            exit;
        }

        // Fetch document from database
        $sql = "SELECT " . $document_type . " FROM employees WHERE id_employees = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $employee_id);
        
        if (!$stmt->execute()) {
            http_response_code(500);
            echo 'Failed to fetch document';
            exit;
        }

        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        if (!$row || !$row[$document_type]) {
            http_response_code(404);
            echo 'Document not found';
            exit;
        }

        // Output file
        header('Content-Type: application/pdf');
        if ($action === 'view') {
            // For viewing in browser, use inline content disposition
            header('Content-Disposition: inline; filename="' . $document_type . '.pdf"');
        } else {
            // For downloading, use attachment content disposition
            header('Content-Disposition: attachment; filename="' . $document_type . '.pdf"');
        }
        echo $row[$document_type];
        exit;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
} 