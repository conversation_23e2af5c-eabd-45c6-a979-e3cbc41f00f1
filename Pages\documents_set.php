<?php
session_start();

// Read the database connection details from the text file
$file = fopen("connection\\one.txt", "r");
$servername = trim(fgets($file));
$username = trim(fgets($file));
$password = trim(fgets($file));
$dbname = trim(fgets($file));
fclose($file);

// Function to resize and compress an image
function resizeAndCompressImage($imageData, $maxWidth = 300, $maxHeight = 300, $quality = 75) {
    // Create image from string
    $image = imagecreatefromstring($imageData);
    if (!$image) {
        return false;
    }
    
    // Get original dimensions
    $origWidth = imagesx($image);
    $origHeight = imagesy($image);
    
    // Calculate new dimensions while maintaining aspect ratio
    $ratio = min($maxWidth / $origWidth, $maxHeight / $origHeight);
    $newWidth = round($origWidth * $ratio);
    $newHeight = round($origHeight * $ratio);
    
    // Create a new image with the new dimensions
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG images
    imagealphablending($newImage, false);
    imagesavealpha($newImage, true);
    $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
    imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
    
    // Resize the image
    imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $origWidth, $origHeight);
    
    // Output to a buffer
    ob_start();
    imagejpeg($newImage, null, $quality);
    $compressedImage = ob_get_contents();
    ob_end_clean();
    
    // Free up memory
    imagedestroy($image);
    imagedestroy($newImage);
    
    return $compressedImage;
}

try {
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if a record exists
    $stmt = $db->query("SELECT * FROM documents LIMIT 1");
    $document = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $name_ar = $_POST['name_ar'];
        $name_en = $_POST['name_en'];
        $address_ar = $_POST['address_ar'];
        $address_en = $_POST['address_en'];
        $numbers = $_POST['numbers'];
        
        // Handle logo upload
        $logo = null;
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $originalLogo = file_get_contents($_FILES['logo']['tmp_name']);
            // Resize and compress the logo
            $logo = resizeAndCompressImage($originalLogo, 300, 300, 80);
            
            if (!$logo) {
                throw new Exception("Invalid image format. Please upload a valid image file.");
            }
        }
        
        if ($document) {
            // Update existing record
            $sql = "UPDATE documents SET 
                    name_ar = :name_ar,
                    name_en = :name_en,
                    address_ar = :address_ar,
                    address_en = :address_en,
                    numbers = :numbers";
            
            if ($logo) {
                $sql .= ", logo = :logo";
            }
            
            $sql .= " WHERE id = :id";
            
            $stmt = $db->prepare($sql);
            $params = [
                ':name_ar' => $name_ar,
                ':name_en' => $name_en,
                ':address_ar' => $address_ar,
                ':address_en' => $address_en,
                ':numbers' => $numbers,
                ':id' => $document['id']
            ];
            
            if ($logo) {
                $params[':logo'] = $logo;
            }
            
        } else {
            // Insert new record
            if (!$logo) {
                throw new Exception("Logo is required for the first record");
            }
            
            $sql = "INSERT INTO documents (logo, name_ar, name_en, address_ar, address_en, numbers) 
                    VALUES (:logo, :name_ar, :name_en, :address_ar, :address_en, :numbers)";
            
            $stmt = $db->prepare($sql);
            $params = [
                ':logo' => $logo,
                ':name_ar' => $name_ar,
                ':name_en' => $name_en,
                ':address_ar' => $address_ar,
                ':address_en' => $address_en,
                ':numbers' => $numbers
            ];
        }
        
        $stmt->execute($params);
        echo '<div class="alert alert-success" role="alert">تم حفظ البيانات بنجاح</div>';
        
        // Refresh document data
        $stmt = $db->query("SELECT * FROM documents LIMIT 1");
        $document = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger" role="alert">خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage() . '</div>';
} catch (Exception $e) {
    echo '<div class="alert alert-danger" role="alert">' . $e->getMessage() . '</div>';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية - معلومات المؤسسة</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        :root {
            --font-family: 'Cairo', sans-serif;
        }
        
        body {
            font-family: var(--font-family);
        }
        
        /* Add styles from details_settings.php */
        .form-group {
            margin-bottom: 20px;
        }

        /* Add sidebar layout fixes */
        #sidebar {
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            width: 250px;
            z-index: 1000;
            padding: 1rem;
            background-color: var(--sidebar-bg);
            transition: transform 0.3s ease;
        }

        #content {
            margin-right: 270px;
            margin-left: 20px;
            padding: 20px;
            transition: margin 0.3s ease;
            max-width: calc(100vw - 290px);
        }

        @media (max-width: 991.98px) {
            #sidebar {
                transform: translateX(100%);
            }
            
            #content {
                margin-right: 20px;
                margin-left: 20px;
                max-width: calc(100vw - 40px);
            }
            
            body.sidebar-open #sidebar {
                transform: translateX(0);
            }
        }

        /* Existing styles continue */
        .logo-section {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo-section img {
            max-width: 200px;
            max-height: 200px;
            margin-left: 20px;
            border: 2px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
            font-size: 16px;
            resize: vertical;
        }

        .submit-btn {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background-color: #45a049;
        }

        .modal-content {
            border-radius: 8px;
        }

        .modal-header {
            background-color: var(--bs-primary);
            color: white;
            border-radius: 8px 8px 0 0;
        }

        .modal-header .btn-close {
            filter: brightness(0) invert(1);
        }

        .form-label {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
        }

        .form-control-sm {
            font-size: 0.9rem;
        }

        textarea.form-control-sm {
            width: 100%;
            max-width: 100%;
        }

        .current-info {
            font-size: 0.9rem;
        }

        .current-info strong {
            color: var(--bs-primary);
        }

        .info-group {
            width: 100%;
            overflow-wrap: break-word;
            word-wrap: break-word;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .info-group:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08) !important;
        }

        .logo-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 120px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .logo-wrapper:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08) !important;
        }

        .section-title {
            position: relative;
        }

        .section-title i {
            font-size: 1.2rem;
        }

        .info-container {
            position: relative;
        }

        .info-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--bs-primary) 50%, transparent);
            opacity: 0.2;
        }

        [data-theme="dark"] .info-group,
        [data-theme="dark"] .logo-wrapper {
            background-color: var(--bs-dark) !important;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .card.bg-light {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        /* Dark theme improvements */
        [data-theme="dark"] {
            --card-bg: #2d3748;
            --text-muted: #a0aec0;
            --border-color: rgba(255, 255, 255, 0.1);
            --hover-bg: #3a4757;
        }

        [data-theme="dark"] .card {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .card.bg-light {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        [data-theme="dark"] .info-group,
        [data-theme="dark"] .logo-wrapper,
        [data-theme="dark"] .modal-content {
            background-color: var(--card-bg) !important;
            border: 1px solid var(--border-color);
        }

        [data-theme="dark"] .info-group:hover {
            background-color: var(--hover-bg) !important;
        }

        [data-theme="dark"] .modal-header {
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .modal-footer {
            border-top-color: var(--border-color);
        }

        [data-theme="dark"] .text-muted {
            color: var(--text-muted) !important;
        }

        [data-theme="dark"] .form-control,
        [data-theme="dark"] .form-control-sm {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: #fff;
        }

        [data-theme="dark"] .form-control:focus,
        [data-theme="dark"] .form-control-sm:focus {
            background-color: var(--hover-bg);
            border-color: var(--bs-primary);
            box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
        }

        /* Alert styles for both themes */
        .alert {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .alert-success {
            background-color: rgba(72, 187, 120, 0.1);
            color: #48bb78;
        }

        [data-theme="dark"] .alert-success {
            background-color: rgba(72, 187, 120, 0.2);
            color: #9ae6b4;
        }

        .alert-danger {
            background-color: rgba(245, 101, 101, 0.1);
            color: #f56565;
        }

        [data-theme="dark"] .alert-danger {
            background-color: rgba(245, 101, 101, 0.2);
            color: #feb2b2;
        }

        /* Improved transitions */
        .modal-content,
        .alert,
        .info-group,
        .form-control,
        .btn {
            transition: all 0.2s ease-in-out;
        }

        /* File input improvements */
        [data-theme="dark"] input[type="file"]::file-selector-button {
            background-color: var(--hover-bg);
            color: #fff;
            border-color: var(--border-color);
        }

        [data-theme="dark"] input[type="file"]:hover::file-selector-button {
            background-color: var(--bs-primary);
        }

        /* Button improvements */
        [data-theme="dark"] .btn-primary {
            border: none;
        }

        [data-theme="dark"] .btn-close {
            filter: invert(1) grayscale(100%) brightness(200%);
        }

        /* Placeholder text color */
        [data-theme="dark"] .form-control::placeholder,
        [data-theme="dark"] .form-control-sm::placeholder {
            color: var(--text-muted);
        }

        /* Scrollbar improvements */
        [data-theme="dark"] ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        [data-theme="dark"] ::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        [data-theme="dark"] ::-webkit-scrollbar-thumb {
            background: var(--hover-bg);
            border-radius: 4px;
        }

        [data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
            background: var(--bs-primary);
        }

        /* Modal backdrop adjustment */
        [data-theme="dark"] .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.7);
        }

        /* Empty state improvements */
        [data-theme="dark"] .text-center.py-5 i {
            opacity: 0.5;
        }

        /* Focus states */
        [data-theme="dark"] .btn:focus,
        [data-theme="dark"] .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
        }

        /* Add these new container styles */
        .container {
            padding-right: 15px;
            padding-left: 15px;
            width: 100%;
            max-width: 100% !important;
        }

        /* Update card and info-group styles */
        .card {
            width: 100%;
            overflow: hidden;
        }

        /* Update modal styles */
        .modal-dialog.modal-lg {
            max-width: min(95vw, 800px);
            margin: 1.75rem auto;
        }

        @media (max-width: 576px) {
            .modal-dialog.modal-lg {
                margin: 0.5rem;
            }
        }

        /* Update row gutters */
        .row.g-4 {
            margin-right: -10px;
            margin-left: -10px;
        }

        .row.g-4 > [class*="col-"] {
            padding-right: 10px;
            padding-left: 10px;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <!-- Main Content -->
    <main id="content">
        <div class="container-fluid p-0">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">إدارة معلومات المؤسسة</h1>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#documentModal">
                            <i class="bi bi-pencil-square"></i> تعديل معلومات المؤسسة
                        </button>
                    </div>
                    
                    <?php if ($document): ?>
                    <div class="current-info">
                        <div class="card bg-light border-0">
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="logo-wrapper p-3 bg-white rounded shadow-sm mb-2">
                                                <img src="data:image/jpeg;base64,<?php echo base64_encode($document['logo']); ?>" 
                                                     alt="الشعار الحالي" 
                                                     class="img-fluid"
                                                     style="max-height: 120px;">
                                            </div>
                                            <span class="text-muted small">الشعار الحالي</span>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="info-container">
                                            <div class="section-title d-flex align-items-center mb-3">
                                                <i class="bi bi-building me-2 text-primary"></i>
                                                <h5 class="mb-0">معلومات المؤسسة الحالية</h5>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <div class="info-group p-3 bg-white rounded shadow-sm">
                                                        <h6 class="text-primary mb-3">المعلومات باللغة العربية</h6>
                                                        <div class="mb-2">
                                                            <label class="small text-muted d-block">اسم الوكالة:</label>
                                                            <p class="mb-2"><?php echo htmlspecialchars($document['name_ar']); ?></p>
                                                        </div>
                                                        <div>
                                                            <label class="small text-muted d-block">العنوان:</label>
                                                            <p class="mb-0"><?php echo htmlspecialchars($document['address_ar']); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="info-group p-3 bg-white rounded shadow-sm">
                                                        <h6 class="text-primary mb-3">المعلومات باللغة الإنجليزية</h6>
                                                        <div class="mb-2">
                                                            <label class="small text-muted d-block">Agency Name:</label>
                                                            <p class="mb-2"><?php echo htmlspecialchars($document['name_en']); ?></p>
                                                        </div>
                                                        <div>
                                                            <label class="small text-muted d-block">Address:</label>
                                                            <p class="mb-0"><?php echo htmlspecialchars($document['address_en']); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="info-group p-3 bg-white rounded shadow-sm">
                                                        <h6 class="text-primary mb-3">معلومات الاتصال</h6>
                                                        <div>
                                                            <label class="small text-muted d-block">الأرقام:</label>
                                                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($document['numbers'])); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                        <p class="mt-3 text-muted">لا توجد معلومات مؤسسة مسجلة بعد. يرجى إضافة المعلومات باستخدام النموذج.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Document Modal -->
            <div class="modal fade" id="documentModal" tabindex="-1" aria-labelledby="documentModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="documentModalLabel">تحديث معلومات المؤسسة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form method="post" enctype="multipart/form-data">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">الشعار</label>
                                            <input type="file" name="logo" class="form-control form-control-sm" <?php echo !$document ? 'required' : ''; ?>>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <?php if ($document): ?>
                                            <img src="data:image/jpeg;base64,<?php echo base64_encode($document['logo']); ?>" 
                                                 alt="الشعار الحالي" 
                                                 class="img-fluid">
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الوكالة - العربي</label>
                                            <textarea name="name_ar" class="form-control form-control-sm" rows="2" required><?php echo $document['name_ar'] ?? ''; ?></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الوكالة - الإنجليزي</label>
                                            <textarea name="name_en" class="form-control form-control-sm" rows="2" required><?php echo $document['name_en'] ?? ''; ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">العنوان - العربي</label>
                                            <textarea name="address_ar" class="form-control form-control-sm" rows="2" required><?php echo $document['address_ar'] ?? ''; ?></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">العنوان - الإنجليزي</label>
                                            <textarea name="address_en" class="form-control form-control-sm" rows="2" required><?php echo $document['address_en'] ?? ''; ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الأرقام</label>
                                    <textarea name="numbers" class="form-control form-control-sm" rows="2" required><?php echo $document['numbers'] ?? ''; ?></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary">
                                        <?php echo $document ? 'تحديث معلومات المؤسسة' : 'حفظ معلومات المؤسسة'; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
    // Theme Management - handled by sidebar.php
// Mobile sidebar toggle
        document.getElementById('toggle-sidebar')?.addEventListener('click', () => {
            document.body.classList.toggle('sidebar-open');
        });
    });
    </script>
</body>
</html>


