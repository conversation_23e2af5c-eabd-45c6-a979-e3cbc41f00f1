<?php
// Increase memory limit and execution time for large files
ini_set('memory_limit', '512M');
set_time_limit(300); // 5 minutes

$message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['sqlfile'])) {
    // Database connection parameters
    $host = 'localhost';
    $dbname = 'mydb';
    $username = 'root';
    $password = 'root';

    // Upload directory configuration
    $uploadDir = 'uploads/';
    $uploadFile = $uploadDir . basename($_FILES['sqlfile']['name']);

    // Create upload directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    if (move_uploaded_file($_FILES['sqlfile']['tmp_name'], $uploadFile)) {
        try {
            // Connect to database
            $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Disable foreign key checks and autocommit
            $pdo->exec("SET foreign_key_checks = 0");
            $pdo->beginTransaction();

            $failedQueries = [];
            $success = true;

            // Process file in chunks to handle large files
            $handle = fopen($uploadFile, 'r');
            if ($handle) {
                $query = '';
                while (!feof($handle)) {
                    $line = fgets($handle);
                    
                    // Skip comments and empty lines
                    if (empty(trim($line)) || strpos(trim($line), '--') === 0 || strpos(trim($line), '/*') === 0) {
                        continue;
                    }

                    $query .= $line;
                    
                    // Execute query when we reach the end of it
                    if (substr(trim($line), -1) == ';') {
                        $query = trim($query);
                        if (!empty($query)) {
                            try {
                                $pdo->exec($query);
                            } catch (PDOException $e) {
                                $failedQueries[] = $query . ' - Error: ' . $e->getMessage();
                                $success = false;
                            }
                        }
                        $query = '';
                    }
                }
                fclose($handle);

                if ($success) {
                    $pdo->commit();
                    $message = "تم استعادة قاعدة البيانات بنجاح!";
                } else {
                    $pdo->rollBack();
                    $message = "تم استعادة قاعدة البيانات مع بعض الأخطاء.";
                }

                // Report failed queries if any
                if (!empty($failedQueries)) {
                    $message .= "<br>الاستعلامات التي فشلت:<br>";
                    foreach ($failedQueries as $failedQuery) {
                        $message .= htmlspecialchars($failedQuery) . "<br>";
                    }
                }
            }

            // Re-enable foreign key checks
            $pdo->exec("SET foreign_key_checks = 1");

            // Clean up the uploaded file
            unlink($uploadFile);

        } catch (PDOException $e) {
            $message = "خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage());
        }
    } else {
        $message = "فشل في تحميل الملف.";
    }
} else {
    $message = "قم باختيار ملف النسخه الاحتياطية.";
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة نسخة احتياطية</title>
    <link href='../assets/css/cairo-font.css' rel='stylesheet'>
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #1976D2;
            --success-color: #4CAF50;
            --danger-color: #f44336;
            --background-color: #f5f5f5;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--background-color);
            direction: rtl;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .form-container {
            background-color: #fff;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .file-upload {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            padding: 2rem;
            border: 2px dashed var(--primary-color);
            border-radius: 10px;
            margin-bottom: 1.5rem;
            transition: border-color 0.3s ease;
        }

        .file-upload:hover {
            border-color: var(--secondary-color);
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .file-upload label {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
        }

        .file-upload .icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .file-upload .text {
            color: #666;
            text-align: center;
        }

        .submit-button {
            width: 100%;
            padding: 1rem;
            font-size: 1.2rem;
            color: white;
            background-color: var(--primary-color);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .submit-button:hover {
            background-color: var(--secondary-color);
        }

        .message-box {
            margin-top: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            background-color: #E3F2FD;
            text-align: center;
        }

        .message-box h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .message-box p {
            color: #333;
            line-height: 1.6;
        }

        .success {
            background-color: #E8F5E9;
            color: var(--success-color);
        }

        .error {
            background-color: #FFEBEE;
            color: var(--danger-color);
        }

        .back-button {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            color: var(--primary-color);
            text-decoration: none;
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            margin-top: 1.5rem;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>استعادة نسخة احتياطية</h1>
        </div>

        <div class="form-container">
            <form action="" method="post" enctype="multipart/form-data">
                <div class="file-upload">
                    <label for="sqlfile">
                        <div class="icon">📁</div>
                        <div class="text">
                            <p>اختر ملف النسخة الاحتياطية</p>
                            <p style="font-size: 0.9rem; color: #888;">(.sql امتداد)</p>
                        </div>
                    </label>
                    <input type="file" id="sqlfile" name="sqlfile" accept=".sql" required>
                </div>
                <button type="submit" class="submit-button">استعادة النسخة الاحتياطية</button>
            </form>
        </div>

        <?php if (!empty($message)): ?>
        <div class="message-box <?php echo strpos($message, 'نجاح') !== false ? 'success' : 'error'; ?>">
            <h2>رسالة النظام</h2>
            <p><?php echo $message; ?></p>
        </div>
        <?php endif; ?>

        <div style="text-align: center;">
            <a href="xxx.php" class="back-button">العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        // Add file name display functionality
        document.getElementById('sqlfile').addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                this.previousElementSibling.querySelector('.text p:first-child').textContent = fileName;
            }
        });
    </script>
</body>
</html>
