<?php
// Get the current page name for active state
$currentPage = basename($_SERVER['PHP_SELF']);

// Load sidebar configuration from JSON
$sidebarConfig = json_decode(file_get_contents(__DIR__ . '/../config/sidebar.json'), true);
?>

<!-- Sidebar -->
<div id="sidebar">
    <div class="logo">
        <div><?php echo $sidebarConfig['logo']; ?></div>
    </div>
    
    <nav class="nav flex-column">
        <?php foreach ($sidebarConfig['menu_items'] as $item): ?>
            <?php if (isset($item['type']) && $item['type'] === 'dropdown'): ?>
                <!-- Dropdown Section -->
                <div class="nav-section">
                    <button class="nav-link section-toggle" data-section="<?php echo $item['id']; ?>">
                        <i class="bi <?php echo $item['icon']; ?>"></i>
                        <?php echo $item['title']; ?>
                    </button>
                    <div class="section-items" id="<?php echo $item['id']; ?>-section">
                        <?php foreach ($item['items'] as $subItem): ?>
                            <a href="<?php echo $subItem['url']; ?>" class="nav-link sub-item <?php echo $currentPage === basename($subItem['url']) ? 'active' : ''; ?>">
                                <i class="bi <?php echo $subItem['icon']; ?>"></i>
                                <?php echo $subItem['title']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Single Link -->
                <a href="<?php echo $item['url']; ?>" class="nav-link <?php echo $currentPage === basename($item['url']) ? 'active' : ''; ?>">
                    <i class="bi <?php echo $item['icon']; ?>"></i>
                    <?php echo $item['title']; ?>
                </a>
            <?php endif; ?>
        <?php endforeach; ?>
    </nav>

    <button id="theme-toggle" class="btn">
        <i class="bi bi-moon"></i>
        <span>تغيير المظهر</span>
    </button>
</div>

<!-- Mobile Sidebar Toggle -->
<button id="toggle-sidebar" class="btn btn-primary position-fixed d-lg-none" style="top: 1rem; right: 1rem; z-index: 1001;">
    <i class="bi bi-list"></i>
</button> 