:root {
    --sidebar-width: 280px;
    --header-height: 60px;
    --primary-color: #4a90e2;
    --secondary-color: #5c6ac4;
}

[data-theme="light"] {
    --bg-main: #f4f6f9;
    --bg-sidebar: #ffffff;
    --bg-card: #ffffff;
    --bg-input: #ffffff;
    --text-color: #2c3e50;
    --text-muted: #6c757d;
    --border-color: #e9ecef;
    --hover-color: #f8f9fa;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --input-border: #ced4da;
    --input-focus-border: #86b7fe;
    --input-focus-shadow: rgba(13, 110, 253, 0.25);
    --btn-text: #ffffff;
    --alert-bg: #ffffff;
}

[data-theme="dark"] {
    --bg-main: #1a1c23;
    --bg-sidebar: #242631;
    --bg-card: #2d303d;
    --bg-input: #1e2028;
    --text-color: #ffffff;
    --text-muted: #a0aec0;
    --border-color: #374151;
    --hover-color: #374151;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --input-border: #4b5563;
    --input-focus-border: #3b82f6;
    --input-focus-shadow: rgba(59, 130, 246, 0.25);
    --btn-text: #ffffff;
    --alert-bg: #2d303d;
    --list-group-bg: #2d303d;
    --list-group-color: #ffffff;
    --card-bg: #2d303d;
    --dropdown-bg: #242631;
    --dropdown-link-hover: #374151;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--bg-main);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
}

/* Sidebar Styles */
#sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    background-color: var(--bg-sidebar);
    border-left: 1px solid var(--border-color);
    padding: 1rem;
    transition: all 0.3s ease;
    box-shadow: -4px 0 10px var(--shadow-color);
    z-index: 1000;
    overflow-y: auto;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 2rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-color);
}

.logo img {
    width: 50px;
    height: 50px;
    margin-bottom: 0.5rem;
}

/* Navigation Styles */
.nav-link {
    color: var(--text-color);
    padding: 0.8rem 1rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-link:hover {
    background-color: var(--hover-color);
    transform: translateX(-5px);
}

.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-link i {
    font-size: 1.2rem;
    margin-left: 1rem;
    width: 24px;
    text-align: center;
}

/* Dropdown Styles */
.nav-item.dropdown {
    margin-bottom: 0.5rem;
    position: relative;
}

.dropdown-toggle {
    text-align: right;
    border: none;
    background: none;
    width: 100%;
    padding: 0.8rem 1rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
}

.dropdown-toggle:hover {
    background-color: var(--hover-color);
}

.dropdown-toggle i {
    font-size: 1.2rem;
    margin-left: 1rem;
    width: 24px;
    text-align: center;
}

.dropdown-toggle::after {
    margin-right: auto;
    margin-left: 0;
}

.dropdown-menu {
    background-color: var(--bg-card) !important;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 0.5rem;
    box-shadow: 0 4px 6px var(--shadow-color);
    min-width: calc(100% - 1rem);
    margin: 0 0.5rem !important;
    position: relative;
    top: 5px !important;
}

/* Add more specific selector for sidebar dropdown menus */
#sidebar .dropdown-menu {
    background-color: var(--bg-card) !important;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.dropdown-item {
    color: var(--text-color);
    padding: 0.8rem 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    text-decoration: none;
}

.dropdown-item:hover {
    background-color: var(--hover-color);
    color: var(--text-color);
    transform: translateX(-5px);
}

.dropdown-item i {
    font-size: 1.2rem;
    margin-left: 1rem;
    width: 24px;
    text-align: center;
}

/* Form Elements */
.form-control, .form-select {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    min-height: 45px;
    background-color: var(--bg-input);
    border-color: var(--input-border);
    color: var(--text-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.form-control:focus {
    background-color: var(--bg-input);
    border-color: var(--input-focus-border);
    color: var(--text-color);
    box-shadow: 0 0 0 0.25rem var(--input-focus-shadow);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-color);
}

/* Select2 Customization */
.select2-container .select2-selection--single {
    height: 45px !important;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 1.5 !important;
    padding-right: 0;
    padding-left: 20px;
    font-size: 1rem;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 45px !important;
    left: 1px;
    right: auto;
    width: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 1rem;
}

.select2-dropdown {
    border-radius: 0.375rem;
    font-size: 1rem;
}

.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-results__option {
    padding: 0.75rem 1rem;
}

/* Form Groups */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

/* Search Form Specific */
#searchForm .form-group {
    margin-bottom: 1.5rem;
}

#searchForm .select2-container {
    width: 100% !important;
}

#searchForm .btn-primary {
    padding: 0.75rem 2rem;
    font-size: 1rem;
}

/* Buttons */
.btn-primary {
    color: var(--btn-text);
}

.btn-secondary {
    color: var(--btn-text);
}

/* Alert Styles */
.alert {
    background-color: var(--alert-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    border-color: rgba(25, 135, 84, 0.2);
    color: #198754;
}

/* Main Content Area */
#content {
    margin-right: var(--sidebar-width);
    padding: 2rem;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Cards and Widgets */
.dashboard-card {
    background-color: var(--bg-card);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px var(--shadow-color);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow-color);
}

/* Card Styles */
.card {
    background-color: var(--bg-card);
    border-color: var(--border-color);
}

.card-title {
    color: var(--text-color);
}

/* Theme Toggle Button */
#theme-toggle {
    position: absolute;
    bottom: 1rem;
    width: calc(100% - 2rem);
    padding: 0.8rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-card);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

#theme-toggle:hover {
    background-color: var(--hover-color);
}

/* Responsive Design */
@media (max-width: 992px) {
    #sidebar {
        transform: translateX(var(--sidebar-width));
    }
    
    #content {
        margin-right: 0;
    }
    
    body.sidebar-open #sidebar {
        transform: translateX(0);
    }
    
    body.sidebar-open #content {
        margin-right: var(--sidebar-width);
    }
    
    .toggle-sidebar {
        display: block !important;
    }
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 240px;
    }
    
    #content {
        padding: 1rem;
    }
    
    .dashboard-card {
        padding: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-muted {
    color: var(--text-muted);
}

.shadow {
    box-shadow: 0 4px 6px var(--shadow-color);
}

.rounded {
    border-radius: 10px;
}

/* Additional Dark Theme Styles */
[data-theme="dark"] .card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .card-title,
[data-theme="dark"] .card-body h5,
[data-theme="dark"] .card-body h6,
[data-theme="dark"] .card-body strong,
[data-theme="dark"] .form-label,
[data-theme="dark"] label,
[data-theme="dark"] .card-body p,
[data-theme="dark"] .card-body span,
[data-theme="dark"] .card-body div {
    color: var(--text-color);
}

[data-theme="dark"] .card-body {
    color: var(--text-color);
}

[data-theme="dark"] .detail-value,
[data-theme="dark"] .detail-label,
[data-theme="dark"] .card-text {
    color: var(--text-color) !important;
}

[data-theme="dark"] .list-group-item {
    background-color: var(--list-group-bg);
    color: var(--list-group-color);
    border-color: var(--border-color);
}

[data-theme="dark"] input[type="date"],
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] textarea {
    background-color: var(--bg-input);
    color: var(--text-color);
    border-color: var(--input-border);
}

[data-theme="dark"] input[type="date"]:focus,
[data-theme="dark"] input[type="text"]:focus,
[data-theme="dark"] input[type="number"]:focus,
[data-theme="dark"] textarea:focus {
    background-color: var(--bg-input);
    color: var(--text-color);
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 0.25rem var(--input-focus-shadow);
}

[data-theme="dark"] .select2-container--default .select2-selection--single {
    background-color: var(--bg-input);
    border-color: var(--input-border);
    height: 45px !important;
}

[data-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--text-color);
    line-height: 1.5 !important;
}

[data-theme="dark"] .select2-dropdown {
    background-color: var(--bg-input);
    border-color: var(--input-border);
}

[data-theme="dark"] .select2-search--dropdown .select2-search__field {
    background-color: var(--bg-input);
    color: var(--text-color);
    border-color: var(--input-border);
    padding: 0.5rem;
}

[data-theme="dark"] .select2-container--default .select2-results__option {
    background-color: var(--bg-input);
    color: var(--text-color);
}

[data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-color);
    color: var(--text-color);
}

[data-theme="dark"] .select2-container--default.select2-container--open .select2-selection--single {
    border-color: var(--input-focus-border);
}

[data-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--text-muted);
}

/* Standard Select Dark Theme */
[data-theme="dark"] select.form-select {
    background-color: var(--bg-input);
    color: var(--text-color);
    border-color: var(--input-border);
}

[data-theme="dark"] select.form-select option {
    background-color: var(--bg-input);
    color: var(--text-color);
}

[data-theme="dark"] select.form-select:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 0.25rem var(--input-focus-shadow);
}

[data-theme="dark"] .dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color);
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: var(--dropdown-link-hover);
    color: var(--text-color);
}

[data-theme="dark"] .table {
    color: var(--text-color);
}

[data-theme="dark"] .table td,
[data-theme="dark"] .table th {
    border-color: var(--border-color);
}

[data-theme="dark"] .alert {
    background-color: var(--bg-card);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .form-select {
    background-color: var(--bg-input);
    color: var(--text-color);
    border-color: var(--input-border);
}

[data-theme="dark"] .form-select:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 0.25rem var(--input-focus-shadow);
}

[data-theme="dark"] .select2-container--default .select2-results__option {
    color: var(--text-color);
}

[data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--dropdown-link-hover);
    color: var(--text-color);
}

/* Calendar Styles */
.calendar-container {
    background: var(--bg-card);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    background: var(--bg-input);
    color: var(--text-color);
    transition: all 0.2s ease;
}

.calendar-day:hover:not(.disabled) {
    background: var(--hover-color);
}

.calendar-day.selected {
    background: var(--primary-color);
    color: #fff;
}

.calendar-day.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--bg-disabled);
}

.calendar-day.outside-month {
    visibility: hidden;
}

.calendar-weekday {
    text-align: center;
    font-weight: 600;
    color: var(--text-muted);
    padding: 0.5rem;
}

/* Date Range Display */
.date-range-display {
    background: var(--bg-input);
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 1rem;
    color: var(--text-color);
}

.calendar-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    max-width: 600px;
}

.calendar-day {
    width: 14%;
    height: 50px;
    text-align: center;
    line-height: 50px;
    border: 1px solid #ccc;
    cursor: pointer;
    user-select: none;
}

.calendar-day:hover {
    background-color: #f0f0f0;
}

.calendar-day.selected {
    background-color: #4CAF50;
    color: white;
}

.calendar-day.out-of-period {
    background-color: #ddd;
    pointer-events: none;
}
