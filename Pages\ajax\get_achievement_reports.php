<?php
session_start();
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    if (!isset($_GET['project_id']) || !isset($_GET['contract_id'])) {
        throw new Exception('Missing required parameters');
    }

    $projectId = intval($_GET['project_id']);
    $contractId = intval($_GET['contract_id']);
    $extensionId = isset($_GET['extension_id']) ? intval($_GET['extension_id']) : null;

    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) throw new Exception('Error reading configuration file');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    $query = "
        SELECT 
            ar.*,
            p.Project_name,
            c.name_ar_contract,
            c.contract_type,
            c.wage_contract,
            ec.extension_number
        FROM achievement_reports ar
        JOIN project p ON ar.id_Project = p.id_Project
        JOIN contract c ON ar.id_contract = c.id_contract
        LEFT JOIN extension_contract ec ON ar.id_extension_contract = ec.id_extension_contract
        WHERE ar.id_Project = ?
        AND ar.id_contract = ?";
    
    $params = [$projectId, $contractId];
    $types = "ii";
    
    if ($extensionId !== null) {
        $query .= " AND ar.id_extension_contract = ?";
        $params[] = $extensionId;
        $types .= "i";
    } else {
        $query .= " AND ar.id_extension_contract IS NULL";
    }
    
    $query .= " ORDER BY ar.start_date_achievement_reports DESC";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $reports = [];
    while ($row = $result->fetch_assoc()) {
        $reports[] = [
            'id' => $row['id_achievement_reports'],
            'project_name' => $row['Project_name'],
            'contract_name' => $row['name_ar_contract'],
            'start_date' => $row['start_date_achievement_reports'],
            'end_date' => $row['end_date_achievement_reports'],
            'todo_list' => json_decode($row['data_todo_list_achievement'], true),
            'extension_number' => $row['extension_number'],
            'contract_type' => $row['contract_type'],
            'wage_contract' => $row['wage_contract']
        ];
    }
    
    echo json_encode($reports);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?> 
