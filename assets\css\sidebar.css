/* Sidebar Styles */
#sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    background-color: var(--bg-sidebar);
    border-left: 1px solid var(--border-color);
    padding: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: -4px 0 10px var(--shadow-color);
    z-index: 1000;
    overflow-y: auto;
}

.logo {
    font-size: 1.25rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 1.5rem;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-color);
}

/* Navigation Styles */
.nav-link {
    color: var(--text-color);
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
    cursor: pointer;
    font-size: 0.9rem;
    position: relative;
}

.nav-link:hover {
    background-color: var(--hover-color);
    transform: translateX(-3px);
}

.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-link i {
    font-size: 1rem;
    margin-left: 0.75rem;
    width: 20px;
    text-align: center;
}

/* Section Toggle Styles */
.section-toggle {
    position: relative;
    justify-content: flex-start;
    padding-right: 0.75rem;
    margin-bottom: 0.15rem;
}

/* Remove the section-icon styles since we're removing the arrows */
.section-icon {
    display: none;
}

/* Section Items Styles */
.section-items {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    margin-right: 1rem;
    position: relative;
    padding-top: 0.15rem;
    padding-bottom: 0.15rem;
}

.section-items::before {
    content: '';
    position: absolute;
    right: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--text-color) 50%, transparent 50%);
    background-size: 6px 12px;
    opacity: 0.8;
}

.section-items.open {
    max-height: 500px;
}

.sub-item {
    padding-right: 2.25rem;
    font-size: 0.85rem;
    margin-bottom: 0.15rem;
    position: relative;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
    padding-top: 0.15rem;
    padding-bottom: 0.15rem;
}

.section-items.open .sub-item {
    opacity: 1;
    transform: translateX(0);
}

.sub-item:last-child {
    margin-bottom: 0;
}

.sub-item::before {
    content: '';
    position: absolute;
    right: 1.5rem;
    top: 50%;
    width: 1rem;
    height: 2px;
    background-color: var(--text-color);
    opacity: 0.8;
}

/* Add theme-specific styles for the lines */
[data-theme="dark"] .section-items::before,
[data-theme="dark"] .sub-item::before {
    background-color: white;
    opacity: 0.8;
}

[data-theme="light"] .section-items::before,
[data-theme="light"] .sub-item::before {
    background-color: black;
    opacity: 0.8;
}

/* Theme Toggle Button */
#theme-toggle {
    margin-top: 1.5rem;
    width: 100%;
    padding: 0.6rem;
    border-radius: 8px;
    background-color: var(--bg-card);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

#theme-toggle:hover {
    background-color: var(--hover-color);
}

#theme-toggle i {
    margin-left: 0.5rem;
    font-size: 0.9rem;
}

/* Mobile Sidebar Toggle */
#toggle-sidebar {
    display: none;
}

/* Responsive Styles */
@media (max-width: 992px) {
    #sidebar {
        transform: translateX(100%);
    }
    
    #toggle-sidebar {
        display: block;
    }
    
    body.sidebar-open #sidebar {
        transform: translateX(0);
    }
    
    body.sidebar-open #content {
        margin-right: var(--sidebar-width);
    }
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 220px;
    }
} 