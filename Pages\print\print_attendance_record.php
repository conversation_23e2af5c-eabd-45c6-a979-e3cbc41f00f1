<?php
// Start output buffering at the very beginning
ob_start();

// Include the TCPDF library
require_once('../../TCPDF-6.6.2/tcpdf.php');

// Get parameters from the request
$data = json_decode($_POST['data'], true);

if (!$data) {
    die('No data provided');
}

// Fetch institution data
$institutionData = null;
try {
    // Connect to the database
    $file = fopen(__DIR__ . "/../../Pages/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch institution data
    $stmt = $db->query("SELECT * FROM documents LIMIT 1");
    $institutionData = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die('Error fetching institution data: ' . $e->getMessage());
}

// Process institution data
if ($institutionData) {
    $agency_name_ar = explode("\n", trim($institutionData['name_ar']));
    $address_ar = explode("\n", trim($institutionData['address_ar']));
    $agency_name_en = explode("\n", trim($institutionData['name_en']));
    $address_en = explode("\n", trim($institutionData['address_en']));
    $numbers = explode("\n", trim($institutionData['numbers']));
    $logoData = $institutionData['logo'];
}

// Clean any output before generating PDF
ob_end_clean();

// Extend TCPDF class to customize header and footer
class MYPDF extends TCPDF {
    protected $institutionData;
    
    public function setInstitutionData($data) {
        $this->institutionData = $data;
    }

    public function Header() {
        if (!$this->institutionData) return;

        // Save current position
        $x = $this->GetX();
        $y = $this->GetY();

        // Calculate widths with equal margins
        $pageWidth = $this->getPageWidth();
        $margin = 2; // Equal margin on both sides
        $logoWidth = 35; // Increased from 25 to 35
        
        // New header layout - Logo at the top center
        if ($this->institutionData['logo']) {
            $this->Image('@'.$this->institutionData['logo'], 
                ($pageWidth - $logoWidth) / 2,
                $y + 2,
                $logoWidth, 
                $logoWidth,
                '', '', '', false, 300, '', false, false, 0);
        }
        
        // Add space after logo
        $y_after_logo = $y + $logoWidth + 5;
        
        // Draw purple lines with text - similar to offer.php style
        // Top purple lines (3 lines)
        $lineY = $y_after_logo;
        $lineHeight = 0.5;
        $lineGap = 1;
        $purpleColor = array(128, 0, 128); // Purple RGB
        
        $this->SetLineWidth(0.2);
        $this->SetDrawColor($purpleColor[0], $purpleColor[1], $purpleColor[2]);
        
        // Draw top 3 purple lines
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Add text in the middle - only English text between lines
        $this->SetY($y_after_logo + 1.5);
        $this->SetFont('times', 'B', 10);
        $this->SetTextColor($purpleColor[0], $purpleColor[1], $purpleColor[2]); // Set text color to purple
        $this->Cell(0, 6, 'Assistance for Response and Development', 0, 1, 'L'); // Changed from 'C' to 'L' for left alignment
        $this->SetTextColor(0, 0, 0); // Reset text color to black for the rest of the document
        
        // Draw bottom 3 purple lines
        $lineY = $y_after_logo + 8;
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Set ending position
        $this->SetY($lineY + 5);
    }

    public function Footer() {
        $this->SetY(-25);
        $this->SetFont('aealarabiya', '', 9);
        $this->Cell(0, 10, 'توقيع الموظف                                  الموارد البشرية                                  رئيس المؤسسة', 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
}

// Create new PDF document (Portrait A4)
$pdf = new MYPDF('P', 'mm', 'A4', true, 'UTF-8', false);

// Set institution data
$pdf->setInstitutionData([
    'agency_name_ar' => $agency_name_ar,
    'address_ar' => $address_ar,
    'agency_name_en' => $agency_name_en,
    'address_en' => $address_en,
    'numbers' => $numbers,
    'logo' => $logoData
]);

// Set document information
$pdf->SetCreator('HR System');
$pdf->SetAuthor('HR System');
$pdf->SetTitle('Attendance Record');

// Set equal minimal margins on both sides
$pdf->SetMargins(2, 55, 2);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 25);

// Add a page
$pdf->AddPage();

// Set font for Arabic text
$pdf->SetFont('aealarabiya', '', 9);

// Set font for header information
$pdf->SetFont('aealarabiya', 'B', 10);

// Add a light gray background for the header section
$pdf->SetFillColor(245, 245, 245);

// Calculate page width and adjust column widths
$pageWidth = 210 - 4; // Total page width minus total margins (2mm + 2mm)
$labelWidth = 22;
$valueWidth = 98; // Increased to use the extra space
$smallValueWidth = 55;
$spacing = 2;

// Store starting Y position
$startY = $pdf->GetY();

// Left side of first row (Project ID)
$leftStart = $pdf->GetMargins()['left'];
$pdf->SetX($leftStart);
$pdf->Cell($smallValueWidth, 6, $data['id_Project'], 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'رقم المشروع', 1, 0, 'R', false);
$pdf->Cell($spacing, 6, '', 0, 0, 'C', false);

// Right side - Project name with MultiCell
$rightX = $pdf->GetX();
$rightY = $pdf->GetY();
$pdf->MultiCell($valueWidth, 6, $data['Project_name'], 1, 'R', false);

// Get ending Y position after project name
$endY = $pdf->GetY();

// Add project label at the right height
$pdf->SetXY($rightX + $valueWidth, $rightY);
$pdf->Cell($labelWidth, $endY - $rightY, 'اسم المشروع', 1, 1, 'R', false);

// Ensure we start the second row at the correct position
$pdf->SetY($endY);

// Add minimal spacing between rows
$pdf->Ln(0.5); // Reduced from 1

// Second row (aligned with the first row)
$pdf->SetX($leftStart);
$pdf->Cell($smallValueWidth, 6, $data['name_ar_contract'], 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'اسم الموظف', 1, 0, 'R', false);
$pdf->Cell($spacing, 6, '', 0, 0, 'C', false);
$pdf->Cell($valueWidth, 6, $data['name_Job'], 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'المسمى الوظيفي', 1, 1, 'R', false);

$pdf->Ln(3); // Reduced from 5

// Add attendance record title with date range
$startDate = date('Y/m/d', strtotime($data['start_date_permanent_diapers']));
$endDate = date('Y/m/d', strtotime($data['end_date_permanent_diapers']));
$pdf->SetFont('aealarabiya', 'B', 11);
$pdf->Cell(0, 6, "سجل الحضور والانصراف ($startDate - $endDate)", 0, 1, 'C');
$pdf->Ln(3);

// Create attendance table
$pdf->SetFont('aealarabiya', '', 8);

// Table header
$pdf->SetFillColor(220, 220, 220);
$pdf->SetTextColor(0);
$pdf->SetDrawColor(128, 128, 128);
$pdf->SetLineWidth(0.15);

// Column widths (readjusted)
$w = array(20, 25, 33, 33, 33, 33, 25); // Further reduced notes column width to 25mm

// Main header
$pdf->Cell($w[0], 7, 'اليوم', 1, 0, 'C', true);
$pdf->Cell($w[1], 7, 'التاريخ', 1, 0, 'C', true);
$pdf->Cell(66, 7, 'الدوام العادي', 1, 0, 'C', true);
$pdf->Cell(66, 7, 'الدوام الإضافي', 1, 0, 'C', true);
$pdf->Cell($w[6], 7, 'ملاحظات', 1, 1, 'C', true);

// Subheader
$pdf->Cell($w[0], 7, '', 1, 0, 'C', true);
$pdf->Cell($w[1], 7, '', 1, 0, 'C', true);
$pdf->Cell($w[2], 7, 'وقت الدخول', 1, 0, 'C', true);
$pdf->Cell($w[3], 7, 'وقت الخروج', 1, 0, 'C', true);
$pdf->Cell($w[4], 7, 'وقت الدخول', 1, 0, 'C', true);
$pdf->Cell($w[5], 7, 'وقت الخروج', 1, 0, 'C', true);
$pdf->Cell($w[6], 7, '', 1, 1, 'C', true);

// Data rows
$pdf->SetFillColor(245, 245, 245);
$fill = false;

if (isset($data['data']) && isset($data['data']['AttendanceData'])) {
    foreach ($data['data']['AttendanceData'] as $dayData) {
        $rowHeight = 6;
        
        // Format empty times to show dash instead
        $formatTime = function($status, $time) {
            // Special case: if status contains "Leave" or "Absent" (or their Arabic equivalents), show it
            $leaveKeywords = ['Leave', 'Absent', 'إجازة', 'غائب'];
            
            foreach ($leaveKeywords as $keyword) {
                if (stripos($status, $keyword) !== false) {
                    return $status;
                }
            }
            
            // For empty times
            if (empty($time) || $time == '00:00' || $time == '--:--') {
                return '-';
            }
            
            // For regular attendance (Present/Departed), just show the time
            return $time;
        };
        
        // Main row with all data
        $pdf->Cell($w[0], $rowHeight, $dayData['BasicInfo']['DayName'], 1, 0, 'C', $fill);
        $pdf->Cell($w[1], $rowHeight, $dayData['BasicInfo']['DayNumber'] . '/' . $dayData['BasicInfo']['MonthNumber'], 1, 0, 'C', $fill);
        
        // Regular shift with formatted times
        $pdf->Cell($w[2], $rowHeight, 
            $formatTime(
                $dayData['RegularShift']['CheckIn']['Status'],
                $dayData['RegularShift']['CheckIn']['Time']
            ), 1, 0, 'C', $fill);
        $pdf->Cell($w[3], $rowHeight,
            $formatTime(
                $dayData['RegularShift']['CheckOut']['Status'],
                $dayData['RegularShift']['CheckOut']['Time']
            ), 1, 0, 'C', $fill);
        
        // Overtime shift with formatted times
        $pdf->Cell($w[4], $rowHeight,
            $formatTime(
                $dayData['OvertimeShift']['CheckIn']['Status'],
                $dayData['OvertimeShift']['CheckIn']['Time']
            ), 1, 0, 'C', $fill);
        $pdf->Cell($w[5], $rowHeight,
            $formatTime(
                $dayData['OvertimeShift']['CheckOut']['Status'],
                $dayData['OvertimeShift']['CheckOut']['Time']
            ), 1, 0, 'C', $fill);
        
        // Notes - only display if there are actual notes
        $notes = isset($dayData['AdditionalNotes']) ? trim($dayData['AdditionalNotes']) : '';
        $pdf->Cell($w[6], $rowHeight, $notes, 1, 1, 'R', $fill);
        
        $fill = !$fill;
    }
}

// Output the PDF
$pdf->Output('attendance_record.pdf', 'I'); 
