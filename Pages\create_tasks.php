<?php
// بداية جلسة المستخدم وإعدادات عرض الأخطاء
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// معالجة طلب AJAX للحصول على مهام الوظيفة
if (isset($_GET['action']) && $_GET['action'] === 'get_job_tasks') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid job title ID');
        }

        $jobTitleId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // الحصول على بيانات المسمى الوظيفي
        $stmt = $conn->prepare("SELECT data_todo_list_Job FROM Job_titles WHERE id_Job_titles = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        $stmt->bind_param("i", $jobTitleId);
        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $todoList = json_decode($row['data_todo_list_Job'], true);
            $tasks = array_map(function($task) {
                return $task['taskName'];
            }, $todoList['jobDetails']['tasks']);
            echo json_encode($tasks);
        } else {
            echo json_encode([]);
        }

        $stmt->close();
        $conn->close();
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

$error_message = '';
$success_message = '';

// جلب المشاريع والمسميات الوظيفية من قاعدة البيانات
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    // جلب المشاريع
    $projects = [];
    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }

    // جلب الموظفين
    $employees = [];
    $result = $conn->query("SELECT id_employees, name_ar_contract, name_en_contract FROM employees WHERE status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $employees[] = $row;
        }
    }

    // جلب المسميات الوظيفية مع عدد المهام
    $task_titles = [];
    $result = $conn->query("SELECT id_tasks_titles, name_tasks, name_tasks_en, data_todo_list_tasks FROM tasks_titles");

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $task_titles[] = [
                'id' => $row['id_tasks_titles'],
                'name' => $row['name_tasks'],
                'name_en' => $row['name_tasks_en'],
                'data' => $row['data_todo_list_tasks']
            ];
        }
    }
    
    // Get delegates from database
    $delegates = [];
    $result = $conn->query("SELECT id_assigned, name_ar_assigned, name_en_assigned FROM assigned WHERE status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $delegates[] = $row;
        }
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // التحقق من صحة البيانات وتجهيزها
        $project_id = intval($_POST['project_id'] ?? 0);
        $employee_id = intval($_POST['employee_id'] ?? 0);
        $delegate_id = intval($_POST['delegate_id'] ?? 0);
        $assignee_type = $_POST['assignee_type'] ?? '';
        
        // Remove contract type related variables and validation
        $contract_type = 1; // Set a default value
        $status_contract = 1; // Set a default value

        $version_date = trim($_POST['version_date'] ?? '');
        $start_date_contract = trim($_POST['start_date_contract'] ?? '');
        $end_date_contract = trim($_POST['end_date_contract'] ?? '');
        $wage_contract = trim($_POST['wage_contract'] ?? '');
        $job_title_id = trim($_POST['job_title_id'] ?? '');
        $data_todo_list_contract = trim($_POST['json_output'] ?? '');
        $amount_written_ar = trim($_POST['amount_written_ar'] ?? '');
        $amount_written_en = trim($_POST['amount_written_en'] ?? '');

        $validation_errors = [];

        // التحقق من المشروع
        if (empty($project_id)) {
            $validation_errors[] = 'يرجى اختيار مشروع';
        }

        // التحقق من الموظف أو المندوب
        if ($assignee_type === 'employee') {
            if (empty($employee_id)) {
                $validation_errors[] = 'يرجى اختيار موظف';
            }
        } elseif ($assignee_type === 'delegate') {
            if (empty($delegate_id)) {
                $validation_errors[] = 'يرجى اختيار مندوب';
            }
        } else {
            $validation_errors[] = 'يرجى اختيار نوع التكليف (موظف أو مندوب)';
        }

        // التحقق من تاريخ الإصدار
        if (empty($version_date)) {
            $validation_errors[] = 'يرجى إدخال تاريخ الإصدار';
        }

        // التحقق من تاريخ البداية - remove the comparison with version_date
        if (empty($start_date_contract)) {
            $validation_errors[] = 'يرجى إدخال تاريخ البداية';
        }

        // التحقق من تاريخ النهاية
        if (empty($end_date_contract)) {
            $validation_errors[] = 'يرجى إدخال تاريخ النهاية';
        } else {
            $start = new DateTime($start_date_contract);
            $end = new DateTime($end_date_contract);
            
            if ($end <= $start) {
                $validation_errors[] = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
            }
        }

        // التحقق من المستحق المالي
        if (empty($wage_contract)) {
            $validation_errors[] = 'يرجى إدخال المستحق المالي';
        }

        // التحقق من المسمى الوظيفي
        if (empty($job_title_id)) {
            $validation_errors[] = 'يرجى اختيار مسمى وظيفي';
        }

        // التحقق من صحة JSON
        if (empty($data_todo_list_contract)) {
            $validation_errors[] = 'يرجى إدخال بيانات JSON للمهام';
        } else {
            $decoded_json = json_decode($data_todo_list_contract, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $validation_errors[] = 'بيانات JSON غير صالحة: ' . json_last_error_msg();
            } elseif (!isset($decoded_json['taskDetails']) || !isset($decoded_json['taskDetails']['subtasks']) || empty($decoded_json['taskDetails']['subtasks'])) {
                $validation_errors[] = 'يجب إضافة مهمة واحدة على الأقل';
            }
        }

        // التحقق من المبلغ المكتوب
        if (empty($amount_written_ar)) {
            $validation_errors[] = 'المبلغ بالعربية مطلوب';
        }

        if (empty($amount_written_en)) {
            $validation_errors[] = 'المبلغ بالإنجليزية مطلوب';
        }

        // التحقق من نوع العملة
        if (empty($_POST['currency_type']) || !in_array($_POST['currency_type'], ['1', '2'])) {
            $validation_errors[] = 'يرجى اختيار نوع العملة';
        }

        // التحقق من مكان العمل
        if (empty($_POST['workplace'])) {
            $validation_errors[] = 'يرجى إدخال مكان العمل';
        }

        if (empty($validation_errors)) {
            // إضافة المهمة إلى قاعدة البيانات
            $sql = "INSERT INTO tasks (
                id_Project,
                id_employees,
                id_assigned,
                version_date,
                start_date_TASKS,
                end_date_TASKS,
                status_TASKS,
                wage_TASKS,
                name_TASKS,
                name_TASKS_en,
                data_todo_list_TASKS,
                amount_written_ar,
                amount_written_en,
                Type_currency,
                note,
                workplace
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
            }

            // Get both Arabic and English job names from the list
            $job_name = '';
            $job_name_en = '';
            foreach ($task_titles as $job) {
                if ($job['id'] == $job_title_id) {
                    $job_name = $job['name'];
                    $job_name_en = $job['name_en'];
                    break;
                }
            }

            if (empty($job_name) || empty($job_name_en)) {
                throw new Exception("لم يتم العثور على المسمى الوظيفي المحدد");
            }

            // Get the assignee ID based on the selected type
            $assigned_id = null;
            if ($assignee_type === 'delegate') {
                $employee_id = null;
                $assigned_id = $delegate_id;
            } else {
                $assigned_id = null;
            }

            $workplace = trim($_POST['workplace'] ?? '');
            $note = trim($_POST['note'] ?? '');

            $stmt->bind_param("iiisssidsssssiss",
                $project_id,
                $employee_id,
                $assigned_id,
                $version_date,
                $start_date_contract,
                $end_date_contract,
                $status_contract,
                $wage_contract,
                $job_name,
                $job_name_en,
                $data_todo_list_contract,
                $amount_written_ar,
                $amount_written_en,
                $_POST['currency_type'],
                $note,
                $workplace
            );

            if (!$stmt->execute()) {
                throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
            }

            $success_message = 'تم إنشاء المهمة بنجاح';
        } else {
            $error_message = "أخطاء التحقق:<br>" . implode("<br>", $validation_errors);
        }
    }
} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء مهمة جديدة - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Alert styling */
        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .section-title {
            color: #0d6efd;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Form controls */
        .form-control, .form-select {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            color: var(--text-color);
        }

        .form-control:disabled {
            background-color: var(--hover-color);
            color: var(--text-muted);
        }

        /* Helper text styling */
        .text-muted {
            color: #6c757d !important;
        }

        [data-theme="dark"] .text-muted {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        /* Primary color overrides */
        .text-primary {
            color: #0d6efd !important;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }

        /* Save button styling */
        .save-contract-btn {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 200px;
        }

        .save-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .save-contract-btn:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Select2 styling */
        .select2-container--default .select2-selection--single {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
            height: 38px;
            line-height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            color: var(--text-color);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-dropdown {
            background-color: var(--bg-input);
            border-color: var(--input-border);
        }

        .select2-container--default .select2-results__option {
            color: var(--text-color);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color);
        }

        /* Task list styling */
        .task-list-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: var(--bg-input);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .task-list-scrollable {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .task-list-scrollable::-webkit-scrollbar {
            width: 8px;
        }

        .task-list-scrollable::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        .task-item {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-item:last-child {
            margin-bottom: 0;
        }

        .task-input-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex: 1;
        }

        .task-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        /* Delete button styling */
        .btn-danger.btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }

        .btn-danger.btn-sm:hover {
            opacity: 0.9;
        }

        .text-end {
            text-align: end;
            margin-top: -0.5rem; /* Adjust spacing */
        }

        /* Card styling */
        .card {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .card-title {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--primary-color);
        }

        /* Button styling */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--btn-text);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-danger {
            color: var(--btn-text);
        }

        /* Duration helper text */
        .duration-helper {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        /* JSON section styling */
        .btn-secondary {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: var(--hover-color);
            border-color: var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary .bi-chevron-down {
            transition: transform 0.3s ease;
        }

        .btn-secondary[aria-expanded="true"] .bi-chevron-down {
            transform: rotate(180deg);
        }

        #jsonSection {
            transition: all 0.3s ease;
        }

        #jsonSection.collapsing {
            transition: all 0.3s ease;
        }

        #jsonSection.show {
            margin-top: 1rem;
        }

        #json_output {
            background-color: var(--bg-input);
            color: var(--text-color);
            font-family: monospace;
            font-size: 0.875rem;
            resize: vertical;
        }

        /* Identity attachment styling */
        .identity-attachment-container {
            transition: all 0.3s ease;
        }

        .identity-attachment-container:hover {
            background-color: var(--bg-input) !important;
        }

        .border-dashed {
            border-style: dashed !important;
        }

        .upload-box {
            transition: all 0.3s ease;
        }

        .upload-box:hover {
            border-color: var(--primary-color) !important;
            background-color: rgba(var(--primary-rgb), 0.05);
        }

        /* Dark theme support */
        [data-theme="dark"] .identity-attachment-container {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        [data-theme="dark"] .upload-box:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .selected-file {
            color: rgba(255, 255, 255, 0.7) !important;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <!-- Main Content -->
    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">إنشاء مهمة جديدة</h5>
                            
                            <!-- Alert Info -->
                            <div class="alert alert-info mb-4" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                مرحباً بك في صفحة إنشاء المهام. يمكنك من خلال هذه الصفحة إنشاء مهام جديدة وتحديد تفاصيلها وربطها بالمشاريع والموظفين أو المنتدبين.
                            </div>
                            
                            <form id="contractForm" method="post" onsubmit="return validateForm()" enctype="multipart/form-data">
                                <!-- Project and Assignment Information -->
                                <div class="section-container">
                                    <h6 class="section-title">معلومات المشروع والمكلف</h6>
                                    <div class="row mb-3">
                                        <div class="col-12 mb-3">
                                            <div class="btn-group w-100" role="group">
                                                <input type="radio" class="btn-check" name="assignee_type" id="employee_type" value="employee" checked>
                                                <label class="btn btn-outline-primary" for="employee_type">موظف</label>
                                                
                                                <input type="radio" class="btn-check" name="assignee_type" id="delegate_type" value="delegate">
                                                <label class="btn btn-outline-primary" for="delegate_type">منتدب</label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                            <select class="form-select select2-search" id="project_id" name="project_id" required onchange="handleProjectChange()">
                                                <option value="">اختر المشروع</option>
                                                <?php foreach ($projects as $project): ?>
                                                    <option value="<?php echo htmlspecialchars($project['id_Project']); ?>">
                                                        <?php echo htmlspecialchars($project['Project_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="employee_id" class="form-label fw-bold mb-2">اختر موظف</label>
                                            <select class="form-select select2-search" id="employee_id" name="employee_id" disabled>
                                                <option value="">اختر الموظف</option>
                                            </select>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="delegate_id" class="form-label fw-bold mb-2">اختر منتدب</label>
                                            <select class="form-select select2-search" id="delegate_id" name="delegate_id" disabled>
                                                <option value="">اختر المنتدب</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Amount Written Section -->
                                <div class="section-container">
                                    <h6 class="section-title">تفاصيل المستحقات المالية</h6>
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label for="currency_type" class="form-label">نوع العملة</label>
                                            <select class="form-select select2" id="currency_type" name="currency_type" required onchange="updateAmountWritten()">
                                                <option value="">اختر نوع العملة</option>
                                                <option value="1">دولار</option>
                                                <option value="2">ريال</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="wage_contract" class="form-label">المستحق المالي</label>
                                            <input type="number" class="form-control" id="wage_contract" name="wage_contract" 
                                                   value="<?php echo isset($_POST['wage_contract']) ? htmlspecialchars($_POST['wage_contract']) : ''; ?>" 
                                                   step="0.01" min="0" required onchange="updateAmountWritten()" oninput="updateAmountWritten()">
                                            <small class="text-muted">سيتم تحويل المبلغ تلقائياً إلى كلمات</small>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="amount_written_ar" class="form-label">المبلغ بالعربية</label>
                                            <input type="text" class="form-control" id="amount_written_ar" name="amount_written_ar" 
                                                   dir="rtl" required>
                                            <small class="text-muted">يمكنك تعديل النص يدوياً</small>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="amount_written_en" class="form-label">المبلغ بالإنجليزية</label>
                                            <input type="text" class="form-control" id="amount_written_en" name="amount_written_en" 
                                                   dir="ltr" required>
                                            <small class="text-muted">يمكنك تعديل النص يدوياً</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contract Dates Section -->
                                <div class="section-container">
                                    <h6 class="section-title">تواريخ المهمة</h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="version_date" class="form-label">تاريخ إصدار المهمة</label>
                                            <input type="date" class="form-control" id="version_date" name="version_date" 
                                                   value="<?php echo isset($_POST['version_date']) ? htmlspecialchars($_POST['version_date']) : date('Y-m-d'); ?>" 
                                                   required>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="start_date_contract" class="form-label">تاريخ بداية المهمة</label>
                                            <input type="date" class="form-control" id="start_date_contract" name="start_date_contract" required onchange="handleStartDateChange()">
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="end_date_contract" class="form-label">تاريخ نهاية المهمة</label>
                                            <input type="date" class="form-control" id="end_date_contract" name="end_date_contract" required onchange="handleEndDateChange()">
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Task Details Section -->
                                <div class="section-container">
                                    <h6 class="section-title">تفاصيل إضافية للمهمة</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="workplace" class="form-label">مكان العمل</label>
                                            <input type="text" class="form-control" id="workplace" name="workplace" 
                                                   value="<?php echo isset($_POST['workplace']) ? htmlspecialchars($_POST['workplace']) : ''; ?>" 
                                                   required>
                                            <small class="text-muted">أدخل مكان تنفيذ المهمة</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="note" class="form-label">ملاحظات</label>
                                            <textarea class="form-control" id="note" name="note" rows="3"><?php echo isset($_POST['note']) ? htmlspecialchars($_POST['note']) : ''; ?></textarea>
                                            <small class="text-muted">يمكنك إضافة أي ملاحظات إضافية هنا</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Job Title and Tasks Section -->
                                <div class="section-container">
                                    <h6 class="section-title">عنوان المهمة والمهام الفرعية</h6>
                                    <div class="mb-3">
                                        <label for="job_title_id" class="form-label">عنوان المهمة</label>
                                        <select class="form-control select2" id="job_title_id" name="job_title_id" onchange="handleJobTitleChange()">
                                            <option value="">اختر عنوان المهمة</option>
                                            <?php foreach ($task_titles as $task): ?>
                                                <option value="<?php echo htmlspecialchars($task['id']); ?>" 
                                                        data-json="<?php echo htmlspecialchars($task['data']); ?>"
                                                        data-name-en="<?php echo htmlspecialchars($task['name_en']); ?>">
                                                    <?php echo htmlspecialchars($task['name']) . ' - ' . htmlspecialchars($task['name_en']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div id="selected_job_title_display" class="mt-2" style="display: none;">
                                            <small class="text-muted">
                                                <span class="me-2">العربية:</span><span id="job_title_ar" class="text-primary"></span>
                                                <br>
                                                <span class="me-2">English:</span><span id="job_title_en" class="text-primary"></span>
                                            </small>
                                        </div>
                                    </div>

                                    <div class="task-list-container">
                                        <label class="form-label">المهام الفرعية</label>
                                        <div id="task_list" class="task-list-scrollable"></div>
                                        <button type="button" id="add_task_btn" class="btn btn-primary mt-3" onclick="addNewTask(event)">
                                            <i class="bi bi-plus-lg"></i> إضافة مهمة فرعية جديدة
                                        </button>
                                    </div>
                                </div>

                                <!-- Save Task Button -->
                                <div class="mt-4 mb-4 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5 py-2 save-contract-btn">
                                        <i class="bi bi-save me-2"></i> حفظ المهمة
                                    </button>
                                </div>

                                <!-- Collapsible JSON Section -->
                                <div class="mt-3">
                                    <button class="btn btn-secondary w-100 mb-2" type="button" 
                                            data-bs-toggle="collapse" data-bs-target="#jsonSection" 
                                            aria-expanded="false" aria-controls="jsonSection">
                                        <i class="bi bi-code-square me-2"></i>
                                        عرض كود JSON
                                        <i class="bi bi-chevron-down ms-2"></i>
                                    </button>
                                    <div class="collapse" id="jsonSection">
                                        <textarea id="json_output" name="json_output" class="form-control" rows="10" readonly></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        // Handle identity type selection
        function updateIdentityType() {
            const select = document.getElementById('identity_type');
            const arInput = document.getElementById('Identity_contract_ar');
            const enInput = document.getElementById('Identity_contract_en');
            const typeEnInput = document.getElementById('Identity_type_en');
            
            if (select.value === 'Passport') {
                arInput.value = 'جواز سفر';
                enInput.value = 'Passport';
                typeEnInput.value = 'Passport';
            } else if (select.value === 'National ID') {
                arInput.value = 'بطاقة شخصية';
                enInput.value = 'National ID';
                typeEnInput.value = 'National ID';
            } else {
                arInput.value = '';
                enInput.value = '';
                typeEnInput.value = '';
            }
        }

        // Handle place of issuance selection
        function handleIssuePlaceChange() {
            const select = document.getElementById('identity_issue_place');
            const customInput = document.getElementById('custom_issue_place');
            const arInput = document.getElementById('Identity_issue_contract_ar');
            const enInput = document.getElementById('Identity_issue_contract_en');
            const placeEnInput = document.getElementById('Identity_issue_place_en');

            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.required = true;
                arInput.value = customInput.value;
                enInput.value = customInput.value;
                placeEnInput.value = customInput.value;
                placeEnInput.readOnly = false;
            } else if (select.value) {
                customInput.style.display = 'none';
                customInput.required = false;
                const [en, ar] = select.value.split('|');
                arInput.value = ar;
                enInput.value = en;
                placeEnInput.value = en;
                placeEnInput.readOnly = true;
            } else {
                customInput.style.display = 'none';
                customInput.required = false;
                arInput.value = '';
                enInput.value = '';
                placeEnInput.value = '';
                placeEnInput.readOnly = true;
            }
        }

        // Handle custom place input
        function handleCustomPlaceInput() {
            const customInput = document.getElementById('custom_issue_place');
            const arInput = document.getElementById('Identity_issue_contract_ar');
            const enInput = document.getElementById('Identity_issue_contract_en');
            const placeEnInput = document.getElementById('Identity_issue_place_en');
            
            arInput.value = customInput.value;
            enInput.value = customInput.value;
            placeEnInput.value = customInput.value;
        }

        // Handle amount written update
        function updateAmountWritten() {
            const amount = document.getElementById('wage_contract').value;
            const currencyType = document.getElementById('currency_type').value;
            const arField = document.getElementById('amount_written_ar');
            const enField = document.getElementById('amount_written_en');

            if (!amount || !currencyType) {
                arField.value = '';
                enField.value = '';
                return;
            }

            const amountNum = parseFloat(amount);
            if (isNaN(amountNum)) {
                arField.value = '';
                enField.value = '';
                return;
            }

            // Convert to words
            const arAmount = numberToArabicWords(amountNum);
            const enAmount = numberToEnglishWords(amountNum);

            // Update fields with appropriate currency
            if (currencyType === '1') { // Dollar
                arField.value = arAmount + " دولارا";
                enField.value = enAmount + " dollars";
            } else { // Riyal
                arField.value = arAmount + " ريالا";
                enField.value = enAmount + " riyals";
            }
        }

        // Convert number to Arabic words
        function numberToArabicWords(number) {
            const digits = ['صفر', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
            const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
            const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
            const hundreds = ['', 'مائة', 'مئتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
            
            function convertHundreds(num) {
                if (num === 0) return '';
                
                let words = '';
                
                // Handle hundreds
                if (num >= 100) {
                    words += hundreds[Math.floor(num / 100)];
                    num %= 100;
                    if (num > 0) words += ' و';
                }
                
                // Handle tens and ones
                if (num > 0) {
                    if (num < 10) {
                        words += digits[num];
                    } else if (num < 20) {
                        words += teens[num - 10];
                    } else {
                        const onesDigit = num % 10;
                        const tensDigit = Math.floor(num / 10);
                        if (onesDigit > 0) {
                            words += digits[onesDigit] + ' و';
                        }
                        words += tens[tensDigit];
                    }
                }
                
                return words;
            }

            if (number === 0) return digits[0];
            
            let words = '';
            
            // Handle billions
            if (number >= 1000000000) {
                const billions = Math.floor(number / 1000000000);
                if (billions === 1) {
                    words += 'مليار';
                } else if (billions === 2) {
                    words += 'ملياران';
                } else if (billions >= 3 && billions <= 10) {
                    words += convertHundreds(billions) + ' مليارات';
                } else {
                    words += convertHundreds(billions) + ' مليار';
                }
                number %= 1000000000;
                if (number > 0) words += ' و';
            }
            
            // Handle millions
            if (number >= 1000000) {
                const millions = Math.floor(number / 1000000);
                if (millions === 1) {
                    words += 'مليون';
                } else if (millions === 2) {
                    words += 'مليونان';
                } else if (millions >= 3 && millions <= 10) {
                    words += convertHundreds(millions) + ' ملايين';
                } else {
                    words += convertHundreds(millions) + ' مليون';
                }
                number %= 1000000;
                if (number > 0) words += ' و';
            }
            
            // Handle thousands
            if (number >= 1000) {
                const thousands = Math.floor(number / 1000);
                if (thousands === 1) {
                    words += 'ألف';
                } else if (thousands === 2) {
                    words += 'ألفان';
                } else if (thousands >= 3 && thousands <= 10) {
                    words += convertHundreds(thousands) + ' آلاف';
                } else {
                    words += convertHundreds(thousands) + ' ألف';
                }
                number %= 1000;
                if (number > 0) words += ' و';
            }
            
            // Handle remaining hundreds
            if (number > 0) {
                words += convertHundreds(number);
            }
            
            return words;
        }

        // Convert number to English words
        function numberToEnglishWords(number) {
            const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
            const teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
            const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];

            function capitalizeFirst(str) {
                return str.charAt(0).toUpperCase() + str.slice(1);
            }

            function convertLessThanThousand(num) {
                if (num === 0) return '';
                
                let words = '';
                
                // Handle hundreds
                if (num >= 100) {
                    words += ones[Math.floor(num / 100)] + ' hundred';
                    num %= 100;
                    if (num > 0) words += ' and ';
                }
                
                // Handle tens and ones
                if (num > 0) {
                    if (num < 10) {
                        words += ones[num];
                    } else if (num < 20) {
                        words += teens[num - 10];
                    } else {
                        const tensDigit = Math.floor(num / 10);
                        const onesDigit = num % 10;
                        words += tens[tensDigit];
                        if (onesDigit > 0) {
                            words += '-' + ones[onesDigit];
                        }
                    }
                }
                
                return words;
            }

            if (number === 0) return 'zero';

            let words = '';
            
            // Handle billions
            if (number >= 1000000000) {
                const billions = Math.floor(number / 1000000000);
                words += convertLessThanThousand(billions) + ' billion';
                number %= 1000000000;
                if (number > 0) words += ' ';
            }
            
            // Handle millions
            if (number >= 1000000) {
                const millions = Math.floor(number / 1000000);
                words += convertLessThanThousand(millions) + ' million';
                number %= 1000000;
                if (number > 0) words += ' ';
            }
            
            // Handle thousands
            if (number >= 1000) {
                const thousands = Math.floor(number / 1000);
                words += convertLessThanThousand(thousands) + ' thousand';
                number %= 1000;
                if (number > 0) words += ' ';
            }

            // Handle rest of the number
            if (number > 0) {
                words += convertLessThanThousand(number);
            }

            return capitalizeFirst(words.trim());
        }

        $(document).ready(function() {
            // Initialize Select2 for project selection
            $('#project_id').select2({
                placeholder: 'اختر المشروع',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for contract type
            $('#contract_type').select2({
                placeholder: 'اختر نوع العقد',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for job title
            $('#job_title_id').select2({
                placeholder: 'اختر المسمى الوظيفي',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for identity type
            $('#identity_type').select2({
                placeholder: 'اختر نوع الهوية',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for place of issuance
            $('#identity_issue_place').select2({
                placeholder: 'اختر مكان الإصدار',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for employee selection
            $('#employee_id').select2({
                placeholder: 'اختر الموظف',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for delegate selection
            $('#delegate_id').select2({
                placeholder: 'اختر المنتدب',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Add event listeners
            $('#identity_issue_place').on('change', handleIssuePlaceChange);
            $('#custom_issue_place').on('input', handleCustomPlaceInput);

            handleIssuanceDateTypeChange();

            // File upload handling
            $('#contractcol').on('change', function(e) {
                const fileName = e.target.files[0]?.name;
                if (fileName) {
                    $('#selected-file').html(
                        '<i class="bi bi-file-earmark me-1"></i>' +
                        '<span>تم اختيار: ' + fileName + '</span>'
                    );
                } else {
                    $('#selected-file').empty();
                }
            });

            // Drag and drop handling
            const uploadBox = document.querySelector('.upload-box');
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadBox.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadBox.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadBox.addEventListener(eventName, unhighlight, false);
            });

            function highlight(e) {
                uploadBox.classList.add('border-primary');
            }

            function unhighlight(e) {
                uploadBox.classList.remove('border-primary');
            }

            uploadBox.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                const fileInput = document.getElementById('contractcol');
                
                if (files.length > 0) {
                    fileInput.files = files;
                    const event = new Event('change');
                    fileInput.dispatchEvent(event);
                }
            }

            // Initialize task title field
            const taskTitleField = document.getElementById('job_title_id');
            taskTitleField.disabled = true; // Initially disabled until project is selected

            // Initialize the project change handler
            handleProjectChange();
        });

        // عند تغيير الاسم الوظيفي
        function handleJobTitleChange() {
            const jobTitleField = document.getElementById('job_title_id');
            const addTaskButton = document.getElementById('add_task_btn');
            const jobTitleDisplay = document.getElementById('selected_job_title_display');
            const jobTitleAr = document.getElementById('job_title_ar');
            const jobTitleEn = document.getElementById('job_title_en');

            if (jobTitleField.value) {
                const selectedOption = jobTitleField.options[jobTitleField.selectedIndex];
                const nameAr = selectedOption.text.split(' - ')[0];
                const nameEn = selectedOption.getAttribute('data-name-en');

                // Display the job titles
                jobTitleAr.textContent = nameAr;
                jobTitleEn.textContent = nameEn;
                jobTitleDisplay.style.display = 'block';

                addTaskButton.disabled = false;
                updateTaskList();
            } else {
                jobTitleDisplay.style.display = 'none';
                addTaskButton.disabled = true;
                tasks = [];
                originalData = {};
                renderTaskList();
                updateJsonOutput();
            }
        }

        // تحديث قسم evaluation بناءً على نوع العقد
        function updateEvaluation() {
            const contractType = document.getElementById('contract_type').value;

            if (contractType === "1" || contractType === "2") {
                evaluation = { percentageEvaluation: "yes", workDaysEvaluation: "no" };
            } else if (contractType === "3" || contractType === "4") {
                evaluation = { percentageEvaluation: "no", workDaysEvaluation: "yes" };
            } else {
                evaluation = { percentageEvaluation: "no", workDaysEvaluation: "no" };
            }

            updateJsonOutput(); // Update JSON output
        }

        // عرض المهام في القائمة
        function renderTaskList() {
            const taskList = document.getElementById('task_list');
            taskList.innerHTML = '';

            tasks.forEach((task, index) => {
                const taskDiv = document.createElement('div');
                taskDiv.className = 'task-item';

                const taskLabel = document.createElement('div');
                taskLabel.className = 'task-number mb-2';
                taskLabel.textContent = `المهمة الفرعية ${index + 1}`;
                taskLabel.style.fontWeight = 'bold';
                taskLabel.style.color = 'var(--primary-color)';

                const inputContainer = document.createElement('div');
                inputContainer.className = 'task-input-container';

                // Arabic task name input
                const arInput = document.createElement('input');
                arInput.type = 'text';
                arInput.className = 'form-control mb-2';
                arInput.value = task.taskName || '';
                arInput.placeholder = 'اسم المهمة الفرعية بالعربية';
                arInput.dir = 'rtl';
                arInput.oninput = () => updateTask(index, 'taskName', arInput.value);

                // English task name input
                const enInput = document.createElement('input');
                enInput.type = 'text';
                enInput.className = 'form-control mb-2';
                enInput.value = task.taskNameEn || '';
                enInput.placeholder = 'Subtask name in English';
                enInput.dir = 'ltr';
                enInput.oninput = () => updateTask(index, 'taskNameEn', enInput.value);

                // Delete button container
                const deleteContainer = document.createElement('div');
                deleteContainer.className = 'text-end';

                // Delete button
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.className = 'btn btn-danger btn-sm';
                deleteButton.innerHTML = '<i class="bi bi-trash"></i>';
                deleteButton.onclick = () => deleteTask(index);
                deleteButton.title = 'حذف المهمة الفرعية';

                deleteContainer.appendChild(deleteButton);
                inputContainer.appendChild(taskLabel);
                inputContainer.appendChild(arInput);
                inputContainer.appendChild(enInput);
                inputContainer.appendChild(deleteContainer);
                taskDiv.appendChild(inputContainer);
                taskList.appendChild(taskDiv);
            });
        }

        // تحديث المهمة
        function updateTask(index, field, newValue) {
            tasks[index][field] = newValue;
            tasks[index].completionRate = 0; // Always set completionRate to 0
            updateJsonOutput();
        }

        // حذف المهمة
        function deleteTask(index) {
            tasks.splice(index, 1);
            renderTaskList();
            updateJsonOutput();
        }

        // إضافة مهمة جديدة
        function addNewTask(event) {
            if (event) {
                event.preventDefault();
            }
            tasks.push({
                taskName: '',
                taskNameEn: '',
                completionRate: 0
            });
            renderTaskList();
            updateJsonOutput();
        }

        // تحديث قائمة المهام بناءً على الاسم الوظيفي
        function updateTaskList() {
            const select = document.getElementById('job_title_id');
            const selectedOption = select.options[select.selectedIndex];

            if (select.value) {
                const json = selectedOption.getAttribute('data-json') || '{}';
                let data;
                try {
                    data = JSON.parse(json);
                    // Check if data has the expected structure
                    if (!data.taskDetails || !Array.isArray(data.taskDetails.subtasks)) {
                        data = {
                            taskDetails: {
                                subtasks: []
                            }
                        };
                    }
                } catch (e) {
                    console.error('Error parsing JSON:', e);
                    data = {
                        taskDetails: {
                            subtasks: []
                        }
                    };
                }

                originalData = data;
                tasks = (data.taskDetails?.subtasks || []).map(task => ({
                    taskName: task.subtaskName || '',
                    taskNameEn: task.subtaskNameEn || '',
                    completionRate: 0 // Always set to 0 when loading
                }));
                renderTaskList();
                updateJsonOutput();
            } else {
                tasks = [];
                originalData = {};
                renderTaskList();
                updateJsonOutput();
            }
        }

        // تحديث كود JSON
        function updateJsonOutput() {
            const jsonOutput = document.getElementById('json_output');
            const updatedData = {
                ...originalData,
                taskDetails: {
                    ...originalData.taskDetails,
                    subtasks: tasks.map(task => ({
                        subtaskName: task.taskName,
                        subtaskNameEn: task.taskNameEn,
                        completionRate: task.completionRate
                    }))
                }
            };
            jsonOutput.value = JSON.stringify(updatedData, null, 2);
        }

        // التحقق من صحة النموذج قبل الإرسال
        function validateForm() {
            if (tasks.length === 0) {
                alert('يجب إضافة مهمة واحدة على الأقل');
                return false;
            }

            for (let i = 0; i < tasks.length; i++) {
                if (!tasks[i].taskName || tasks[i].taskName.trim() === '') {
                    alert('لا يمكن حفظ المهمة. يوجد حقل مهمة عربي فارغ');
                    return false;
                }
                if (!tasks[i].taskNameEn || tasks[i].taskNameEn.trim() === '') {
                    alert('لا يمكن حفظ المهمة. يوجد حقل مهمة إنجليزي فارغ');
                    return false;
                }
            }

            const jsonOutput = document.getElementById('json_output').value;
            if (!jsonOutput) {
                alert('يجب إدخال بيانات JSON للمهام');
                return false;
            }

            return true;
        }

        // Handle assignee type change
        document.querySelectorAll('input[name="assignee_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const employeeSelect = document.getElementById('employee_id');
                const delegateSelect = document.getElementById('delegate_id');
                
                // Clear both selects and their Select2 instances
                $('#employee_id').val(null).trigger('change');
                $('#delegate_id').val(null).trigger('change');
                
                // Reset the HTML content
                employeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
                delegateSelect.innerHTML = '<option value="">اختر المنتدب</option>';
                
                // Disable/enable appropriate select
                if (this.value === 'employee') {
                    employeeSelect.disabled = false;
                    delegateSelect.disabled = true;
                } else {
                    employeeSelect.disabled = true;
                    delegateSelect.disabled = false;
                }
                
                // If a project is selected, fetch the appropriate list
                const projectId = document.getElementById('project_id').value;
                if (projectId) {
                    handleProjectChange();
                }
            });
        });

        // Handle project change
        function handleProjectChange() {
            const projectId = document.getElementById('project_id').value;
            const employeeSelect = document.getElementById('employee_id');
            const delegateSelect = document.getElementById('delegate_id');
            const taskTitleField = document.getElementById('job_title_id');
            const assigneeType = document.querySelector('input[name="assignee_type"]:checked').value;
            
            // Reset and disable selects if no project selected
            if (!projectId) {
                employeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
                delegateSelect.innerHTML = '<option value="">اختر المنتدب</option>';
                $('#employee_id').val(null).trigger('change');
                $('#delegate_id').val(null).trigger('change');
                employeeSelect.disabled = true;
                delegateSelect.disabled = true;
                taskTitleField.disabled = true;
                return;
            }
            
            // Enable task title field when project is selected
            taskTitleField.disabled = false;
            
            // Clear both selects before fetching new data
            $('#employee_id').val(null).trigger('change');
            $('#delegate_id').val(null).trigger('change');
            
            // Enable/disable and populate the appropriate select based on assignee type
            if (assigneeType === 'employee') {
                employeeSelect.disabled = false;
                delegateSelect.disabled = true;
                
                // Fetch employees
                fetch(`get_project_members.php?project_id=${projectId}&type=employees`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        employeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
                        if (data.length === 0) {
                            employeeSelect.innerHTML += `
                                <option value="" disabled>لا يوجد موظفين متاحين لهذا المشروع</option>`;
                        } else {
                            data.forEach(employee => {
                                employeeSelect.innerHTML += `
                                    <option value="${employee.id_employees}">
                                        ${employee.name_ar_contract} - ${employee.name_en_contract}
                                    </option>`;
                            });
                        }
                        // Refresh Select2
                        $('#employee_id').trigger('change');
                    })
                    .catch(error => {
                        console.error('Error fetching employees:', error);
                        alert('حدث خطأ أثناء جلب قائمة الموظفين');
                    });
            } else {
                employeeSelect.disabled = true;
                delegateSelect.disabled = false;
                
                // Fetch delegates
                fetch(`get_project_members.php?project_id=${projectId}&type=delegates`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        delegateSelect.innerHTML = '<option value="">اختر المنتدب</option>';
                        if (data.length === 0) {
                            delegateSelect.innerHTML += `
                                <option value="" disabled>لا يوجد مندوبين متاحين لهذا المشروع</option>`;
                        } else {
                            data.forEach(delegate => {
                                delegateSelect.innerHTML += `
                                    <option value="${delegate.id_assigned}">
                                        ${delegate.name_ar_assigned} - ${delegate.name_en_assigned}
                                    </option>`;
                            });
                        }
                        // Refresh Select2
                        $('#delegate_id').trigger('change');
                    })
                    .catch(error => {
                        console.error('Error fetching delegates:', error);
                        alert('حدث خطأ أثناء جلب قائمة المنتدبين');
                    });
            }
        }
    </script>
</body>
</html>




