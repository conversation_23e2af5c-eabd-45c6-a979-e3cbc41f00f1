<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    if (!isset($_GET['contract_id'])) {
        throw new Exception('Contract ID is required');
    }
    
    if (!isset($_GET['permanent_diapers_id'])) {
        throw new Exception('Permanent diapers ID is required');
    }

    $contractId = intval($_GET['contract_id']);
    $permanentDiapersId = intval($_GET['permanent_diapers_id']);

    // Read database connection details
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) throw new Exception('Error reading configuration file');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // First check if there are any achievement reports for this contract and attendance record
    $achievementQuery = "SELECT data_todo_list_achievement 
                        FROM achievement_reports 
                        WHERE id_contract = ? 
                        AND id_permanent_diapers = ?
                        ORDER BY add_achievement_reports DESC 
                        LIMIT 1";
    
    $stmt = $conn->prepare($achievementQuery);
    $stmt->bind_param("ii", $contractId, $permanentDiapersId);
    $stmt->execute();
    $achievementResult = $stmt->get_result();
    
    if ($achievementRow = $achievementResult->fetch_assoc()) {
        // If achievement reports exist, return the todo list from the latest report
        echo json_encode([
            'success' => true,
            'data' => [
                'data_todo_list_contract' => $achievementRow['data_todo_list_achievement']
            ]
        ]);
    } else {
        // If no achievement reports exist, get the todo list from the contract table
        $contractQuery = "SELECT data_todo_list_contract 
                         FROM contract 
                         WHERE id_contract = ?";
        
        $stmt = $conn->prepare($contractQuery);
        $stmt->bind_param("i", $contractId);
        $stmt->execute();
        $contractResult = $stmt->get_result();
        
        if ($contractRow = $contractResult->fetch_assoc()) {
            echo json_encode([
                'success' => true,
                'data' => $contractRow
            ]);
        } else {
            throw new Exception('Contract not found');
        }
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) $conn->close();
} 
