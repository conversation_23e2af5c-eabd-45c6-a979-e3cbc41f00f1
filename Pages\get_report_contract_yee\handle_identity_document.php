<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Read database connection details
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Handle file upload
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload') {
        if (!isset($_POST['employee_id']) || !isset($_FILES['identity_document'])) {
            throw new Exception('Missing required parameters');
        }

        $employeeId = $_POST['employee_id'];
        
        // Validate file
        if ($_FILES['identity_document']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error');
        }

        // Check file type
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        $fileType = $_FILES['identity_document']['type'];
        
        if (!in_array($fileType, $allowedTypes)) {
            throw new Exception('Invalid file type. Only PDF and images are allowed.');
        }

        // Check file size (5MB max)
        if ($_FILES['identity_document']['size'] > 5 * 1024 * 1024) {
            throw new Exception('File size too large. Maximum size is 5MB.');
        }

        // Read file content
        $fileContent = file_get_contents($_FILES['identity_document']['tmp_name']);
        
        // Update database
        $stmt = $conn->prepare("UPDATE employees SET Identity_document = ? WHERE id_employees = ?");
        if (!$stmt) {
            throw new Exception("Error preparing statement: " . $conn->error);
        }
        
        $null = null;
        $stmt->bind_param("bi", $fileContent, $employeeId);
        
        if (!$stmt->execute()) {
            throw new Exception("Error executing statement: " . $stmt->error);
        }
        
        $stmt->close();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم رفع المستند بنجاح'
        ]);
    }
    // Handle file download
    elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'download') {
        if (!isset($_GET['employee_id'])) {
            throw new Exception('Employee ID is required');
        }

        $employeeId = $_GET['employee_id'];
        
        // Fetch the document
        $stmt = $conn->prepare("SELECT Identity_document FROM employees WHERE id_employees = ?");
        if (!$stmt) {
            throw new Exception("Error preparing statement: " . $conn->error);
        }
        
        $stmt->bind_param("i", $employeeId);
        $stmt->execute();
        $stmt->store_result();
        
        if ($stmt->num_rows === 0) {
            throw new Exception('Document not found');
        }
        
        $stmt->bind_result($fileContent);
        $stmt->fetch();
        $stmt->close();
        
        if (!$fileContent) {
            throw new Exception('No document found');
        }

        // Detect file type
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($fileContent);
        
        // Set appropriate extension based on mime type
        switch ($mimeType) {
            case 'application/pdf':
                $extension = '.pdf';
                break;
            case 'image/jpeg':
                $extension = '.jpg';
                break;
            case 'image/png':
                $extension = '.png';
                break;
            default:
                $extension = '';
        }

        // Clear any output buffers
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Remove the JSON content type header for downloads
        header_remove('Content-Type');
        
        // Set headers for download
        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="identity_document_' . $employeeId . $extension . '"');
        header('Content-Length: ' . strlen($fileContent));
        header('Cache-Control: private, must-revalidate, max-age=0');
        header('Pragma: public');
        
        echo $fileContent;
        exit;
    }
    else {
        throw new Exception('Invalid request');
    }

} catch (Exception $e) {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // For GET requests (downloads), show a user-friendly error page
        echo "Error: " . $e->getMessage();
    } else {
        // For POST requests (uploads), return JSON error
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?> 