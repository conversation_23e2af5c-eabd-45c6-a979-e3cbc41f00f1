<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Handle file upload
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isset($_FILES['document']) || $_FILES['document']['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['success' => false, 'message' => 'لم يتم اختيار ملف أو حدث خطأ أثناء الرفع']);
            exit;
        }

        $file = $_FILES['document'];
        $employeeId = $_POST['employee_id'];
        $fieldName = $_POST['field_name'];

        // Validate file type
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        $fileType = $file['type'];
        
        if (!in_array($fileType, $allowedTypes)) {
            echo json_encode(['success' => false, 'message' => 'نوع الملف غير مسموح به. يرجى اختيار ملف PDF أو صورة']);
            exit;
        }

        // Get file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        // Check file size (5MB max)
        if ($file['size'] > 5 * 1024 * 1024) {
            echo json_encode(['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى هو 5 ميجابايت']);
            exit;
        }

        try {
            // Read file content
            $fileContent = file_get_contents($file['tmp_name']);
            if ($fileContent === false) {
                throw new Exception("فشل في قراءة محتوى الملف");
            }

            // Update both content and type
            $sql = "UPDATE employees SET 
                    `$fieldName` = ?,
                    `{$fieldName}_type` = ? 
                    WHERE id_employees = ?";
            
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Error preparing statement: " . $conn->error);
            }
            
            $stmt->bind_param("bsi", $fileContent, $extension, $employeeId);
            if (!$stmt->execute()) {
                throw new Exception("Error executing statement: " . $stmt->error);
            }
            
            $stmt->close();
            echo json_encode(['success' => true, 'message' => 'تم رفع الملف بنجاح']);
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء معالجة الملف: ' . $e->getMessage()]);
        }
    }

    // Handle file download
    if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['download'])) {
        $employeeId = $_GET['employee_id'];
        $fieldName = $_GET['field'];
        
        try {
            // Get both content and type
            $stmt = $conn->prepare("SELECT `$fieldName`, `{$fieldName}_type` FROM employees WHERE id_employees = ?");
            if (!$stmt) {
                throw new Exception("Error preparing statement: " . $conn->error);
            }
            
            $stmt->bind_param("i", $employeeId);
            if (!$stmt->execute()) {
                throw new Exception("Error executing statement: " . $stmt->error);
            }
            
            $stmt->store_result();
            
            // Bind both results
            $fileContent = null;
            $fileType = null;
            $stmt->bind_result($fileContent, $fileType);
            
            if ($stmt->fetch() && $fileContent !== null && $fileType !== null) {
                // Set MIME type based on file extension
                $mimeTypes = [
                    'pdf' => 'application/pdf',
                    'jpg' => 'image/jpeg',
                    'jpeg' => 'image/jpeg',
                    'png' => 'image/png'
                ];
                
                $mimeType = $mimeTypes[$fileType] ?? 'application/octet-stream';
                
                // Clean the fieldName for the filename
                $cleanFieldName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $fieldName);
                $filename = $cleanFieldName . '_' . $employeeId . '.' . $fileType;

                // Set appropriate headers
                header('Content-Type: ' . $mimeType);
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Content-Length: ' . strlen($fileContent));
                header('Cache-Control: private, must-revalidate, max-age=0');
                header('Pragma: public');
                
                echo $fileContent;
                exit;
            } else {
                throw new Exception("لم يتم العثور على الملف");
            }
        } catch (Exception $e) {
            echo "خطأ في تحميل الملف: " . $e->getMessage();
        } finally {
            if (isset($stmt)) {
                $stmt->close();
            }
        }
    }

    // Handle file preview
    if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['preview'])) {
        $employeeId = $_GET['employee_id'];
        $fieldName = $_GET['field'];
        
        try {
            // Get both content and type
            $stmt = $conn->prepare("SELECT `$fieldName`, `{$fieldName}_type` FROM employees WHERE id_employees = ?");
            if (!$stmt) {
                throw new Exception("Error preparing statement: " . $conn->error);
            }
            
            $stmt->bind_param("i", $employeeId);
            if (!$stmt->execute()) {
                throw new Exception("Error executing statement: " . $stmt->error);
            }
            
            $stmt->store_result();
            
            // Bind both results
            $fileContent = null;
            $fileType = null;
            $stmt->bind_result($fileContent, $fileType);
            
            if ($stmt->fetch() && $fileContent !== null && $fileType !== null) {
                // Set MIME type based on file extension
                $mimeTypes = [
                    'pdf' => 'application/pdf',
                    'jpg' => 'image/jpeg',
                    'jpeg' => 'image/jpeg',
                    'png' => 'image/png'
                ];
                
                $mimeType = $mimeTypes[$fileType] ?? 'application/octet-stream';
                
                header('Content-Type: ' . $mimeType);
                echo $fileContent;
                exit;
            } else {
                throw new Exception("لم يتم العثور على الملف");
            }
        } catch (Exception $e) {
            echo "خطأ في عرض الملف: " . $e->getMessage();
        } finally {
            if (isset($stmt)) {
                $stmt->close();
            }
        }
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?> 