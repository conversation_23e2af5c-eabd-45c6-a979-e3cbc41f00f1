<?php
session_start();
header('Content-Type: text/html; charset=UTF-8');
// Security configurations
define('TEMPLATE_DIR', __DIR__ . '/templates/');
ini_set('display_errors', 0);
error_reporting(0);

$error_message = null;
$success_message = null;
$templateData = null;

// Function to fetch templates by type
function fetchTemplatesByType($db, $type) {
    try {
        $stmt = $db->prepare("SELECT id_template_contract, name_template_contract 
                             FROM template_contract 
                             WHERE type_template = :type 
                             ORDER BY created_at DESC");
        $stmt->execute([':type' => $type]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        throw new Exception('Error fetching templates: ' . $e->getMessage());
    }
}

// Connect to the database
try {
    // Connect to the database
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// Fetch templates if type is selected
$templates = [];
$selectedType = $_GET['template_type'] ?? null;
if ($selectedType && isset($db)) {
    try {
        $templates = fetchTemplatesByType($db, $selectedType);
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Handle AJAX request for templates
if (isset($_GET['ajax']) && $_GET['ajax'] == 'get_templates' && isset($_GET['type'])) {
    try {
        $ajaxTemplates = fetchTemplatesByType($db, $_GET['type']);
        echo json_encode(['success' => true, 'templates' => $ajaxTemplates]);
        exit;
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}

// Handle file upload and processing
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Check if a file was uploaded
        if (isset($_FILES['template_file']) && $_FILES['template_file']['error'] === UPLOAD_ERR_OK) {
            // Validate file type
            $fileInfo = pathinfo($_FILES['template_file']['name']);
            if (strtolower($fileInfo['extension']) !== 'json') {
                throw new Exception('يجب أن يكون الملف بتنسيق JSON');
            }

            // Read the uploaded file
            $jsonContent = file_get_contents($_FILES['template_file']['tmp_name']);
            if ($jsonContent === false) {
                throw new Exception('فشل في قراءة محتوى الملف');
            }

            // Decode JSON content
            $templateData = json_decode($jsonContent, true);
            if ($templateData === null) {
                throw new Exception('الملف ليس بتنسيق JSON صالح');
            }

            // Validate required fields
            if (!isset($templateData['template']) || !isset($templateData['styles']) || !isset($templateData['name']) || !isset($templateData['type'])) {
                throw new Exception('الملف لا يحتوي على البيانات المطلوبة');
            }

            // Store the template data in session for rendering
            $_SESSION['restored_template'] = $templateData;
            $success_message = "تم استعادة القالب بنجاح. يمكنك الآن تحريره وحفظه.";
            
            // Redirect to index.php with a parameter to indicate restoration
            header('Location: index.php?restore=1');
            exit;
        } 
        // Check if a template was selected from the database
        elseif (isset($_POST['template_id']) && !empty($_POST['template_id'])) {
            $templateId = $_POST['template_id'];
            
            // Fetch the template from the database
            $stmt = $db->prepare("SELECT id_template_contract, name_template_contract, type_template, date_template_contract 
                                 FROM template_contract 
                                 WHERE id_template_contract = :id");
            $stmt->execute([':id' => $templateId]);
            $template = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$template) {
                throw new Exception('لم يتم العثور على القالب المطلوب');
            }
            
            // Decode the JSON data
            $jsonData = json_decode($template['date_template_contract'], true);
            if (!$jsonData) {
                throw new Exception('تنسيق بيانات القالب غير صالح');
            }
            
            // Create template data structure
            $templateData = [
                'template' => $jsonData['content'],
                'styles' => $jsonData['meta']['styles'],
                'name' => $template['name_template_contract'],
                'type' => $template['type_template'],
                'id' => $template['id_template_contract']
            ];
            
            // Store the template data in session for rendering
            $_SESSION['restored_template'] = $templateData;
            $success_message = "تم استعادة القالب بنجاح. يمكنك الآن تحريره وحفظه.";
            
            // Redirect to index.php with update parameter
            header('Location: index.php?restore=1&update=' . $template['id_template_contract']);
            exit;
        }
        else {
            throw new Exception('لم يتم اختيار ملف أو قالب');
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة قالب</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/materialdesignicons.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 25px;
        }
        label {
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
        }
        .btn-primary {
            background-color: #4a6cf7;
            border-color: #4a6cf7;
            padding: 10px 20px;
        }
        .btn-primary:hover {
            background-color: #3a5bd9;
            border-color: #3a5bd9;
        }
        .alert {
            margin-bottom: 20px;
        }
        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        .file-upload-input {
            position: relative;
            z-index: 1;
            width: 100%;
            height: 46px;
            margin: 0;
            padding: 0 20px;
            opacity: 0;
            cursor: pointer;
        }
        .file-upload-button {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 46px;
            z-index: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border: 1px solid #ced4da;
            border-radius: 4px;
            color: #495057;
            font-size: 16px;
            cursor: pointer;
        }
        .file-name {
            margin-top: 8px;
            font-size: 14px;
            color: #666;
        }
        .back-link {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #4a6cf7;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .template-input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #4a6cf7;
            border-radius: 8px;
            background: #f8faff;
        }
        .template-input:disabled {
            background: #e9ecef;
            border-color: #ced4da;
            cursor: not-allowed;
        }
        select.template-input {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: left 1rem center;
            padding-left: 2.5rem;
        }
        .or-separator {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 30px 0;
        }
        .or-separator::before,
        .or-separator::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #ddd;
        }
        .or-separator::before {
            margin-right: 10px;
        }
        .or-separator::after {
            margin-left: 10px;
        }
        .tab-container {
            margin-bottom: 30px;
        }
        .nav-tabs {
            display: flex;
            list-style: none;
            padding: 0;
            margin: 0 0 20px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs li {
            margin-bottom: -1px;
        }
        .nav-tabs li a {
            display: block;
            padding: 10px 15px;
            text-decoration: none;
            color: #495057;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 5px;
        }
        .nav-tabs li a.active {
            color: #4a6cf7;
            background-color: #fff;
            border-bottom-color: #fff;
            font-weight: bold;
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>استعادة قالب</h1>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="tab-container">
            <ul class="nav-tabs">
                <li><a href="#fileTab" class="active tab-link" data-tab="fileTab">من ملف</a></li>
                <li><a href="#databaseTab" class="tab-link" data-tab="databaseTab">من قاعدة البيانات</a></li>
            </ul>
            
            <div class="tab-content">
                <!-- File Upload Tab -->
                <div class="tab-pane active" id="fileTab">
                    <form method="post" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="template_file">اختر ملف القالب (JSON)</label>
                            <div class="file-upload">
                                <input type="file" name="template_file" id="template_file" class="file-upload-input" accept=".json" required>
                                <div class="file-upload-button">
                                    <i class="mdi mdi-upload"></i> اختر ملف
                                </div>
                            </div>
                            <div class="file-name" id="file-name-display">لم يتم اختيار ملف</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="mdi mdi-restore"></i> استعادة القالب
                        </button>
                    </form>
                </div>
                
                <!-- Database Selection Tab -->
                <div class="tab-pane" id="databaseTab">
                    <form method="post" id="templateForm">
                        <!-- Template Type Selection -->
                        <div class="form-group">
                            <label for="templateType">نوع النموذج</label>
                            <select name="template_type" class="template-input" id="templateType">
                                <option value="">اختر نوع النموذج</option>
                                <option value="1" <?= $selectedType === '1' ? 'selected' : '' ?>>نموذج عقد أساسي</option>
                                <option value="2" <?= $selectedType === '2' ? 'selected' : '' ?>>نموذج عقد ممدد</option>
                            </select>
                        </div>

                        <!-- Template Selection -->
                        <div class="form-group">
                            <label for="templateId">القالب</label>
                            <select name="template_id" class="template-input" id="templateId" required <?= empty($templates) && !isset($_GET['ajax']) ? 'disabled' : '' ?>>
                                <option value="">اختر النموذج</option>
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?= htmlspecialchars($template['id_template_contract']) ?>"
                                            <?= ($_GET['template_id'] ?? '') == $template['id_template_contract'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($template['name_template_contract']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="mdi mdi-database-import"></i> استعادة القالب من قاعدة البيانات
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <a href="index.php" class="back-link">
            <i class="mdi mdi-arrow-left"></i> العودة إلى صفحة القوالب
        </a>
    </div>
    
    <script>
        // Display the name of the selected file
        document.getElementById('template_file').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'لم يتم اختيار ملف';
            document.getElementById('file-name-display').textContent = fileName;
        });
        
        // Tab navigation
        document.querySelectorAll('.tab-link').forEach(function(tab) {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all tabs and panes
                document.querySelectorAll('.tab-link').forEach(function(t) {
                    t.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane').forEach(function(p) {
                    p.classList.remove('active');
                });
                
                // Add active class to clicked tab and corresponding pane
                const tabId = this.getAttribute('data-tab');
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // Template type change handler with AJAX
        document.getElementById('templateType').addEventListener('change', function() {
            const templateType = this.value;
            const templateIdSelect = document.getElementById('templateId');
            
            if (templateType === '') {
                // Clear and disable the template dropdown if no type is selected
                templateIdSelect.innerHTML = '<option value="">اختر النموذج</option>';
                templateIdSelect.disabled = true;
                return;
            }
            
            // Show loading indication
            templateIdSelect.innerHTML = '<option value="">جاري التحميل...</option>';
            templateIdSelect.disabled = true;
            
            // Fetch templates via AJAX
            fetch(`?ajax=get_templates&type=${templateType}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Enable the dropdown and populate with templates
                        templateIdSelect.disabled = false;
                        templateIdSelect.innerHTML = '<option value="">اختر النموذج</option>';
                        
                        // Add template options
                        data.templates.forEach(template => {
                            const option = document.createElement('option');
                            option.value = template.id_template_contract;
                            option.textContent = template.name_template_contract;
                            templateIdSelect.appendChild(option);
                        });
                    } else {
                        // Show error
                        alert('خطأ في تحميل القوالب: ' + data.error);
                        templateIdSelect.innerHTML = '<option value="">اختر النموذج</option>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    templateIdSelect.innerHTML = '<option value="">حدث خطأ، يرجى المحاولة مرة أخرى</option>';
                });
        });
    </script>
</body>
</html> 