<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 0); // Disable error display for production

// Set content type to JSON
header('Content-Type: application/json');

// Get input data from POST request
$inputJSON = file_get_contents('php://input');
$data = json_decode($inputJSON, true);

// Initialize response array
$response = ['success' => false, 'message' => '', 'data' => null];

try {
    // Validate that we received data
    if (!$data) {
        throw new Exception("No data received");
    }
    
    // Required fields validation
    $requiredFields = [
        'id_achievement_reports' => 'رقم تقرير الإنجاز',
        'id_Project' => 'رقم المشروع',
        'id_TASKS' => 'رقم المهمة',
        'actual_working_days' => 'أيام العمل الفعلية',
        'today_wage' => 'الأجر اليومي',
        'total' => 'إجمالي المبلغ'
    ];
    
    foreach ($requiredFields as $field => $label) {
        if (!isset($data[$field]) || (empty($data[$field]) && $data[$field] !== 0)) {
            throw new Exception("الحقل المطلوب غير موجود: $label");
        }
    }
    
    // Connect to database
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception("خطأ في قراءة ملف الاتصال");
    }
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);
    
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    // Check if a merit report already exists for this achievement report
    $check_sql = "SELECT id_merit_reports FROM merit_reports WHERE id_achievement_reports = ?";
    $check_stmt = $conn->prepare($check_sql);
    
    if (!$check_stmt) {
        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
    }
    
    $check_stmt->bind_param("i", $data['id_achievement_reports']);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        throw new Exception("يوجد بالفعل تقرير استحقاق لتقرير الإنجاز هذا");
    }
    $check_stmt->close();
    
    // Prepare the insert query
    $insert_sql = "INSERT INTO merit_reports (
        id_achievement_reports, 
        id_Project, 
        id_TASKS, 
        id_employees,
        id_assigned,
        actual_working_days, 
        today_wage, 
        total, 
        total_after_discount, 
        tax_rate, 
        predecessor, 
        Insurance
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $insert_stmt = $conn->prepare($insert_sql);
    
    if (!$insert_stmt) {
        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
    }
    
    // Set default values for null fields
    $id_employees = isset($data['id_employees']) ? $data['id_employees'] : null;
    $id_assigned = isset($data['id_assigned']) ? $data['id_assigned'] : null;
    $tax_rate = isset($data['tax_rate']) ? $data['tax_rate'] : 0;
    $predecessor = isset($data['advances']) ? $data['advances'] : 0;
    $insurance = isset($data['insurance']) ? $data['insurance'] : 0;
    $total_after_discount = isset($data['total_after_discount']) ? $data['total_after_discount'] : $data['total'];
    
    // Bind parameters
    $insert_stmt->bind_param(
        "iiiiiiddddd", 
        $data['id_achievement_reports'],
        $data['id_Project'],
        $data['id_TASKS'],
        $id_employees,
        $id_assigned,
        $data['actual_working_days'],
        $data['today_wage'],
        $data['total'],
        $total_after_discount,
        $tax_rate,
        $predecessor,
        $insurance
    );
    
    // Execute the query
    if (!$insert_stmt->execute()) {
        throw new Exception("خطأ في تنفيذ الاستعلام: " . $insert_stmt->error);
    }
    
    // Get the ID of the inserted merit report
    $merit_report_id = $insert_stmt->insert_id;
    $insert_stmt->close();
    
    // Set success response
    $response['success'] = true;
    $response['message'] = "تم حفظ تقرير الاستحقاق بنجاح";
    $response['data'] = ['id_merit_reports' => $merit_report_id];
    
    // Set success message in session for display after redirect
    $_SESSION['success_message'] = "تم حفظ تقرير الاستحقاق بنجاح";
    
} catch (Exception $e) {
    // Set error response
    $response['success'] = false;
    $response['message'] = $e->getMessage();
    
    // Add more details for debugging in development
    $response['details'] = [
        'error_line' => $e->getLine(),
        'error_file' => $e->getFile()
    ];
    
    if (isset($conn)) {
        $response['details']['sql_error'] = $conn->error;
        $response['details']['sql_errno'] = $conn->errno;
    }
} finally {
    // Close database connection if open
    if (isset($conn)) {
        $conn->close();
    }
}

// Return JSON response
echo json_encode($response); 