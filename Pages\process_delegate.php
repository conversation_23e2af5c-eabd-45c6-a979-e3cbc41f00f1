<?php
// Start session and error reporting
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

// Check if it's an AJAX request with the correct action
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['action']) || $_POST['action'] !== 'add_from_excel') {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

// Check if delegate data is provided
if (!isset($_POST['delegate']) || !is_array($_POST['delegate'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات المندوب غير صحيحة']);
    exit;
}

try {
    // Get delegate data
    $delegate = $_POST['delegate'];
    
    // Basic validation
    if (empty($delegate['الاسم العربي']) || empty($delegate['الاسم الإنجليزي']) || 
        empty($delegate['نوع الهوية ar']) || empty($delegate['نوع الهوية en']) || 
        empty($delegate['رقم الهوية'])) {
        echo json_encode(['success' => false, 'message' => 'البيانات الأساسية غير مكتملة']);
        exit;
    }
    
    // Parse data from excel format
    $name_ar = $delegate['الاسم العربي'];
    $name_en = $delegate['الاسم الإنجليزي'];
    $identity_type_ar = $delegate['نوع الهوية ar'];
    $identity_type_en = $delegate['نوع الهوية en'];
    $identity_number = $delegate['رقم الهوية'];
    
    // Get the project ID from the form
    $project_id = isset($delegate['id_Project']) ? intval($delegate['id_Project']) : 0;
    
    // Validate project ID
    if ($project_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'يجب اختيار مشروع صالح']);
        exit;
    }
    
    // Read database connection details
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    // Create database connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    // Check for duplicates - name
    $check_name_sql = "SELECT * FROM assigned WHERE (name_ar_assigned = ? OR name_en_assigned = ?) AND status = 1";
    $check_name_stmt = $conn->prepare($check_name_sql);
    $check_name_stmt->bind_param("ss", $name_ar, $name_en);
    $check_name_stmt->execute();
    $name_result = $check_name_stmt->get_result();
    
    if ($name_result->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'يوجد منتدب مسجل بنفس الاسم بالفعل']);
        $check_name_stmt->close();
        $conn->close();
        exit;
    }
    $check_name_stmt->close();
    
    // Check for duplicates - ID number
    $check_id_sql = "SELECT * FROM assigned WHERE Identity_number_assigned = ? AND Identity_assigned_ar = ? AND status = 1";
    $check_id_stmt = $conn->prepare($check_id_sql);
    $check_id_stmt->bind_param("ss", $identity_number, $identity_type_ar);
    $check_id_stmt->execute();
    $id_result = $check_id_stmt->get_result();
    
    if ($id_result->num_rows > 0) {
        $message = ($identity_type_ar === 'بطاقة شخصية') ? 'يوجد منتدب مسجل بنفس رقم البطاقة الشخصية' : 
                  (($identity_type_ar === 'جواز سفر') ? 'يوجد منتدب مسجل بنفس رقم جواز السفر' : 'يوجد منتدب مسجل بنفس رقم الهوية');
        
        echo json_encode(['success' => false, 'message' => $message]);
        $check_id_stmt->close();
        $conn->close();
        exit;
    }
    $check_id_stmt->close();
    
    // Verify that the project exists
    $check_project_sql = "SELECT id_Project FROM Project WHERE id_Project = ? AND Project_status = 1";
    $check_project_stmt = $conn->prepare($check_project_sql);
    $check_project_stmt->bind_param("i", $project_id);
    $check_project_stmt->execute();
    $project_result = $check_project_stmt->get_result();
    
    if ($project_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'المشروع المختار غير موجود أو غير نشط']);
        $check_project_stmt->close();
        $conn->close();
        exit;
    }
    $check_project_stmt->close();
    
    // All checks passed, insert the new delegate
    $sql = "INSERT INTO assigned (
        id_Project,
        name_ar_assigned,
        name_en_assigned,
        Identity_assigned_ar,
        Identity_assigned_en,
        Identity_number_assigned,
        status
    ) VALUES (?, ?, ?, ?, ?, ?, 1)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
    }
    
    $stmt->bind_param("isssss",
        $project_id,
        $name_ar,
        $name_en,
        $identity_type_ar,
        $identity_type_en,
        $identity_number
    );
    
    if (!$stmt->execute()) {
        throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
    }
    
    echo json_encode(['success' => true, 'message' => 'تم إضافة المندوب بنجاح']);
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 