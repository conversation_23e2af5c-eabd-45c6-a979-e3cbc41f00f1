<?php
session_start();
header('Content-Type: text/html; charset=UTF-8');
define('TEMPLATE_DIR', __DIR__ . '/templates/');
$variables = [
    '@name_ar_contract', '@name_en_contract', '@contract_type',
    '@version_date', '@start_date_contract', '@end_date_contract',
    '@wage_contract', '@name_Job', '@Identity_contract_ar',
    '@Identity_contract_en', '@Identity_number_contract',
    '@amount_written_ar', '@amount_written_en',
    '@full_name_ar', '@full_name_en', '@role', '@role_en',
    '@Project_name', '@Project_name_en', '@Project_end_date'
];

$error_message = null;
$success_message = null;
$templateData = null;

// Add institution data fetching
$institutionData = null;
try {
    // Connect to the database
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch institution data
    $stmt = $db->query("SELECT * FROM documents LIMIT 1");
    $institutionData = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// After fetching institution data, add this processing code
if ($institutionData) {
    // Process Arabic name into lines
    $agency_name_ar = explode("\n", trim($institutionData['name_ar']));
    $address_ar = explode("\n", trim($institutionData['address_ar']));
    $numbers_ar = explode("\n", trim($institutionData['numbers']));

    // Process English name into lines
    $agency_name_en = explode("\n", trim($institutionData['name_en']));
    $address_en = explode("\n", trim($institutionData['address_en']));
    $numbers_en = explode("\n", trim($institutionData['numbers']));

    // Prepare logo data
    $logoData = $institutionData['logo'];
    $logoPath = 'data:image/jpeg;base64,' . base64_encode($logoData);
}

// Update the fetchContractData function to handle both contract types
function fetchContractData($contractId, $db, $isExtended = false) {
    try {
        // If it's an extended contract, first get the base contract ID and extension dates
        if ($isExtended) {
            $extensionQuery = "
                SELECT 
                    ec.id_contract,
                    ec.version_date as version_date_extension,
                    ec.start_date_contract as start_date_contract_extension,
                    ec.end_date_contract as end_date_contract_extension
                FROM extension_contract ec
                WHERE ec.id_extension_contract = :contractId
            ";
            
            $stmt = $db->prepare($extensionQuery);
            $stmt->execute([':contractId' => $contractId]);
            $extensionData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$extensionData) {
                throw new Exception('Extended contract not found');
            }
            
            // Use the base contract ID for the main query
            $contractId = $extensionData['id_contract'];
        }

        // Main query to fetch contract data
        $query = "
            SELECT 
                c.*,
                e.name_ar_contract,
                e.name_en_contract,
                e.Identity_contract_ar,
                e.Identity_contract_en,
                e.Identity_number_contract,
                e.Identity_issue_date_contract,
                e.Identity_issue_contract_ar,
                e.Identity_issue_contract_en,
                p.Project_name,
                p.Project_name_en,
                p.Project_end_date,
                a.full_name_ar,
                a.full_name_en,
                a.role,
                a.role_en
            FROM contract c
            LEFT JOIN employees e ON c.id_employees = e.id_employees
            LEFT JOIN project p ON c.id_Project = p.id_Project
            LEFT JOIN administrators a ON p.id_administrators = a.id_administrators
            WHERE c.id_contract = :contractId
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([':contractId' => $contractId]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$data) {
            throw new Exception('Contract not found');
        }

        // If this is an extended contract, merge the extension dates
        if ($isExtended && isset($extensionData)) {
            $data = array_merge($data, [
                'version_date_extension' => $extensionData['version_date_extension'],
                'start_date_contract_extension' => $extensionData['start_date_contract_extension'],
                'end_date_contract_extension' => $extensionData['end_date_contract_extension']
            ]);
        }
        
        return $data;
    } catch (Exception $e) {
        throw new Exception('Error fetching contract data: ' . $e->getMessage());
    }
}

// Function to extract tasks from JSON data
function extractTasks($jsonData, $language = 'ar') {
    try {
        $data = json_decode($jsonData, true);
        if (!$data) {
            return '';
        }

        $tasks = [];
        
        // Extract tasks from jobDetails if it exists
        if (isset($data['jobDetails']['tasks']) && is_array($data['jobDetails']['tasks'])) {
            foreach ($data['jobDetails']['tasks'] as $task) {
                if (isset($task['taskName']) && isset($task['taskNameEn'])) {
                    $tasks[] = $language === 'ar' ? $task['taskName'] : $task['taskNameEn'];
                }
            }
        }
        
        // Return tasks as plain list without numbering
        if (!empty($tasks)) {
            // Handle direction differently for Arabic and English
            if ($language === 'ar') {
                // For Arabic, we can use the regular approach
                $taskList = '';
                foreach ($tasks as $task) {
                    $taskList .= $task;
                    if ($task !== end($tasks)) {
                        $taskList .= '<br>';
                    }
                }
            } else {
                // For English, we need to ensure direction is explicitly set to LTR
                $taskList = '';
                foreach ($tasks as $task) {
                    // Explicitly set direction to LTR for English content
                    $taskList .= '<span style="direction: ltr; display: inline-block; width: 100%; text-align: left;">' . 
                                $task . '</span>';
                    if ($task !== end($tasks)) {
                        $taskList .= '<br>';
                    }
                }
            }
            return $taskList;
        }
        
        return '';
        
    } catch (Exception $e) {
        return '';
    }
}

// Update the replaceTemplateVariables function to handle extension dates
function replaceTemplateVariables($content, $contractData) {
    // Format regular dates
    $start_date = $contractData['start_date_contract'] ? date('Y-m-d', strtotime($contractData['start_date_contract'])) : '';
    $end_date = $contractData['end_date_contract'] ? date('Y-m-d', strtotime($contractData['end_date_contract'])) : '';
    $version_date = $contractData['version_date'] ? date('Y-m-d', strtotime($contractData['version_date'])) : '';
    $issue_date = $contractData['Identity_issue_date_contract'] ? date('Y-m-d', strtotime($contractData['Identity_issue_date_contract'])) : '';
    $project_end_date = $contractData['Project_end_date'] ? date('Y-m-d', strtotime($contractData['Project_end_date'])) : '';

    // Format extension dates if they exist
    $version_date_extension = isset($contractData['version_date_extension']) ? 
        date('Y-m-d', strtotime($contractData['version_date_extension'])) : '';
    $start_date_extension = isset($contractData['start_date_contract_extension']) ? 
        date('Y-m-d', strtotime($contractData['start_date_contract_extension'])) : '';
    $end_date_extension = isset($contractData['end_date_contract_extension']) ? 
        date('Y-m-d', strtotime($contractData['end_date_contract_extension'])) : '';

    // Process task lists if JSON data exists
    $tasks_ar = '';
    $tasks_en = '';
    if (isset($contractData['data_todo_list_contract'])) {
        $tasks_ar = extractTasks($contractData['data_todo_list_contract'], 'ar');
        $tasks_en = extractTasks($contractData['data_todo_list_contract'], 'en');
    }

    // Create replacements array with exact variable matches
    $replacements = [
        '(id_Project)' => $contractData['id_Project'] ?? '(id_Project)',
        '(start_date_contract)' => $start_date ?: '(start_date_contract)',
        '(end_date_contract)' => $end_date ?: '(end_date_contract)',
        '(version_date)' => $version_date ?: '(version_date)',
        '(wage_contract)' => $contractData['wage_contract'] ?? '(wage_contract)',
        '(amount_written_ar)' => $contractData['amount_written_ar'] ?? '(amount_written_ar)',
        '(amount_written_en)' => $contractData['amount_written_en'] ?? '(amount_written_en)',
        '(data_todo_list_contract)' => $contractData['data_todo_list_contract'] ?? '(data_todo_list_contract)',
        '(data_todo_list_contract_ar)' => $tasks_ar ?: '(data_todo_list_contract_ar)',
        '(data_todo_list_contract_en)' => $tasks_en ?: '(data_todo_list_contract_en)',
        '(name_ar_contract)' => $contractData['name_ar_contract'] ?? '(name_ar_contract)',
        '(name_en_contract)' => $contractData['name_en_contract'] ?? '(name_en_contract)',
        '(Identity_contract_ar)' => $contractData['Identity_contract_ar'] ?? '(Identity_contract_ar)',
        '(Identity_contract_en)' => $contractData['Identity_contract_en'] ?? '(Identity_contract_en)',
        '(Identity_number_contract)' => $contractData['Identity_number_contract'] ?? '(Identity_number_contract)',
        '(Identity_issue_date_contract)' => $issue_date ?: '(Identity_issue_date_contract)',
        '(Identity_issue_contract_ar)' => $contractData['Identity_issue_contract_ar'] ?? '(Identity_issue_contract_ar)',
        '(Identity_issue_contract_en)' => $contractData['Identity_issue_contract_en'] ?? '(Identity_issue_contract_en)',
        '(name_Job)' => $contractData['name_Job'] ?? '(name_Job)',
        '(name_Job_en)' => $contractData['name_Job_en'] ?? '(name_Job_en)',
        '(full_name_ar)' => $contractData['full_name_ar'] ?? '(full_name_ar)',
        '(full_name_en)' => $contractData['full_name_en'] ?? '(full_name_en)',
        '(role)' => $contractData['role'] ?? '(role)',
        '(role_en)' => $contractData['role_en'] ?? '(role_en)',
        '(Project_name)' => $contractData['Project_name'] ?? '(Project_name)',
        '(Project_name_en)' => $contractData['Project_name_en'] ?? '(Project_name_en)',
        '(Project_end_date)' => $project_end_date ?: '(Project_end_date)',
        '(version_date_extension)' => $version_date_extension ?: '(version_date_extension)',
        '(start_date_contract_extension)' => $start_date_extension ?: '(start_date_contract_extension)',
        '(end_date_contract_extension)' => $end_date_extension ?: '(end_date_contract_extension)',
    ];

    // Replace each variable in the content
    $processedContent = $content;
    foreach ($replacements as $search => $replace) {
        // Special handling for task list variables to preserve formatting
        if ($search === '(data_todo_list_contract_ar)' || $search === '(data_todo_list_contract_en)') {
            // Skip these - they will be handled by processHtmlVariables
            continue;
        }
        
        // Use simple string replacement for regular variables
        $processedContent = str_replace($search, $replace, $processedContent);
    }

    return $processedContent;
}

// Add new function to fetch templates by type
function fetchTemplatesByType($db, $type) {
    try {
        $stmt = $db->prepare("SELECT id_template_contract, name_template_contract 
                             FROM template_contract 
                             WHERE type_template = :type 
                             ORDER BY created_at DESC");
        $stmt->execute([':type' => $type]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        throw new Exception('Error fetching templates: ' . $e->getMessage());
    }
}

// Fetch templates if type is selected
$templates = [];
$selectedType = $_GET['template_type'] ?? null;
if ($selectedType && isset($db)) {
    try {
        $templates = fetchTemplatesByType($db, $selectedType);
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get template ID and contract ID from request parameters
$templateId = $_GET['template_id'] ?? null;
$contractId = $_GET['contract_id'] ?? null;

$error_message = null;
$success_message = null;
$templateData = null;
$contractData = null;

// Add this function near the top of the file with other functions
function checkContractExists($db, $contractId, $isExtended = false) {
    try {
        if ($isExtended) {
            // Check if extended contract exists
            $stmt = $db->prepare("SELECT id_extension_contract FROM extension_contract WHERE id_extension_contract = :id");
            $stmt->execute([':id' => $contractId]);
            
            if (!$stmt->fetch()) {
                throw new Exception('رقم العقد الممدد غير موجود');
            }
        } else {
            // Check if base contract exists
            $stmt = $db->prepare("SELECT id_contract FROM contract WHERE id_contract = :id");
            $stmt->execute([':id' => $contractId]);
            
            if (!$stmt->fetch()) {
                throw new Exception('رقم العقد غير موجود');
            }
        }
        return true;
    } catch (Exception $e) {
        throw new Exception($e->getMessage());
    }
}

// Update the contract data fetching logic
if ($templateId && $contractId) {
    try {
        // Connect to database (using existing connection code)
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('خطأ في قراءة ملف الإعدادات');
        }
        $servername = trim(fgets($file));
        $username   = trim(fgets($file));
        $password   = trim(fgets($file));
        $dbname     = trim(fgets($file));
        fclose($file);
        
        $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Check if contract exists before proceeding
        $isExtendedContract = ($selectedType == '2');
        checkContractExists($db, $contractId, $isExtendedContract);

        // Fetch template data with type
        $stmt = $db->prepare("SELECT date_template_contract, type_template FROM template_contract WHERE id_template_contract = :id");
        $stmt->execute([':id' => $templateId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result) {
            throw new Exception('لم يتم العثور على القالب المطلوب');
        }

        $templateData = json_decode($result['date_template_contract'], true);
        if (!isset($templateData['content'])) {
            throw new Exception('تنسيق القالب غير صالح - المحتوى مفقود');
        }

        // Store template type for use in the view
        $templateType = $result['type_template'];

        // Fetch contract data based on template type
        $contractData = fetchContractData($contractId, $db, $isExtendedContract);

        // Replace variables in template content
        $templateData['content'] = replaceTemplateVariables($templateData['content'], $contractData);

        $success_message = 'تم تحميل القالب والبيانات بنجاح';
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Handle special HTML content variables separately to preserve formatting
function processHtmlVariables($content, $contractData) {
    // Process task lists if JSON data exists
    if (isset($contractData['data_todo_list_contract'])) {
        $tasks_ar = extractTasks($contractData['data_todo_list_contract'], 'ar');
        $tasks_en = extractTasks($contractData['data_todo_list_contract'], 'en');
        
        // Define more precise patterns to find task list variables in styled HTML
        $pattern_ar_styled = '/<([a-z]+)([^>]*?)style="([^"]*)"([^>]*?)>(.*?)\(data_todo_list_contract_ar\)(.*?)<\/\1>/is';
        $pattern_en_styled = '/<([a-z]+)([^>]*?)style="([^"]*)"([^>]*?)>(.*?)\(data_todo_list_contract_en\)(.*?)<\/\1>/is';
        
        // Define patterns for task list variables in regular HTML without style
        $pattern_ar_regular = '/<([a-z]+)([^>]*?)>(.*?)\(data_todo_list_contract_ar\)(.*?)<\/\1>/is';
        $pattern_en_regular = '/<([a-z]+)([^>]*?)>(.*?)\(data_todo_list_contract_en\)(.*?)<\/\1>/is';
        
        // Replace task list variables in styled HTML elements
        $content = preg_replace_callback($pattern_ar_styled, function($matches) use ($tasks_ar) {
            return '<' . $matches[1] . $matches[2] . 'style="' . $matches[3] . '"' . $matches[4] . '>' . 
                   $matches[5] . $tasks_ar . $matches[6] . '</' . $matches[1] . '>';
        }, $content);
        
        $content = preg_replace_callback($pattern_en_styled, function($matches) use ($tasks_en) {
            // For English content, ensure style preserves direction
            $style = $matches[3];
            if (strpos($style, 'direction:') === false) {
                $style .= '; direction: ltr; text-align: left';
            }
            
            return '<' . $matches[1] . $matches[2] . 'style="' . $style . '"' . $matches[4] . '>' . 
                   $matches[5] . $tasks_en . $matches[6] . '</' . $matches[1] . '>';
        }, $content);
        
        // Replace task list variables in regular HTML elements
        $content = preg_replace_callback($pattern_ar_regular, function($matches) use ($tasks_ar) {
            return '<' . $matches[1] . $matches[2] . '>' . 
                   $matches[3] . $tasks_ar . $matches[4] . '</' . $matches[1] . '>';
        }, $content);
        
        $content = preg_replace_callback($pattern_en_regular, function($matches) use ($tasks_en) {
            // For English content, add direction attribute
            $attrs = $matches[2];
            if (strpos($attrs, 'dir="ltr"') === false && strpos($attrs, 'direction') === false) {
                $attrs .= ' dir="ltr" style="direction: ltr; text-align: left;"';
            }
            
            return '<' . $matches[1] . $attrs . '>' . 
                   $matches[3] . $tasks_en . $matches[4] . '</' . $matches[1] . '>';
        }, $content);
        
        // Direct replacement for any remaining variables not in HTML tags
        $content = str_replace('(data_todo_list_contract_ar)', $tasks_ar, $content);
        $content = str_replace('(data_todo_list_contract_en)', $tasks_en, $content);
    }
    
    return $content;
}

// Function to extract footer text between markers
function extractFooterText($content) {
    // Look for text between << and >> markers
    $pattern = '/<<(.*?)>>/s';
    $footerText = '';
    $mainContent = $content;
    
    if (preg_match_all($pattern, $content, $matches)) {
        // Extract all footer texts
        $footerText = implode("\n", $matches[1]);
        
        // Remove the footer markers from the main content
        $mainContent = preg_replace($pattern, '', $content);
    }
    
    return [
        'mainContent' => $mainContent,
        'footerText' => $footerText
    ];
}

// Function to process variables in content (for non-HTML variables)
function processVariables($content, $variables) {
    foreach ($variables as $var) {
        // Skip task list variables - they will be handled separately
        if ($var === '@data_todo_list_contract_ar' || $var === '@data_todo_list_contract_en' ||
            $var === '(data_todo_list_contract_ar)' || $var === '(data_todo_list_contract_en)') {
            continue;
        }
        
        // Only add placeholders for variables that aren't already replaced
        // Check if the variable exists in the content before trying to replace it
        if (strpos($content, $var) !== false) {
            $placeholder = '<span class="variable-placeholder">' . $var . '</span>';
            
            // Be careful to only replace complete variable names, not partial matches
            // Add a space check to avoid replacing variables within other variables
            $content = str_replace($var . ' ', $placeholder . ' ', $content);
            
            // Also check for the variable at the end of a string or before punctuation
            $content = preg_replace('/(' . preg_quote($var, '/') . ')([.,;:!?\s]|$)/', $placeholder . '$2', $content);
        }
    }
    return $content;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة القوالب - Professional Contract Suite</title>
    <!-- Add Bootstrap and Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/lib/mdi/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;700&display=swap" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <!-- Add html2canvas and jsPDF libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- Add this script to ensure jsPDF is properly defined -->
    <script>
        // Make jsPDF accessible as a global variable
        window.jspdf = window.jspdf || {};
    </script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --bg-main: #ffffff;
            --bg-sidebar: #f8f9fa;
            --bg-card: #ffffff;
            --text-primary: #2c3e50;
            --text-muted: #6c757d;
            --border-color: #e9ecef;
            --hover-bg: #f8f9fa;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* Add dark theme variables */
        [data-theme="dark"] {
            --bg-main: #1a1d21;
            --bg-sidebar: #1a1d21;
            --bg-card: #242931;
            --text-primary: #e9ecef;
            --text-muted: #adb5bd;
            --border-color: #2c3237;
            --hover-bg: #242931;
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        body {
            background: var(--bg-main);
            margin: 0;
            padding: 30px;
            font-family: 'Cairo', sans-serif;
            color: var(--text-primary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--bg-main);
        }

        .upload-card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }

        .upload-form {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .file-input {
            flex: 1;
            padding: 0.8rem;
            border: 2px dashed var(--secondary-color);
            border-radius: 8px;
            background: #f8faff;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .a4-container {
            width: 210mm;
            min-height: 297mm;
            height: fit-content;
            margin: 20px auto;
            background: white !important;
            color: #000 !important;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            box-sizing: border-box;
            overflow: visible;
            padding: 0; /* Removed bottom padding for footer */
            page-break-after: always;
            background: white !important;
            overflow: visible !important; /* Ensure content is not cut off */
        }

        .footer-section, .footer-lines-container, .footer-text, .footer-text-right, .footer-text-left {
            /* These styles are intentionally empty to override previous definitions */
            display: none !important;
        }

        .header-section {
            width: 100%;
            position: relative;
            margin: 0;
            padding: 0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 130px; /* Increased to accommodate larger logo */
        }

        .logo-container {
            position: relative;
            height: 130px; /* Increased to match header-section */
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 100%; /* Use full width */
            overflow: visible;
        }

        .logo-container img {
            height: 120px;
            width: auto;
            max-width: 230px; /* Limit logo width */
            max-height: 120px;
            object-fit: contain;
            margin: 0 auto; /* Center horizontally */
            padding: 0;
        }

        .header-bottom {
            width: 100%;
            margin-top: 0;
        }

        .contract-content {
            width: 100%;
            min-height: calc(297mm - 60px);
            height: fit-content;
            font-family: 'Times New Roman', 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
            overflow: visible;
            box-sizing: border-box;
            background: white !important;
            color: #000 !important;
            padding: 0;
            margin: 0;
            line-height: 1.1 !important;
        }

        /* Add specific alignment classes */
        .contract-content [style*="text-align: right"] {
            text-align: right !important;
        }

        .contract-content [style*="text-align: left"] {
            text-align: left !important;
        }

        .contract-content [style*="text-align: center"] {
            text-align: center !important;
        }

        .contract-content [style*="text-align: justify"] {
            text-align: justify !important;
        }

        /* Handle Quill specific alignment classes */
        .contract-content .ql-align-right {
            text-align: right !important;
        }

        .contract-content .ql-align-left {
            text-align: left !important;
        }

        .contract-content .ql-align-center {
            text-align: center !important;
        }

        .contract-content .ql-align-justify {
            text-align: justify !important;
        }

        /* Handle Quill specific font sizes */
        .contract-content .ql-size-small { font-size: 10px !important; }
        .contract-content .ql-size-normal { font-size: 14px !important; }
        .contract-content .ql-size-large { font-size: 18px !important; }
        .contract-content .ql-size-huge { font-size: 20px !important; }

        /* Handle inline style font sizes with exact matching */
        .contract-content [style*="font-size: 10px"] { font-size: 10px !important; }
        .contract-content [style*="font-size: 12px"] { font-size: 12px !important; }
        .contract-content [style*="font-size: 14px"] { font-size: 14px !important; }
        .contract-content [style*="font-size: 16px"] { font-size: 16px !important; }
        .contract-content [style*="font-size: 18px"] { font-size: 18px !important; }
        .contract-content [style*="font-size: 20px"] { font-size: 20px !important; }

        /* Ensure proper line height inheritance */
        .contract-content p {
            text-align: inherit;
            margin: 0;
            padding: 0;
            line-height: inherit;
        }

        /* Ensure proper text scaling */
        .contract-content * {
            box-sizing: border-box;
            -webkit-text-size-adjust: 100%;
            -moz-text-size-adjust: 100%;
            text-size-adjust: 100%;
        }

        /* Split page styles */
        .split-content {
            display: flex;
            gap: 20px;
            min-height: 100%;
            width: 100%;
            position: relative;
            overflow: visible;
            box-sizing: border-box;
            border: 1px solid #ccc; /* Add border around entire split content */
            padding: 0 10px; /* Add some padding inside the border */
        }

        .split-content::after {
            content: '';
            position: absolute;
            left: 60%;
            top: 0;
            bottom: 0;
            width: 1px;
            background-color: #ccc;
            transform: translateX(-50%);
            pointer-events: none;
            z-index: 1;
        }

        /* Ensure complete style isolation between sections */
        .split-content .right-content,
        .split-content .left-content {
            min-height: 100%;
            overflow: visible;
            position: relative;
            box-sizing: border-box;
            padding: 10px 5px; /* Add some horizontal padding as well */
        }

        /* Right content specific styles */
        .split-content .right-content {
            flex: 4; /* Changed from 6 to 4 */
            width: 40%; /* Changed from 60% to 40% */
            direction: rtl;
            text-align: right !important;
            padding-right: 10px; /* Extra padding on the right side */
        }

        /* Left content specific styles */
        .split-content .left-content {
            flex: 6; /* Changed from 4 to 6 */
            width: 60%; /* Changed from 40% to 60% */
            direction: ltr;
            text-align: left !important;
            padding-left: 10px; /* Extra padding on the left side */
        }

        .split-content .right-content * {
            direction: rtl;
            text-align: inherit;
        }

        /* Left content specific styles */
        .split-content .left-content * {
            direction: ltr;
            text-align: inherit;
        }

        /* Isolate font sizes for each section */
        .split-content .right-content [style*="font-size"],
        .split-content .left-content [style*="font-size"] {
            font-size: inherit;
        }

        /* Right content font sizes */
        .split-content .right-content [style*="font-size: 10px"] { font-size: 10px !important; }
        .split-content .right-content [style*="font-size: 12px"] { font-size: 12px !important; }
        .split-content .right-content [style*="font-size: 14px"] { font-size: 14px !important; }
        .split-content .right-content [style*="font-size: 16px"] { font-size: 16px !important; }
        .split-content .right-content [style*="font-size: 18px"] { font-size: 18px !important; }
        .split-content .right-content [style*="font-size: 20px"] { font-size: 20px !important; }

        /* Left content font sizes */
        .split-content .left-content [style*="font-size: 10px"] { font-size: 10px !important; }
        .split-content .left-content [style*="font-size: 12px"] { font-size: 12px !important; }
        .split-content .left-content [style*="font-size: 14px"] { font-size: 14px !important; }
        .split-content .left-content [style*="font-size: 16px"] { font-size: 16px !important; }
        .split-content .left-content [style*="font-size: 18px"] { font-size: 18px !important; }
        .split-content .left-content [style*="font-size: 20px"] { font-size: 20px !important; }

        /* Isolate Quill specific styles for each section */
        /* Right content Quill styles */
        .split-content .right-content .ql-size-small { font-size: 10px !important; }
        .split-content .right-content .ql-size-normal { font-size: 14px !important; }
        .split-content .right-content .ql-size-large { font-size: 18px !important; }
        .split-content .right-content .ql-size-huge { font-size: 20px !important; }

        /* Left content Quill styles */
        .split-content .left-content .ql-size-small { font-size: 10px !important; }
        .split-content .left-content .ql-size-normal { font-size: 14px !important; }
        .split-content .left-content .ql-size-large { font-size: 18px !important; }
        .split-content .left-content .ql-size-huge { font-size: 20px !important; }

        /* Isolate text alignment for each section */
        /* Right content alignments */
        .split-content .right-content [style*="text-align: right"] { text-align: right !important; }
        .split-content .right-content [style*="text-align: left"] { text-align: left !important; }
        .split-content .right-content [style*="text-align: center"] { text-align: center !important; }
        .split-content .right-content [style*="text-align: justify"] { text-align: justify !important; }
        .split-content .right-content .ql-align-right { text-align: right !important; }
        .split-content .right-content .ql-align-left { text-align: left !important; }
        .split-content .right-content .ql-align-center { text-align: center !important; }
        .split-content .right-content .ql-align-justify { text-align: justify !important; }

        /* Left content alignments */
        .split-content .left-content [style*="text-align: right"] { text-align: right !important; }
        .split-content .left-content [style*="text-align: left"] { text-align: left !important; }
        .split-content .left-content [style*="text-align: center"] { text-align: center !important; }
        .split-content .left-content [style*="text-align: justify"] { text-align: justify !important; }
        .split-content .left-content .ql-align-right { text-align: right !important; }
        .split-content .left-content .ql-align-left { text-align: left !important; }
        .split-content .left-content .ql-align-center { text-align: center !important; }
        .split-content .left-content .ql-align-justify { text-align: justify !important; }

        /* Isolate colors and backgrounds for each section */
        .split-content .right-content [style*="color"],
        .split-content .left-content [style*="color"] {
            color: inherit;
        }

        .split-content .right-content [style*="background"],
        .split-content .left-content [style*="background"] {
            background: inherit;
        }

        /* Remove scrollbar styles but keep content visible */
        .split-content .right-content::-webkit-scrollbar,
        .split-content .left-content::-webkit-scrollbar {
            display: none;
        }

        .split-content .right-content,
        .split-content .left-content {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        /* Ensure content flows properly */
        .split-content .right-content > *,
        .split-content .left-content > * {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-wrap: break-word;
            word-wrap: break-word;
            word-break: break-word;
        }

        .variable-placeholder {
            color: var(--danger-color) !important;
            background: #f9ebec !important;
            padding: 2px 5px;
            border-radius: 3px;
            border: 1px dashed var(--danger-color);
            font-family: monospace !important;
            display: inline !important;
            white-space: nowrap !important;
        }

        .error-message {
            color: var(--danger-color);
            padding: 1rem;
            margin: 1rem 0;
            border: 1px solid var(--danger-color);
            border-radius: 8px;
            background: #fdd;
        }

        /* Add these styles to ensure font consistency */
        .contract-content * {
            font-family: 'Times New Roman', 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
        }

        .split-content .right-content,
        .split-content .right-content *,
        .split-content .left-content,
        .split-content .left-content * {
            font-family: 'Times New Roman', 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
        }

        /* Add new styles for the template selector form */
        .template-selector {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }

        .template-form {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .form-group {
            position: relative;
        }

        .template-input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            background: #f8faff;
            font-family: 'Cairo', sans-serif;
        }

        .template-input:disabled {
            background: #e9ecef;
            border-color: #ced4da;
            cursor: not-allowed;
        }

        select.template-input {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: left 1rem center;
            padding-left: 2.5rem;
        }

        .messages {
            margin: 1rem 0;
        }

        .error-message {
            color: var(--danger-color);
            background: #ffebee;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .success-message {
            color: var(--success-color);
            background: #e8f5e9;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        /* Updated styles for the institution header */
        .personal-details {
            page-break-inside: avoid;
            margin: 10px 0;
            padding: 15px 20px;
            width: 100%;
            height: auto;
            min-height: 140px;
            display: flex;
            flex-direction: column;
            background-color: white !important;
            position: relative;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: 100%;
            margin-bottom: 15px;
        }

        .ard-text {
            color: #800080;
            font-family: 'Times New Roman', serif !important;
            font-size: 16px !important;
            font-weight: bold;
            line-height: 1.2 !important;
            text-align: left;
            margin-top: 5px;
        }

        .arabic-title {
            color: #800080;
            font-family: 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
            font-size: 16px !important;
            font-weight: bold;
            line-height: 1.8 !important;
            text-align: right;
            white-space: nowrap;
            display: flex;
            align-items: center;
        }

        .header-bottom {
            width: 100%;
            margin-top: 0;
        }

        .purple-lines-container {
            position: relative;
            width: calc(100% - 40px);
            padding: 0;
            margin: 0 auto 10px auto;
            display: block;
            height: 28px;
        }

        /* Remove old lines */
        .purple-lines-container::before,
        .purple-lines-container::after {
            content: none;
        }

        /* Top three lines */
        .purple-lines-container::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            height: 6px;
            background-image: linear-gradient(
                to bottom,
                #800080 1px,
                transparent 1px,
                transparent 2px,
                #800080 2px,
                transparent 3px,
                transparent 4px,
                #800080 4px,
                transparent 5px
            );
        }

        /* Bottom three lines */
        .purple-lines-container::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 6px;
            background-image: linear-gradient(
                to bottom,
                #800080 1px,
                transparent 1px,
                transparent 2px,
                #800080 2px,
                transparent 3px,
                transparent 4px,
                #800080 4px,
                transparent 5px
            );
        }

        /* Add English title styles */
        .english-title {
            text-align: left;
            color: #800080;
            font-family: 'Times New Roman', serif !important;
            font-size: 16px !important;
            font-weight: normal;
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            margin: 0;
            padding: 4px 0;
            line-height: 1;
            z-index: 1;
        }

        .service-contract-title {
            text-align: center;
            color: #666;
            font-family: 'Times New Roman', serif !important;
            font-size: 16px !important;
            font-weight: bold;
            margin-top: 2px;
        }

        /* Style for the loading spinner */
        .mdi-spin {
            animation: mdi-spin 2s infinite linear;
        }

        @keyframes mdi-spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        
        /* Ensure tables don't get cut off */
        .a4-container table {
            page-break-inside: auto !important;
            width: 100% !important;
        }
        
        /* Ensure health data sections are fully visible */
        .health-data, .medical-info {
            page-break-inside: avoid !important;
            overflow: visible !important;
            margin-top: 5mm !important;
            margin-bottom: 5mm !important;
        }
        
        /* Ensure text elements don't get split across pages */
        .a4-container p, 
        .a4-container h1, 
        .a4-container h2, 
        .a4-container h3, 
        .a4-container h4, 
        .a4-container h5, 
        .a4-container h6 {
            page-break-inside: avoid !important;
            orphans: 3 !important;
            widows: 3 !important;
        }
        
        /* Ensure images are properly sized */
        .a4-container img {
            max-width: 100% !important;
            height: auto !important;
        }
        
        .header-section .ard-text {
            font-size: 18px !important;
        }
        
        .compact-task-list {
            margin: 0;
            padding: 0;
            line-height: 1.1;
            display: block !important;
            font-family: 'Times New Roman', 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
        }

        .compact-task-list .task-item {
            margin: 0;
            padding: 1px 0;
            line-height: 1.1;
            font-family: inherit !important;
            display: block !important;
            text-align: inherit !important;
            color: inherit !important;
            font-size: inherit !important;
        }

        /* Footer styles for double angle bracket content */
        .footer-text-container {
            position: absolute;
            bottom: 15px;
            left: 20px;
            right: 20px;
            padding-top: 10px;
            border-top: 1px solid #000;
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            white-space: pre-wrap;
            font-family: 'Times New Roman', 'Noto Naskh Arabic', 'Traditional Arabic', serif !important;
            z-index: 10;
            background-color: white;
        }

        /* Add the bottom purple line style */
        .bottom-purple-line {
            position: absolute;
            bottom: 5px;
            left: 20px;
            right: 20px;
            height: 6px;
            background-image: linear-gradient(
                to bottom,
                #800080 1px,
                transparent 1px,
                transparent 2px,
                #800080 2px,
                transparent 3px,
                transparent 4px,
                #800080 4px,
                transparent 5px
            );
            z-index: 10;
        }

        /* Special styles for split-page layout footer */
        .split-content + .footer-text-container {
            width: calc(100% - 40px);
            margin-left: auto;
            margin-right: auto;
        }

        /* Ensure footer text has proper text direction based on content */
        .footer-text-container[dir="rtl"] {
            direction: rtl;
            text-align: right;
        }

        .footer-text-container[dir="ltr"] {
            direction: ltr;
            text-align: left;
        }

        /* Page break indicators for preview */
        .page-break-indicator {
            width: 100%;
            height: 300px; /* Changed from 100px to 300px */
            margin: 10px 0;
            margin-top: 50px; /* Increased from 30px to 50px to shift further down */
            position: relative;
            text-align: center;
            page-break-after: always;
            background-color: rgba(255, 255, 255, 0.2); /* Changed from red to white with transparency */
            border: none;
        }

        .page-break-indicator::after {
            content: "";
        }

        /* Export button styles */
        .export-btn-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .export-btn {
            background: #2c3e50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: background 0.3s;
        }

        .export-btn:hover {
            background: #1a252f;
        }

        /* Loading indicator styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }

        .loading-spinner {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .loading-text {
            font-size: 18px;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    $jsonFile = __DIR__ . '/../config/sidebar.json';
    $jsonContent = file_get_contents($jsonFile);
    if ($jsonContent === false) {
        die('Error: Unable to read sidebar configuration file');
    }
    $config = json_decode($jsonContent, true);
    if ($config === null) {
        die('Error: Invalid JSON in sidebar configuration file');
    }
    $currentPage = 'offer.php';
    ?>
    <!-- Main Content -->
    <main>
        <div class="container">
            <!-- Template Selector Section -->
            <div class="template-selector">
                <h2 style="margin-top: 0; color: var(--primary-color)">
                    <i class="mdi mdi-file-document-outline"></i> عرض القالب
                </h2>
                
                <div class="messages">
                    <?php if ($error_message): ?>
                        <div class="error-message">
                            <i class="mdi mdi-alert-circle"></i> <?= htmlspecialchars($error_message) ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success_message): ?>
                        <div class="success-message">
                            <i class="mdi mdi-check-circle"></i> <?= htmlspecialchars($success_message) ?>
                        </div>
                    <?php endif; ?>
                </div>

                <form class="template-form" method="GET" id="templateForm">
                    <!-- Template Type Selection -->
                    <div class="form-group" style="flex: 1;">
                        <select name="template_type" class="template-input" id="templateType" required>
                            <option value="">اختر نوع النموذج</option>
                            <option value="1" <?= $selectedType === '1' ? 'selected' : '' ?>>نموذج عقد أساسي</option>
                            <option value="2" <?= $selectedType === '2' ? 'selected' : '' ?>>نموذج عقد ممدد</option>
                        </select>
                    </div>

                    <!-- Template Selection -->
                    <div class="form-group" style="flex: 1;">
                        <select name="template_id" class="template-input" id="templateId" required <?= empty($templates) ? 'disabled' : '' ?>>
                            <option value="">اختر النموذج</option>
                            <?php foreach ($templates as $template): ?>
                                <option value="<?= htmlspecialchars($template['id_template_contract']) ?>"
                                        <?= ($_GET['template_id'] ?? '') == $template['id_template_contract'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($template['name_template_contract']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Contract ID Input with real-time validation -->
                    <div class="form-group" style="flex: 1;">
                        <input type="number" 
                               name="contract_id" 
                               class="template-input" 
                               placeholder="<?= ($selectedType === '2') ? 'أدخل رقم العقد الممدد' : 'أدخل رقم العقد' ?>" 
                               required 
                               min="1"
                               value="<?= htmlspecialchars($_GET['contract_id'] ?? '') ?>"
                               id="contractIdInput">
                        <div class="validation-feedback" id="validationFeedback"></div>
                    </div>

                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="mdi mdi-eye"></i> عرض القالب
                    </button>
                </form>
            </div>

            <!-- Preview Section -->
            <?php if ($templateData): ?>
                <?php
                // Process the content
                $processedContent = $templateData['content'];
                
                // Extract footer text if present
                $extractedContent = extractFooterText($processedContent);
                $processedContent = $extractedContent['mainContent'];
                $footerText = $extractedContent['footerText'];
                
                // First handle regular variables
                $processedContent = processVariables($processedContent, $variables);
                
                // Also process variables in footer text
                $footerText = processVariables($footerText, $variables);
                
                // Then handle HTML variables to preserve formatting
                if (isset($contractData) && isset($contractData['data_todo_list_contract'])) {
                    $processedContent = processHtmlVariables($processedContent, $contractData);
                    $footerText = processHtmlVariables($footerText, $contractData);
                }
                
                // Apply template formatting if present
                if (isset($templateData['meta']['styles'])) {
                    $styles = $templateData['meta']['styles'];
                    
                    echo '<style>';
                    // Apply margins if present
                    if (isset($styles['margins'])) {
                        $margins = $styles['margins'];
                        
                        if (isset($styles['isSplitPage']) && $styles['isSplitPage']) {
                            // For split pages, apply margins to individual sections
                            echo '#contractContent .split-content .right-content {';
                            echo 'padding-top: ' . htmlspecialchars($margins['top']) . 'mm;';
                            echo 'padding-bottom: ' . htmlspecialchars($margins['bottom']) . 'mm;';
                            echo 'padding-right: ' . htmlspecialchars($margins['right']) . 'mm;';
                            echo '}';
                            
                            echo '#contractContent .split-content .left-content {';
                            echo 'padding-top: ' . htmlspecialchars($margins['top']) . 'mm;';
                            echo 'padding-bottom: ' . htmlspecialchars($margins['bottom']) . 'mm;';
                            echo 'padding-left: ' . htmlspecialchars($margins['left']) . 'mm;';
                            echo '}';
                        } else {
                            // For regular pages, apply margins to the container
                            echo '#contractContent {';
                            echo 'padding-top: ' . htmlspecialchars($margins['top']) . 'mm;';
                            echo 'padding-bottom: ' . htmlspecialchars($margins['bottom']) . 'mm;';
                            echo 'padding-right: ' . htmlspecialchars($margins['right']) . 'mm;';
                            echo 'padding-left: ' . htmlspecialchars($margins['left']) . 'mm;';
                            echo '}';
                        }
                    }

                    // Apply text formatting
                    echo '#contractContent {';
                    if (isset($styles['lineHeight'])) {
                        echo 'line-height: ' . htmlspecialchars($styles['lineHeight']) . ' !important;';
                    }
                    
                    // Preserve whitespace and word wrapping
                    echo 'white-space: pre-wrap;';
                    echo 'word-wrap: break-word;';
                    echo 'margin: 0;';
                    echo 'box-sizing: border-box;';
                    echo '}';

                    // Split page specific styles
                    if (isset($styles['isSplitPage']) && $styles['isSplitPage']) {
                        echo '#contractContent .split-content {';
                        echo 'display: flex;';
                        echo 'gap: 20px;';
                        echo 'min-height: 100%;';
                        echo 'width: 100%;';
                        echo 'position: relative;';
                        echo 'overflow: visible;';
                        echo 'box-sizing: border-box;';
                        echo '}';

                        // Add split page specific styles
                        echo '#contractContent .split-content::after {';
                        echo 'content: "";';
                        echo 'position: absolute;';
                        echo 'left: 60%;';
                        echo 'top: 0;';
                        echo 'bottom: 0;';
                        echo 'width: 1px;';
                        echo 'background-color: #ccc;';
                        echo 'transform: translateX(-50%);';
                        echo 'pointer-events: none;';
                        echo 'z-index: 1;';
                        echo '}';
                    }
                    
                    echo '</style>';
                }
                
                // Get contract name for the filename when exporting to PDF
                $contractName = isset($contractData['name_ar_contract']) ? $contractData['name_ar_contract'] : '';
                ?>
                <div class="a4-container" id="previewContainer" data-contract-name="<?php echo htmlspecialchars($contractName); ?>">
                    <div class="header-section">
                        <?php if ($institutionData): ?>
                            <?php if ($logoPath): ?>
                                <div class="logo-container">
                                    <img src="<?php echo htmlspecialchars($logoPath); ?>" alt="Company Logo" />
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($institutionData): ?>
                        <div class="header-bottom">
                            <div class="purple-lines-container">
                                <span class="english-title">Assistance for Response and Development</span>
                            </div>
                            <div class="service-contract-title">
                                <?php echo ($templateType == '2') ? 'CONTRACT EXTENSION' : 'SERVICE CONTRACT'; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="contract-content" id="contractContent">
                        <?php echo $processedContent; ?>
                    </div>
                    
                    <?php if (!empty($footerText)): ?>
                    <?php 
                        $hasArabic = preg_match('/[\x{0600}-\x{06FF}\x{0750}-\x{077F}]/u', $footerText);
                        $direction = $hasArabic ? 'rtl' : 'ltr';
                    ?>
                    <div class="footer-text-container" dir="<?php echo $direction; ?>">
                        <?php echo $footerText; ?>
                    </div>
                    <?php endif; ?>

                    <div class="bottom-purple-line"></div>
                </div>

                <!-- Add Export Button -->
                <div class="export-btn-container">
                    <button class="export-btn" id="exportPdfBtn">
                        <i class="mdi mdi-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            <?php endif; ?>

            <!-- Add Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                <div class="loading-spinner">
                    <i class="mdi mdi-loading mdi-spin"></i>
                </div>
                <div class="loading-text">جاري تصدير الملف...</div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Call the save_organization_name.php endpoint to update the database
    fetch('save_organization_name.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Organization name saved:', data.message);
            } else {
                console.error('Error saving organization name:', data.message);
            }
        })
        .catch(error => {
            console.error('Error calling save_organization_name.php:', error);
        });
        
    document.addEventListener('DOMContentLoaded', () => {
        const templateType = document.getElementById('templateType');
        const templateForm = document.getElementById('templateForm');
        const contractIdInput = document.querySelector('input[name="contract_id"]');
        const validationFeedback = document.getElementById('validationFeedback');
        const submitBtn = document.getElementById('submitBtn');
        const previewContainer = document.getElementById('previewContainer');
        
        // Function to add page break indicators to the preview for A4 sized content
        function addPageBreakIndicators() {
            if (!previewContainer) return;
            
            // Remove any existing page break indicators
            const existingBreaks = document.querySelectorAll('.page-break-indicator');
            existingBreaks.forEach(breakEl => breakEl.remove());
            
            // A4 dimensions in pixels (assuming 96 DPI for screen)
            const a4HeightPx = 1123; // ~297mm at 96 DPI
            
            // Get contract content element
            const contractContent = document.getElementById('contractContent');
            if (!contractContent) return;
            
            // Check if this is a split-page layout
            const isSplitLayout = contractContent.querySelector('.split-content') !== null;
            
            // Get the header height (header section + header bottom)
            const headerSection = document.querySelector('.header-section');
            const headerBottom = document.querySelector('.header-bottom');
            const headerHeight = (headerSection ? headerSection.offsetHeight : 0) + 
                                (headerBottom ? headerBottom.offsetHeight : 0);
            
            // Calculate footer height if it exists
            const footerTextContainer = document.querySelector('.footer-text-container');
            const bottomPurpleLine = document.querySelector('.bottom-purple-line');
            const footerHeight = (footerTextContainer ? footerTextContainer.offsetHeight : 0) + 
                               (bottomPurpleLine ? bottomPurpleLine.offsetHeight : 0) + 40; // Add some extra margin
            
            // Get the total content height
            const contentHeight = contractContent.scrollHeight;
            
            // Calculate how many pages needed
            const firstPageAvailableHeight = a4HeightPx - headerHeight - footerHeight - 40; // 40px for margins
            const contentPerPage = a4HeightPx - 80 - footerHeight; // 80px for top/bottom margins in subsequent pages
            
            // Content that fits on first page
            let remainingHeight = contentHeight - firstPageAvailableHeight;
            
            // If content fits on first page, no need for page breaks
            if (remainingHeight <= 0) return;
            
            // Create page breaks for subsequent pages
            const numAdditionalPages = Math.ceil(remainingHeight / contentPerPage);
            
            // Initialize an array for element measurements
            const elementMeasurements = [];
            
            // Get all elements in the contract content
            const allElements = Array.from(contractContent.querySelectorAll('*'));
            
            // Record position and size of all content elements
            allElements.forEach(el => {
                // Skip invisible elements or very small elements
                if (el.offsetHeight <= 2 || !isElementVisible(el)) return;
                
                // Record the element's position and dimensions
                elementMeasurements.push({
                element: el,
                top: el.offsetTop,
                    height: el.offsetHeight,
                    bottom: el.offsetTop + el.offsetHeight,
                    // Store the element's tag name and classes for debugging
                    tagName: el.tagName,
                    className: el.className
                });
            });
            
            // Sort elements by their vertical position
            elementMeasurements.sort((a, b) => a.top - b.top);
            
            // Helper function to check if an element is visible
            function isElementVisible(el) {
                const style = window.getComputedStyle(el);
                return style.display !== 'none' && 
                       style.visibility !== 'hidden' && 
                       parseFloat(style.opacity) > 0;
            }
            
            // Calculate break positions
            let breakPositions = [];
            let currentY = firstPageAvailableHeight;
            
            for (let i = 0; i < numAdditionalPages; i++) {
                breakPositions.push(currentY);
                currentY += contentPerPage;
            }
            
            // Find the best location for each page break
            breakPositions.forEach((targetPosition, breakIndex) => {
                // Find elements near this position
                const elementsAroundBreak = elementMeasurements.filter(item => 
                    // Look for elements that end within 150px of the break point
                    Math.abs(item.bottom - targetPosition) < 150 || 
                    // Or elements that span across the break point
                    (item.top < targetPosition && item.bottom > targetPosition)
                );
                
                if (elementsAroundBreak.length === 0) {
                    // If no elements found around the break, use the original position
                    insertPageBreak(contractContent, targetPosition, isSplitLayout);
                    return;
                }
                
                // Sort elements by how close they are to the break
                elementsAroundBreak.sort((a, b) => {
                    // Prefer elements ending just before the break (avoid cutting paragraphs)
                    if (a.bottom <= targetPosition && b.bottom <= targetPosition) {
                        return b.bottom - a.bottom; // Element that ends closest to but before the break
                    }
                    
                    // For elements that cross the break, prefer the one with top closest to break
                    if (a.top < targetPosition && a.bottom > targetPosition &&
                        b.top < targetPosition && b.bottom > targetPosition) {
                        return b.top - a.top; 
                    }
                    
                    // Prefer elements that end before the break over elements that start before and end after
                    if (a.bottom <= targetPosition && b.bottom > targetPosition) return -1;
                    if (b.bottom <= targetPosition && a.bottom > targetPosition) return 1;
                    
                    // Default case - sort by bottom distance to break
                    return Math.abs(a.bottom - targetPosition) - Math.abs(b.bottom - targetPosition);
                });
                
                // Get the best element for this page break
                const bestElement = elementsAroundBreak[0];
                
                // Determine where to place the break
                if (bestElement.bottom <= targetPosition) {
                    // Element ends before the break - insert break after this element
                    insertPageBreakAfter(bestElement.element, isSplitLayout);
                } else if (bestElement.top < targetPosition && bestElement.bottom > targetPosition) {
                    // Element spans across the break - we need to find a good point to break
                    
                    // Check if this element has children that end before the target position
                    const childElements = Array.from(bestElement.element.children || []);
                    
                    if (childElements.length > 0) {
                        // Filter for children that end before the target
                        const childrenBeforeBreak = childElements.filter(child => {
                            const childBottom = child.offsetTop + child.offsetHeight;
                            return childBottom <= targetPosition && childBottom > targetPosition - 150;
                        });
                        
                        if (childrenBeforeBreak.length > 0) {
                            // Find the child that ends closest to the target
                            childrenBeforeBreak.sort((a, b) => {
                                const aBottom = a.offsetTop + a.offsetHeight;
                                const bBottom = b.offsetTop + b.offsetHeight;
                                return bBottom - aBottom; // Closest to target first
                            });
                            
                            // Insert break after the best child
                            insertPageBreakAfter(childrenBeforeBreak[0], isSplitLayout);
                            return;
                        }
                    }
                    
                    // If we get here, we couldn't find a good child to break after
                    // Determine if we should break before the element or find the previous element
                    
                    // Look for the element just before this one
                    const elementIndex = elementMeasurements.findIndex(item => item === bestElement);
                    if (elementIndex > 0) {
                        const prevElement = elementMeasurements[elementIndex - 1];
                        
                        if (prevElement.bottom <= targetPosition) {
                            // Previous element ends before target, break after it
                            insertPageBreakAfter(prevElement.element, isSplitLayout);
                            return;
                        }
                    }
                    
                    // As a last resort, break at a specific position
                    insertPageBreak(contractContent, targetPosition, isSplitLayout);
                } else {
                    // Fallback - break at target position
                    insertPageBreak(contractContent, targetPosition, isSplitLayout);
                }
            });
            
            // Helper function to insert a page break after a specific element
            function insertPageBreakAfter(element, isSplitLayout) {
                const pageBreak = document.createElement('div');
                pageBreak.className = 'page-break-indicator';
                
                // For split layouts, we need to handle special cases
                if (isSplitLayout) {
                    // Check if the element is inside a split section
                    const rightContent = element.closest('.right-content');
                    const leftContent = element.closest('.left-content');
                    
                    if (rightContent || leftContent) {
                        // This is within one side of the split content
                        
                        // First, insert break after this element
                        if (element.nextElementSibling) {
                            element.parentNode.insertBefore(pageBreak, element.nextElementSibling);
                        } else {
                            element.parentNode.appendChild(pageBreak);
                        }
                        
                        // Now, check what the same Y position would be on the other side
                        const elementRect = element.getBoundingClientRect();
                        
                        const targetContainer = rightContent ? 
                            rightContent.parentNode.querySelector('.left-content') : 
                            leftContent.parentNode.querySelector('.right-content');
                        
                        if (targetContainer) {
                            // Find an element at approximately the same Y position in the other container
                            const targetElements = Array.from(targetContainer.querySelectorAll('*'))
                                .filter(el => {
                                    if (el.offsetHeight <= 2 || !isElementVisible(el)) return false;
                                    const rect = el.getBoundingClientRect();
                                    return Math.abs(rect.bottom - elementRect.bottom) < 50; // Within 50px of the same position
                                });
                            
                            if (targetElements.length > 0) {
                                // Sort by closest to the Y position
                                targetElements.sort((a, b) => {
                                    const aRect = a.getBoundingClientRect();
                                    const bRect = b.getBoundingClientRect();
                                    return Math.abs(aRect.bottom - elementRect.bottom) - 
                                           Math.abs(bRect.bottom - elementRect.bottom);
                                });
                                
                                // Insert another page break in the other column at the matching position
                                const matchingEl = targetElements[0];
                                const otherBreak = document.createElement('div');
                                otherBreak.className = 'page-break-indicator';
                                
                                if (matchingEl.nextElementSibling) {
                                    matchingEl.parentNode.insertBefore(otherBreak, matchingEl.nextElementSibling);
                                } else {
                                    matchingEl.parentNode.appendChild(otherBreak);
                                }
                            } else {
                                // If we can't find a good match, insert at the same relative position in the container
                                const relativePosition = (elementRect.bottom - rightContent.getBoundingClientRect().top) /
                                                       rightContent.getBoundingClientRect().height;
                                
                                const yPosition = targetContainer.offsetHeight * relativePosition;
                                insertPageBreakAtPosition(targetContainer, yPosition);
                            }
                        }
                        return;
                    }
                }
                
                // Regular case (not split layout or outside split sections)
                if (element.nextElementSibling) {
                    element.parentNode.insertBefore(pageBreak, element.nextElementSibling);
                } else {
                    element.parentNode.appendChild(pageBreak);
                }
            }
            
            // Helper function to insert a page break before a specific element
            function insertPageBreakBefore(element, isSplitLayout) {
                const pageBreak = document.createElement('div');
                pageBreak.className = 'page-break-indicator';
                
                // For split layouts, apply the same logic as insertPageBreakAfter
                if (isSplitLayout) {
                    const rightContent = element.closest('.right-content');
                    const leftContent = element.closest('.left-content');
                    
                    if (rightContent || leftContent) {
                        // Insert in current side
                        element.parentNode.insertBefore(pageBreak, element);
                        
                        // Find matching element in other side
                        const elementRect = element.getBoundingClientRect();
                        
                        const targetContainer = rightContent ? 
                            rightContent.parentNode.querySelector('.left-content') : 
                            leftContent.parentNode.querySelector('.right-content');
                        
                        if (targetContainer) {
                            const targetElements = Array.from(targetContainer.querySelectorAll('*'))
                                .filter(el => {
                                    if (el.offsetHeight <= 2 || !isElementVisible(el)) return false;
                                    const rect = el.getBoundingClientRect();
                                    return Math.abs(rect.top - elementRect.top) < 50;
                                });
                            
                            if (targetElements.length > 0) {
                                targetElements.sort((a, b) => {
                                    const aRect = a.getBoundingClientRect();
                                    const bRect = b.getBoundingClientRect();
                                    return Math.abs(aRect.top - elementRect.top) - 
                                           Math.abs(bRect.top - elementRect.top);
                                });
                                
                                const matchingEl = targetElements[0];
                                const otherBreak = document.createElement('div');
                                otherBreak.className = 'page-break-indicator';
                                matchingEl.parentNode.insertBefore(otherBreak, matchingEl);
                            } else {
                                const relativePosition = (elementRect.top - rightContent.getBoundingClientRect().top) /
                                                       rightContent.getBoundingClientRect().height;
                                
                                const yPosition = targetContainer.offsetHeight * relativePosition;
                                insertPageBreakAtPosition(targetContainer, yPosition);
                            }
                        }
                        return;
                    }
                }
                
                // Regular case
                element.parentNode.insertBefore(pageBreak, element);
            }
            
            // Helper function to insert a page break at an absolute position
            function insertPageBreak(container, position, isSplitLayout) {
                if (isSplitLayout) {
                    // For split layouts, we need to insert the break in both the right and left content
                    const splitContent = container.querySelector('.split-content');
                    if (splitContent) {
                        const rightContent = splitContent.querySelector('.right-content');
                        const leftContent = splitContent.querySelector('.left-content');
                        
                        if (rightContent && leftContent) {
                            // Calculate the relative position within the content
                            const relativePosition = position / container.scrollHeight;
                            
                            // Insert at the same relative position in both sides
                            const rightYPosition = rightContent.scrollHeight * relativePosition;
                            const leftYPosition = leftContent.scrollHeight * relativePosition;
                            
                            insertPageBreakAtPosition(rightContent, rightYPosition);
                            insertPageBreakAtPosition(leftContent, leftYPosition);
                            return;
                        }
                    }
                }
                
                // Regular case (not a split layout)
                const pageBreak = document.createElement('div');
                pageBreak.className = 'page-break-indicator';
                pageBreak.style.position = 'absolute';
                pageBreak.style.top = position + 'px';
                pageBreak.style.left = '0';
                pageBreak.style.right = '0';
                container.appendChild(pageBreak);
            }
            
            // Helper function to insert a page break at a specific Y position within a container
            function insertPageBreakAtPosition(container, yPosition) {
                // Find elements near this position
                const elements = Array.from(container.querySelectorAll('*'))
                    .filter(el => {
                        if (el.offsetHeight <= 2 || !isElementVisible(el)) return false;
                        const elTop = el.offsetTop;
                        const elBottom = elTop + el.offsetHeight;
                        return elTop <= yPosition && elBottom >= yPosition; // Element spans the position
                    });
                
                if (elements.length > 0) {
                    // Find the element most precisely spanning the position
                    elements.sort((a, b) => {
                        const aTop = a.offsetTop;
                        const aBottom = aTop + a.offsetHeight;
                        const bTop = b.offsetTop;
                        const bBottom = bTop + b.offsetHeight;
                        
                        const aHeight = aBottom - aTop;
                        const bHeight = bBottom - bTop;
                        
                        // Prefer smaller elements (more precise)
                        return aHeight - bHeight;
                    });
                    
                    const targetElement = elements[0];
                    const elementTop = targetElement.offsetTop;
                    const elementHeight = targetElement.offsetHeight;
                    
                    // If position is closer to the bottom, insert after
                    if (yPosition >= elementTop + elementHeight / 2) {
                        if (targetElement.nextElementSibling) {
                            const pageBreak = document.createElement('div');
                            pageBreak.className = 'page-break-indicator';
                            targetElement.parentNode.insertBefore(pageBreak, targetElement.nextElementSibling);
                        } else {
                            const pageBreak = document.createElement('div');
                            pageBreak.className = 'page-break-indicator';
                            targetElement.parentNode.appendChild(pageBreak);
                        }
                    } else {
                        // Otherwise insert before
                        const pageBreak = document.createElement('div');
                        pageBreak.className = 'page-break-indicator';
                        targetElement.parentNode.insertBefore(pageBreak, targetElement);
                    }
                } else {
                    // If no suitable element, insert an absolute positioned break
                    const pageBreak = document.createElement('div');
                    pageBreak.className = 'page-break-indicator';
                    pageBreak.style.position = 'absolute';
                    pageBreak.style.top = yPosition + 'px';
                    pageBreak.style.left = '0';
                    pageBreak.style.right = '0';
                    container.appendChild(pageBreak);
                }
            }
        }
        
        // Call the function when the template is loaded and displayed
        if (previewContainer) {
            // Wait for a bit to ensure the content is fully rendered
            setTimeout(addPageBreakIndicators, 500);
            
            // Also add page breaks if window is resized
            window.addEventListener('resize', () => {
                setTimeout(addPageBreakIndicators, 300);
            });
        }
        
        // Add debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Function to validate contract number
        const validateContractNumber = debounce(async (contractId, isExtended) => {
            if (!contractId) return;
            
            try {
                const response = await fetch(`get_report_contract_con/check_contract.php?contract_id=${contractId}&is_extended=${isExtended ? 1 : 0}`);
                const data = await response.json();
                
                if (data.exists) {
                    validationFeedback.textContent = 'رقم العقد صحيح';
                    validationFeedback.className = 'validation-feedback valid';
                    submitBtn.disabled = false;
                } else {
                    validationFeedback.textContent = isExtended ? 'رقم العقد الممدد غير موجود' : 'رقم العقد غير موجود';
                    validationFeedback.className = 'validation-feedback invalid';
                    submitBtn.disabled = true;
                }
            } catch (error) {
                validationFeedback.textContent = 'حدث خطأ في التحقق من رقم العقد';
                validationFeedback.className = 'validation-feedback invalid';
                submitBtn.disabled = true;
            }
        }, 500);
        
        // Add validation styles
        const style = document.createElement('style');
        style.textContent = `
            .validation-feedback {
                font-size: 0.875rem;
                margin-top: 0.25rem;
            }
            .validation-feedback.valid {
                color: #198754;
            }
            .validation-feedback.invalid {
                color: #dc3545;
            }
            .template-input.is-invalid {
                border-color: #dc3545;
            }
            .template-input.is-valid {
                border-color: #198754;
            }
        `;
        document.head.appendChild(style);
        
        // Event listeners
        templateType.addEventListener('change', () => {
            // Update placeholder text based on selected type
            if (templateType.value === '2') {
                contractIdInput.placeholder = 'أدخل رقم العقد الممدد';
            } else {
                contractIdInput.placeholder = 'أدخل رقم العقد';
            }
            
            // Clear validation state
            validationFeedback.textContent = '';
            validationFeedback.className = 'validation-feedback';
            contractIdInput.value = '';
            contractIdInput.classList.remove('is-valid', 'is-invalid');
            
            // Submit form when type changes to load templates
            templateForm.submit();
        });
        
        // Add input validation
        contractIdInput.addEventListener('input', (e) => {
            const isExtended = templateType.value === '2';
            const contractId = e.target.value;
            
            if (contractId) {
                validateContractNumber(contractId, isExtended);
            } else {
                validationFeedback.textContent = '';
                validationFeedback.className = 'validation-feedback';
                e.target.classList.remove('is-valid', 'is-invalid');
            }
        });
    });

    // Add PDF export functionality
    document.addEventListener('DOMContentLoaded', () => {
        const exportPdfBtn = document.getElementById('exportPdfBtn');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const previewContainer = document.getElementById('previewContainer');

        if (exportPdfBtn && previewContainer) {
            exportPdfBtn.addEventListener('click', async () => {
                try {
                    // Show loading overlay
                    loadingOverlay.style.display = 'flex';

                    // Create a new jsPDF instance
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'mm',
                        format: 'a4'
                    });

                    // Get the container dimensions
                    const container = document.getElementById('previewContainer');
                    const containerWidth = container.offsetWidth;
                    const containerHeight = container.scrollHeight;

                    // Calculate A4 dimensions in pixels (assuming 96 DPI)
                    const a4Width = 210; // mm
                    const a4Height = 297; // mm
                    const pixelsPerMm = 96 / 25.4; // 96 DPI / 25.4 mm per inch
                    const a4WidthPx = a4Width * pixelsPerMm;
                    const a4HeightPx = a4Height * pixelsPerMm;

                    // Calculate number of pages needed
                    const numPages = Math.ceil(containerHeight / a4HeightPx);

                    // Create a temporary container for the entire content
                    const tempContainer = document.createElement('div');
                    tempContainer.style.position = 'absolute';
                    tempContainer.style.left = '-9999px';
                    tempContainer.style.top = '0';
                    tempContainer.style.width = `${containerWidth}px`;
                    tempContainer.style.height = `${containerHeight}px`;
                    tempContainer.style.overflow = 'hidden';
                    tempContainer.style.backgroundColor = '#ffffff';
                    
                    // Clone the content
                    const contentClone = container.cloneNode(true);
                    tempContainer.appendChild(contentClone);
                    document.body.appendChild(tempContainer);

                    // Extract the header section (header-section + header-bottom)
                    const headerSection = container.querySelector('.header-section');
                    const headerBottom = container.querySelector('.header-bottom');
                    let headerHeight = 0;
                    let headerCanvas = null;
                    
                    if (headerSection && headerBottom) {
                        // Create a temporary container for the header
                        const headerContainer = document.createElement('div');
                        headerContainer.style.position = 'absolute';
                        headerContainer.style.left = '-9999px';
                        headerContainer.style.top = '0';
                        headerContainer.style.width = `${containerWidth}px`;
                        headerContainer.style.backgroundColor = '#ffffff';
                        
                        // Clone the header elements
                        const headerSectionClone = headerSection.cloneNode(true);
                        const headerBottomClone = headerBottom.cloneNode(true);
                        
                        headerContainer.appendChild(headerSectionClone);
                        headerContainer.appendChild(headerBottomClone);
                        document.body.appendChild(headerContainer);
                        
                        // Calculate header height
                        headerHeight = headerSection.offsetHeight + headerBottom.offsetHeight;
                        
                        // Capture the header as canvas
                        headerCanvas = await html2canvas(headerContainer, {
                            scale: 2,
                            useCORS: true,
                            logging: false,
                            allowTaint: true,
                            backgroundColor: '#ffffff',
                            windowWidth: containerWidth
                        });
                        
                        // Clean up
                        document.body.removeChild(headerContainer);
                    }

                    // Create canvas for each page
                    for (let i = 0; i < numPages; i++) {
                        // Calculate the portion of the container to capture
                        const startY = i * a4HeightPx;
                        const endY = Math.min(startY + a4HeightPx, containerHeight);

                        // Create a viewport for this page
                        const viewport = document.createElement('div');
                        viewport.style.position = 'absolute';
                        viewport.style.left = '0';
                        viewport.style.top = `${startY}px`;
                        viewport.style.width = `${containerWidth}px`;
                        viewport.style.height = `${a4HeightPx}px`;
                        viewport.style.overflow = 'hidden';
                        viewport.style.backgroundColor = '#ffffff';
                        
                        // Clone the content for this viewport
                        const pageContent = contentClone.cloneNode(true);
                        pageContent.style.position = 'absolute';
                        pageContent.style.top = `-${startY}px`;
                        pageContent.style.left = '0';
                        viewport.appendChild(pageContent);
                        tempContainer.appendChild(viewport);

                        // Create canvas and capture the content
                        const canvas = await html2canvas(viewport, {
                            scale: 2, // Higher scale for better quality
                            useCORS: true,
                            logging: false,
                            allowTaint: true,
                            backgroundColor: '#ffffff',
                            windowWidth: containerWidth,
                            windowHeight: a4HeightPx
                        });

                        // Remove the viewport
                        tempContainer.removeChild(viewport);

                        // Add the canvas to the PDF
                        const imgData = canvas.toDataURL('image/jpeg', 1.0);
                        pdf.addImage(imgData, 'JPEG', 0, 0, a4Width, a4Height);
                        
                        // If not the first page and we have a header, overlay the header on this page
                        if (i > 0 && headerCanvas) {
                            // Calculate header height in mm
                            const headerHeightMm = (headerHeight / a4HeightPx) * a4Height;
                            
                            // Convert header canvas to image data
                            const headerImgData = headerCanvas.toDataURL('image/jpeg', 1.0);
                            
                            // Add header image on top of the page content
                            pdf.addImage(headerImgData, 'JPEG', 0, 0, a4Width, headerHeightMm);
                        }

                        // Add new page if not the last page
                        if (i < numPages - 1) {
                            pdf.addPage();
                        }
                    }

                    // Clean up
                    document.body.removeChild(tempContainer);

                    // Save the PDF using the contract name from the data attribute
                    const previewContainer = document.getElementById('previewContainer');
                    let contractName = previewContainer.getAttribute('data-contract-name') || '';
                    
                    // Generate filename with contract name or default to date
                    const filename = contractName 
                        ? `عقد_${contractName.replace(/[\\/:*?"<>|]/g, '_')}.pdf`
                        : `contract_${new Date().toISOString().slice(0, 10)}.pdf`;
                    
                    pdf.save(filename);

                    // Hide loading overlay
                    loadingOverlay.style.display = 'none';
                } catch (error) {
                    console.error('Error exporting PDF:', error);
                    alert('حدث خطأ أثناء تصدير الملف. يرجى المحاولة مرة أخرى.');
                    loadingOverlay.style.display = 'none';
                }
            });
        }
    });
    </script>
</body>
</html>
