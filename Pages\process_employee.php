<?php
// Start session and error reporting
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

// Check if it's an AJAX request with the correct action
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['action']) || $_POST['action'] !== 'add_from_excel') {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

// Check if employee data is provided
if (!isset($_POST['employee']) || !is_array($_POST['employee'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات الموظف غير صحيحة']);
    exit;
}

try {
    // Get employee data
    $employee = $_POST['employee'];
    
    // Basic validation
    if (empty($employee['الاسم العربي']) || empty($employee['الاسم الإنجليزي']) || 
        empty($employee['نوع الهوية ar']) || empty($employee['نوع الهوية en']) || 
        empty($employee['رقم الهوية'])) {
        echo json_encode(['success' => false, 'message' => 'البيانات الأساسية غير مكتملة']);
        exit;
    }
    
    // Parse data from excel format
    $name_ar = $employee['الاسم العربي'];
    $name_en = $employee['الاسم الإنجليزي'];
    $identity_type_ar = $employee['نوع الهوية ar'];
    $identity_type_en = $employee['نوع الهوية en'];
    $identity_number = $employee['رقم الهوية'];
    $identity_issue_ar = isset($employee['مكان الإصدار ar']) ? $employee['مكان الإصدار ar'] : '';
    $identity_issue_en = isset($employee['مكان الإصدار en']) ? $employee['مكان الإصدار en'] : '';
    $identity_issue_date = isset($employee['تاريخ الإصدار']) ? $employee['تاريخ الإصدار'] : '';
    
    // Convert date format from DD/MM/YYYY to YYYY-MM-DD for MySQL
    if (!empty($identity_issue_date)) {
        $date_parts = explode('/', $identity_issue_date);
        if (count($date_parts) === 3) {
            $identity_issue_date = $date_parts[2] . '-' . $date_parts[1] . '-' . $date_parts[0];
        }
    }
    
    // Read database connection details
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    // Create database connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    // Check for duplicates - name
    $check_name_sql = "SELECT * FROM employees WHERE name_ar_contract = ? OR name_en_contract = ?";
    $check_name_stmt = $conn->prepare($check_name_sql);
    $check_name_stmt->bind_param("ss", $name_ar, $name_en);
    $check_name_stmt->execute();
    $name_result = $check_name_stmt->get_result();
    
    if ($name_result->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'يوجد موظف مسجل بنفس الاسم بالفعل']);
        $check_name_stmt->close();
        $conn->close();
        exit;
    }
    $check_name_stmt->close();
    
    // Check for duplicates - ID number
    if ($identity_type_en == 'National ID') {
        $check_id_sql = "SELECT * FROM employees WHERE Identity_number_contract = ? AND Identity_contract_en = 'National ID'";
    } else if ($identity_type_en == 'Passport') {
        $check_id_sql = "SELECT * FROM employees WHERE Identity_number_contract = ? AND Identity_contract_en = 'Passport'";
    } else {
        $check_id_sql = "SELECT * FROM employees WHERE Identity_number_contract = ?";
    }
    
    $check_id_stmt = $conn->prepare($check_id_sql);
    $check_id_stmt->bind_param("s", $identity_number);
    $check_id_stmt->execute();
    $id_result = $check_id_stmt->get_result();
    
    if ($id_result->num_rows > 0) {
        $message = ($identity_type_en == 'National ID') ? 'يوجد موظف مسجل بنفس رقم البطاقة الشخصية' : 
                  (($identity_type_en == 'Passport') ? 'يوجد موظف مسجل بنفس رقم جواز السفر' : 'يوجد موظف مسجل بنفس رقم الهوية');
        
        echo json_encode(['success' => false, 'message' => $message]);
        $check_id_stmt->close();
        $conn->close();
        exit;
    }
    $check_id_stmt->close();
    
    // All checks passed, insert the new employee
    $sql = "INSERT INTO employees (
        name_ar_contract,
        name_en_contract,
        Identity_contract_ar,
        Identity_contract_en,
        Identity_number_contract,
        Identity_issue_contract_ar,
        Identity_issue_contract_en,
        Identity_issue_date_contract
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
    }
    
    $stmt->bind_param("ssssssss",
        $name_ar,
        $name_en,
        $identity_type_ar,
        $identity_type_en,
        $identity_number,
        $identity_issue_ar,
        $identity_issue_en,
        $identity_issue_date
    );
    
    if (!$stmt->execute()) {
        throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
    }
    
    echo json_encode(['success' => true, 'message' => 'تم إضافة الموظف بنجاح']);
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 