$ProgressPreference = 'SilentlyContinue'

# Create directories if they don't exist
$directories = @(
    "www\assets\lib\bootstrap",
    "www\assets\lib\jquery",
    "www\assets\lib\select2",
    "www\assets\lib\datatables",
    "www\assets\lib\moment",
    "www\assets\fonts\cairo"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Force -Path $dir
    }
}

# Download Bootstrap files
$bootstrapFiles = @{
    "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" = "www\assets\lib\bootstrap\bootstrap.min.css"
    "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" = "www\assets\lib\bootstrap\bootstrap.bundle.min.js"
    "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" = "www\assets\lib\bootstrap\bootstrap-icons.css"
}

foreach ($file in $bootstrapFiles.GetEnumerator()) {
    Write-Host "Downloading $($file.Key) to $($file.Value)"
    Invoke-WebRequest -Uri $file.Key -OutFile $file.Value
}

# Download jQuery
Write-Host "Downloading jQuery"
Invoke-WebRequest -Uri "https://code.jquery.com/jquery-3.7.1.min.js" -OutFile "www\assets\lib\jquery\jquery.min.js"

# Download Select2
$select2Files = @{
    "https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" = "www\assets\lib\select2\select2.min.css"
    "https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js" = "www\assets\lib\select2\select2.min.js"
}

foreach ($file in $select2Files.GetEnumerator()) {
    Write-Host "Downloading $($file.Key) to $($file.Value)"
    Invoke-WebRequest -Uri $file.Key -OutFile $file.Value
}

# Download DataTables
$dataTablesFiles = @{
    "https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" = "www\assets\lib\datatables\dataTables.bootstrap5.min.css"
    "https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js" = "www\assets\lib\datatables\jquery.dataTables.min.js"
    "https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js" = "www\assets\lib\datatables\dataTables.bootstrap5.min.js"
}

foreach ($file in $dataTablesFiles.GetEnumerator()) {
    Write-Host "Downloading $($file.Key) to $($file.Value)"
    Invoke-WebRequest -Uri $file.Key -OutFile $file.Value
}

# Download Moment.js
Write-Host "Downloading Moment.js"
Invoke-WebRequest -Uri "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js" -OutFile "www\assets\lib\moment\moment.min.js"
Invoke-WebRequest -Uri "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.js" -OutFile "www\assets\lib\moment\moment-ar.js"

# Download Cairo Font files
$cairoFontFiles = @{
    "https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2" = "www\assets\fonts\cairo\cairo-regular.woff2"
    "https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcSCyS4J0.woff2" = "www\assets\fonts\cairo\cairo-bold.woff2"
}

foreach ($file in $cairoFontFiles.GetEnumerator()) {
    Write-Host "Downloading $($file.Key) to $($file.Value)"
    Invoke-WebRequest -Uri $file.Key -OutFile $file.Value
}

Write-Host "All dependencies downloaded successfully!" 