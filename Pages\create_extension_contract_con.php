<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

$projects = [];
$contracts = [];
$selectedContract = null;
$extensions = [];
$error_message = '';
$success_message = '';

if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) throw new Exception('خطأ في قراءة ملف الإعدادات');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    if (isset($_GET['action']) && $_GET['action'] === 'get_contracts' && isset($_GET['project_id'])) {
        header('Content-Type: application/json');
        $projectId = intval($_GET['project_id']);
        
        try {
            error_log("Fetching contracts for project ID: " . $projectId);
            
            if ($conn->connect_error) {
                throw new Exception("Database connection failed: " . $conn->connect_error);
            }
            
            $query = "SELECT c.id_contract, e.name_ar_contract as employee_name, c.version_date, c.name_Job 
                     FROM contract c 
                     INNER JOIN employees e ON c.id_employees = e.id_employees 
                     WHERE c.id_Project = ? AND c.status_contract = 1";
            error_log("Preparing query: " . $query);
            
            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception("Error preparing statement: " . $conn->error);
            }
            
            $stmt->bind_param("i", $projectId);
            error_log("Executing query with project ID: " . $projectId);
            
            if (!$stmt->execute()) {
                throw new Exception("Error executing statement: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            if (!$result) {
                throw new Exception("Error getting result: " . $stmt->error);
            }
            
            $contracts = [];
            while ($row = $result->fetch_assoc()) {
                $contracts[] = [
                    'id' => $row['id_contract'],
                    'text' => sprintf('رقم العقد %s - %s - %s', 
                        $row['id_contract'],
                        $row['employee_name'],
                        date('Y-m-d', strtotime($row['version_date']))
                    )
                ];
            }
            
            error_log("Found " . count($contracts) . " contracts for project ID: " . $projectId);
            echo json_encode($contracts);
            exit;
            
        } catch (Exception $e) {
            error_log("Error in get_contracts: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'error' => $e->getMessage(),
                'details' => [
                    'project_id' => $projectId,
                    'mysql_error' => $conn->error ?? 'No MySQL error',
                    'stmt_error' => $stmt->error ?? 'No statement error'
                ]
            ]);
            exit;
        }
    }

    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    while ($row = $result->fetch_assoc()) {
        $projects[] = $row;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contract_id'])) {
        $contractId = intval($_POST['contract_id']);
        
        $stmt = $conn->prepare("
            SELECT c.*, e.name_ar_contract 
            FROM contract c
            LEFT JOIN employees e ON c.id_employees = e.id_employees 
            WHERE c.id_contract = ?
        ");
        $stmt->bind_param("i", $contractId);
        $stmt->execute();
        $selectedContract = $stmt->get_result()->fetch_assoc();
        
        $stmt = $conn->prepare("SELECT 
                                id_extension_contract,
                                version_date,
                                start_date_contract,
                                end_date_contract,
                                add_extension_contract 
                            FROM extension_contract 
                            WHERE id_contract = ? 
                            ORDER BY add_extension_contract DESC");
        $stmt->bind_param("i", $contractId);
        $stmt->execute();
        $result = $stmt->get_result();
        $extensions = [];
        while ($row = $result->fetch_assoc()) {
            $extensions[] = $row;
        }
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_extension'])) {
        $contractId = intval($_POST['contract_id']);
        $versionDate = $_POST['version_date'];
        $startDate = $_POST['start_date'];
        $endDate = $_POST['end_date'] ?: null;
        
        $inTransaction = false;
        
        try {
            $conn->autocommit(FALSE);
            $inTransaction = true;

            $stmt = $conn->prepare("SELECT id_Project, end_date_contract, add_contract, wage_contract 
                                  FROM contract WHERE id_contract = ? FOR UPDATE");
            $stmt->bind_param("i", $contractId);
            $stmt->execute();
            $mainContract = $stmt->get_result()->fetch_assoc();

            if (!$mainContract) {
                throw new Exception("العقد الرئيسي غير موجود");
            }

            if ($mainContract['end_date_contract'] === null) {
                throw new Exception("لا يمكن إنشاء تمديد لهذا العقد لأنه عقد مفتوح الأجل");
            }

            $stmt = $conn->prepare("SELECT end_date_contract, add_extension_contract 
                                  FROM extension_contract 
                                  WHERE id_contract = ? 
                                  ORDER BY add_extension_contract DESC FOR UPDATE");
            $stmt->bind_param("i", $contractId);
            $stmt->execute();
            $extensions = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

            foreach ($extensions as $ext) {
                if ($ext['end_date_contract'] === null) {
                    $conn->rollback();
                    $inTransaction = false;
                    throw new Exception("لا يمكن إنشاء تمديد جديد بسبب وجود تمديد مفتوح الأجل");
                }
            }

            $lastEndDate = date('Y-m-d', strtotime($mainContract['end_date_contract']));
            if (!empty($extensions)) {
                $lastExtension = $extensions[0];
                $lastEndDate = date('Y-m-d', strtotime($lastExtension['end_date_contract']));
            }

            if (empty($startDate)) {
                $conn->rollback();
                $inTransaction = false;
                throw new Exception("يجب تحديد تاريخ بدء العقد الممتد");
            }

            if (strtotime($startDate) < strtotime($lastEndDate)) {
                $conn->rollback();
                $inTransaction = false;
                throw new Exception("تاريخ البدء يجب أن يكون مساويًا أو بعد تاريخ انتهاء العقد السابق (" . $lastEndDate . ")");
            }

            $stmt = $conn->prepare("INSERT INTO extension_contract 
                                  (id_Project, id_contract, version_date, start_date_contract, 
                                  end_date_contract, status_contract, wage_contract, add_extension_contract) 
                                  VALUES (?, ?, ?, ?, ?, 1, ?, NOW())");
            $stmt->bind_param("iissss", 
                $mainContract['id_Project'],
                $contractId,
                $versionDate,
                $startDate,
                $endDate,
                $mainContract['wage_contract']
            );

            if (!$stmt->execute()) {
                $conn->rollback();
                $inTransaction = false;
                throw new Exception("فشل في إنشاء تمديد العقد");
            }

            $stmt = $conn->prepare("UPDATE contract SET extension = extension + 1 WHERE id_contract = ?");
            $stmt->bind_param("i", $contractId);
            $stmt->execute();

            $conn->commit();
            $inTransaction = false;

            $_SESSION['success_message'] = "تم إنشاء تمديد العقد بنجاح";
            header("Location: " . $_SERVER['PHP_SELF']);
            exit;

        } catch (Exception $e) {
            if ($inTransaction) {
                $conn->rollback();
            }
            $error_message = $e->getMessage();
        } finally {
            if ($inTransaction) {
                $conn->rollback();
            }
            $conn->autocommit(TRUE);
        }
    }

} catch (Exception $e) {
    $error_message = "خطأ في النظام: " . $e->getMessage();
} finally {
    if (isset($conn)) $conn->close();
}

$minStartDate = null;
if ($selectedContract) {
    $minStartDate = date('Y-m-d', strtotime($selectedContract['end_date_contract']));
    if (!empty($extensions)) {
        $minStartDate = date('Y-m-d', strtotime($extensions[0]['end_date_contract']));
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التمديدات - نظام إدارة الموارد البشرية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: #2d303d;
        }

        .section-title {
            color: #ffffff;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Card styling */
        .card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
        }

        .card-title {
            color: #ffffff;
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Form controls */
        .form-control, .form-select {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.625rem;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: #0d6efd;
            color: var(--text-primary);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* Contract details styling */
        .detail-item {
            padding: 0.5rem;
            background-color: transparent;
            height: 100%;
        }

        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.125rem;
            font-size: 0.875rem;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* Extension list styling */
        .extension-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .extension-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
            min-width: min-content;
        }

        .extension-item:hover {
            background-color: var(--bg-hover);
            border-color: #0d6efd;
        }

        .extension-info {
            display: flex;
            align-items: center;
            gap: 2rem;
            flex: 1;
            white-space: nowrap;
        }

        .extension-info > div {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .extension-info .detail-label {
            margin-bottom: 0;
            color: var(--text-muted);
        }

        .extension-info .detail-value {
            margin-right: 0.5rem;
            color: var(--text-primary);
        }

        /* Scrollbar styling for the extension list */
        .extension-list::-webkit-scrollbar {
            width: 8px;
        }

        .extension-list::-webkit-scrollbar-track {
            background: var(--bg-card);
            border-radius: 4px;
        }

        .extension-list::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        .extension-list::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        .no-extensions {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px dashed var(--border-color);
        }

        /* Alert styling */
        .custom-alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }

        .custom-alert.success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .custom-alert.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
            background-color: var(--bg-info);
            color: var(--text-info);
            border-color: var(--border-info);
        }

        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Select2 customization */
        .select2-container--default .select2-selection--single {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background-color: var(--bg-input);
            transition: all 0.3s ease;
        }

        .select2-container--default .select2-selection--single:hover {
            border-color: #0d6efd;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 46px;
            padding-right: 1rem;
            color: var(--text-primary);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 46px;
        }

        /* Button styling */
        .btn-primary {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .btn-primary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <main id="content">
        <div class="container-fluid py-4">
            <?php if (!empty($error_message)): ?>
                <div class="custom-alert error" role="alert">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="custom-alert success" role="alert">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <!-- Added informational message -->
            <div class="alert alert-info mb-4" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                مرحباً بك في صفحة إنشاء تمديدات العقود. يمكنك من خلال هذه الصفحة إنشاء وإدارة تمديدات العقود الحالية، وتحديد فترات التمديد وتواريخ البداية والنهاية لكل تمديد.
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-4">البحث عن العقود</h5>
                    <form method="post" id="searchForm">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                    <select class="form-select select2-search" id="project_id" name="project_id" required>
                                        <option value="">اختر المشروع</option>
                                        <?php foreach ($projects as $project): ?>
                                            <option value="<?= $project['id_Project'] ?>" <?= isset($_POST['project_id']) && $_POST['project_id'] == $project['id_Project'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($project['Project_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contract_id" class="form-label fw-bold mb-2">اختر عقد</label>
                                    <select class="form-select select2-search" id="contract_id" name="contract_id" required <?= empty($_POST['project_id']) ? 'disabled' : '' ?>>
                                        <option value="">اختر العقد</option>
                                        <?php if (isset($_POST['project_id'])): ?>
                                            <?php foreach ($contracts as $contract): ?>
                                                <option value="<?= $contract['id'] ?>" <?= isset($_POST['contract_id']) && $_POST['contract_id'] == $contract['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($contract['text']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-primary px-4">عرض البيانات</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($selectedContract): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">تفاصيل العقد الرئيسي</h5>
                        <?php
                        $todoListData = json_decode($selectedContract['data_todo_list_contract'], true);
                        ?>
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">رقم العقد</label>
                                    <span class="detail-value">: <?= htmlspecialchars($selectedContract['id_contract'] ?? '-') ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">اسم صاحب العقد</label>
                                    <span class="detail-value">: <?= htmlspecialchars($selectedContract['name_ar_contract']) ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">الاسم الوظيفي</label>
                                    <span class="detail-value">: <?= htmlspecialchars($selectedContract['name_Job']) ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">تاريخ الإصدار</label>
                                    <span class="detail-value">: <?= date('Y-m-d', strtotime($selectedContract['version_date'])) ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">تاريخ بدء العقد</label>
                                    <span class="detail-value">: <?= date('Y-m-d', strtotime($selectedContract['version_date'])) ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">تاريخ انتهاء العقد</label>
                                    <span class="detail-value">: <?= $selectedContract['end_date_contract'] ? date('Y-m-d', strtotime($selectedContract['end_date_contract'])) : '-' ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">نوع العقد</label>
                                    <span class="detail-value">: <?= isset($todoListData['evaluation']['percentageEvaluation']) && $todoListData['evaluation']['percentageEvaluation'] === 'yes' ? 'أجر شهري' : 'أجر يومي' ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-item">
                                    <label class="detail-label">عدد التمديدات</label>
                                    <span class="detail-value">: <?= htmlspecialchars($selectedContract['extension']) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-container">
                    <h6 class="section-title">إنشاء تمديد للعقد</h6>
                    <?php
                    $hasOpenEndedExtension = false;
                    if (!empty($extensions)) {
                        foreach ($extensions as $extension) {
                            if ($extension['end_date_contract'] === null) {
                                $hasOpenEndedExtension = true;
                                break;
                            }
                        }
                    }
                    
                    if ($selectedContract['end_date_contract'] === null): ?>
                        <div class="alert alert-warning" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            لا يمكن إنشاء تمديد لهذا العقد لأنه عقد مفتوح الأجل
                        </div>
                    <?php elseif ($hasOpenEndedExtension): ?>
                        <div class="alert alert-warning" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            لا يمكن إنشاء تمديد لهذا العقد لأنه يمتلك عقد ممدد مفتوح الاجل
                        </div>
                    <?php else: ?>
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="contract_id" value="<?= $selectedContract['id_contract'] ?>">
                            <input type="hidden" name="create_extension" value="1">
                            
                            <div class="row g-4">
                                <div class="col-md-4">
                                    <label for="version_date" class="form-label">تاريخ إصدار تمديد العقد</label>
                                    <input type="date" class="form-control" id="version_date" name="version_date" 
                                           required value="<?= date('Y-m-d') ?>">
                                    <div class="invalid-feedback">يرجى تحديد تاريخ الإصدار</div>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="start_date" class="form-label">تاريخ البدء</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           required min="<?= $minStartDate ?>">
                                    <div class="invalid-feedback">يجب أن يكون التاريخ بعد <?= $minStartDate ?></div>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="end_date" class="form-label">تاريخ الانتهاء (اختياري)</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date"
                                           min="<?= $minStartDate ?>">
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    إنشاء التمديد
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>

                <div class="section-container">
                    <h6 class="section-title">قائمة العقود الممددة</h6>
                    <?php if (!empty($extensions)): ?>
                        <div class="extension-list">
                            <?php foreach ($extensions as $extension): ?>
                                <div class="extension-item">
                                    <div class="extension-info">
                                        <div>
                                            <span class="detail-label">رقم التمديد:</span>
                                            <span class="detail-value"><?= isset($extension['id_extension_contract']) ? htmlspecialchars($extension['id_extension_contract']) : 'N/A' ?></span>
                                        </div>
                                        <div>
                                            <span class="detail-label">تاريخ الإصدار:</span>
                                            <span class="detail-value"><?= isset($extension['version_date']) ? date('Y-m-d', strtotime($extension['version_date'])) : 'N/A' ?></span>
                                        </div>
                                        <div>
                                            <span class="detail-label">تاريخ البدء:</span>
                                            <span class="detail-value"><?= isset($extension['start_date_contract']) ? date('Y-m-d', strtotime($extension['start_date_contract'])) : 'N/A' ?></span>
                                        </div>
                                        <div>
                                            <span class="detail-label">تاريخ الانتهاء:</span>
                                            <span class="detail-value"><?= !empty($extension['end_date_contract']) ? date('Y-m-d', strtotime($extension['end_date_contract'])) : 'مفتوح الأجل' ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-extensions">
                            <i class="bi bi-info-circle" style="font-size: 2rem;"></i>
                            <p class="mt-2">لا توجد عقود ممددة لهذا العقد.</p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script>
        $(document).ready(function() {
            $('#project_id').select2({
                placeholder: 'اختر المشروع',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            $('#contract_id').select2({
                placeholder: 'اختر العقد',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            $('#project_id').on('change', function() {
                const projectId = $(this).val();
                const contractSelect = $('#contract_id');
                
                if (projectId) {
                    contractSelect.prop('disabled', false);
                    
                    $.ajax({
                        url: 'create_extension_contract_con.php?action=get_contracts&project_id=' + projectId,
                        type: 'GET',
                        success: function(data) {
                            if (data.error) {
                                console.error('Server error:', data);
                                alert('خطأ في الخادم: ' + data.error);
                                return;
                            }
                            
                            contractSelect.empty().append('<option value="">اختر العقد</option>');
                            if (Array.isArray(data) && data.length > 0) {
                                data.forEach(function(contract) {
                                    contractSelect.append(
                                        $('<option></option>').val(contract.id).text(contract.text)
                                    );
                                });
                            } else {
                                console.log('No contracts found for project:', projectId);
                                contractSelect.append('<option value="" disabled>لا توجد عقود لهذا المشروع</option>');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX Error:', {
                                status: status,
                                error: error,
                                response: xhr.responseText
                            });
                            
                            let errorMessage = 'حدث خطأ أثناء جلب العقود: ';
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMessage += response.error || error;
                            } catch (e) {
                                errorMessage += error;
                            }
                            
                            alert(errorMessage);
                        }
                    });
                } else {
                    contractSelect.prop('disabled', true).empty().append('<option value="">اختر العقد</option>');
                }
            });

            setTimeout(function() {
                $('.custom-alert').addClass('fade-out');
                setTimeout(() => $('.custom-alert').remove(), 500);
            }, 5000);
        });
    </script>
</body>
</html>

