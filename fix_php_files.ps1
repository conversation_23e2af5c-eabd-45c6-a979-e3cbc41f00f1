# Get all PHP files in the Pages directory and its subdirectories
$phpFiles = Get-ChildItem -Path "www\Pages" -Filter "*.php" -Recurse

foreach ($file in $phpFiles) {
    Write-Host "Processing $($file.FullName)"
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    
    # Check if the file starts with <?php
    if ($content -notmatch '^\s*<\?php') {
        Write-Host "  Adding PHP opening tag"
        $content = "<?php`n" + $content
    }
    
    # Check if session_start() is present and not at the beginning
    if ($content -match 'session_start\(\)' -and $content -notmatch '^\s*<\?php\s*(?:\/\*[\s\S]*?\*\/|\/{2}[^\n]*\n)*\s*session_start\(\)') {
        Write-Host "  Moving session_start() to the beginning"
        # Remove existing session_start()
        $content = $content -replace 'session_start\(\);\s*', ''
        # Add session_start() after PHP opening tag
        $content = $content -replace '(<\?php\s*(?:\/\*[\s\S]*?\*\/|\/{2}[^\n]*\n)*)', "`$1session_start();`n"
    }
    
    # Save the file with UTF-8 encoding without BOM
    $utf8NoBomEncoding = New-Object System.Text.UTF8Encoding $false
    [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBomEncoding)
}

Write-Host "All PHP files have been fixed!" 