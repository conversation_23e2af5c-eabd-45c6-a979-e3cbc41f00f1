<?php
session_start();
header('Content-Type: application/json');

// Error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$response = ['success' => false, 'message' => ''];

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get database connection
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) throw new Exception('Error reading configuration file');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Validate required fields
    $requiredFields = ['id_Project', 'id_contract', 'id_permanent_diapers', 
                      'start_date_achievement_reports', 'end_date_achievement_reports', 
                      'data_todo_list_achievement'];
    
    foreach ($requiredFields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }

    // Prepare the SQL statement
    $sql = "INSERT INTO achievement_reports (
                id_Project, 
                id_contract, 
                id_extension_contract,
                id_permanent_diapers, 
                start_date_achievement_reports, 
                end_date_achievement_reports, 
                data_todo_list_achievement,
                actual_working_days
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    // Handle NULL value for extension_contract
    $extensionId = !empty($_POST['id_extension_contract']) ? $_POST['id_extension_contract'] : null;
    $actualWorkingDays = isset($_POST['actual_working_days']) ? $_POST['actual_working_days'] : null;

    // Bind parameters
    $stmt->bind_param(
        "iiissssi",
        $_POST['id_Project'],
        $_POST['id_contract'],
        $extensionId,
        $_POST['id_permanent_diapers'],
        $_POST['start_date_achievement_reports'],
        $_POST['end_date_achievement_reports'],
        $_POST['data_todo_list_achievement'],
        $actualWorkingDays
    );

    // Execute the statement
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    $response['success'] = true;
    $response['message'] = 'Achievement report saved successfully';

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
} finally {
    if (isset($stmt)) $stmt->close();
    if (isset($conn)) $conn->close();
}

echo json_encode($response); 
