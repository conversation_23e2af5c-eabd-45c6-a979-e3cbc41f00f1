<?php
function downloadFile($url, $path) {
    if (!file_exists(dirname($path))) {
        mkdir(dirname($path), 0777, true);
    }
    
    $ch = curl_init($url);
    $fp = fopen($path, 'w');
    
    curl_setopt($ch, CURLOPT_FILE, $fp);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $success = curl_exec($ch);
    
    curl_close($ch);
    fclose($fp);
    
    return $success;
}

// Create assets directories
$directories = [
    '../assets/css',
    '../assets/js',
    '../assets/fonts/cairo'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0777, true);
    }
}

// Download required files
$files = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' => '../assets/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js' => '../assets/js/bootstrap.bundle.min.js',
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css' => '../assets/css/bootstrap-icons.css',
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/fonts/bootstrap-icons.woff' => '../assets/fonts/bootstrap-icons.woff',
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/fonts/bootstrap-icons.woff2' => '../assets/fonts/bootstrap-icons.woff2',
    'https://fonts.gstatic.com/s/cairo/v28/SLXVc1nY6HkvangtZmpQdkhzfH5lkSscQyyS4J0.woff2' => '../assets/fonts/cairo/cairo-regular.woff2',
    'https://fonts.gstatic.com/s/cairo/v28/SLXVc1nY6HkvangtZmpQdkhzfH5lkSscSCyS4J0.woff2' => '../assets/fonts/cairo/cairo-semibold.woff2',
    'https://fonts.gstatic.com/s/cairo/v28/SLXVc1nY6HkvangtZmpQdkhzfH5lkSscRiyS4J0.woff2' => '../assets/fonts/cairo/cairo-bold.woff2'
];

foreach ($files as $url => $path) {
    echo "Downloading: " . basename($path) . "... ";
    if (downloadFile($url, $path)) {
        echo "SUCCESS\n";
    } else {
        echo "FAILED\n";
    }
}

// Update Bootstrap Icons CSS file to use local fonts
$iconsCSS = file_get_contents('../assets/css/bootstrap-icons.css');
$iconsCSS = str_replace('fonts/', '../assets/fonts/', $iconsCSS);
file_put_contents('../assets/css/bootstrap-icons.css', $iconsCSS);

echo "\nSetup complete! All necessary files have been downloaded and configured for offline use."; 