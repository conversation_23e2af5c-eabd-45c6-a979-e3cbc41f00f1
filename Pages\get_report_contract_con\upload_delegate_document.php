<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    // Check if request is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    // Check if all required parameters are provided
    if (!isset($_POST['delegate_id']) || !isset($_POST['document_type']) || !isset($_FILES['document_file'])) {
        throw new Exception('Missing required parameters');
    }
    
    $delegate_id = (int)$_POST['delegate_id'];
    $document_type = $_POST['document_type'];
    $document_file = $_FILES['document_file'];
    
    // Validate document type
    $allowed_document_types = [
        'Identity_document',
        'test_results_document',
        'Interview_results_document',
        'guarantee_document',
        'medical_examination_document',
        'cv_document',
        'request_for_resignation_document',
        'reconciliation_document',
        'resignation_approval_document',
        'code_conduct'
    ];
    
    if (!in_array($document_type, $allowed_document_types)) {
        throw new Exception('Invalid document type');
    }
    
    // Validate file
    if ($document_file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $document_file['error']);
    }
    
    // Check file size (limit to 5MB)
    if ($document_file['size'] > 5 * 1024 * 1024) {
        throw new Exception('File size exceeds the limit (5MB)');
    }
    
    // Check file type (allow PDF only)
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mime_type = $finfo->file($document_file['tmp_name']);
    
    if ($mime_type !== 'application/pdf') {
        throw new Exception('Only PDF files are allowed');
    }
    
    // Read file content
    $document_data = file_get_contents($document_file['tmp_name']);
    
    if ($document_data === false) {
        throw new Exception('Failed to read file content');
    }
    
    // Read database connection details
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);
    
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");
    
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    
    // Update the database
    $sql = "UPDATE assigned SET {$document_type} = ? WHERE id_assigned = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Query preparation failed: " . $conn->error);
    }
    
    $null = NULL;
    $stmt->bind_param("bi", $null, $delegate_id);
    $stmt->send_long_data(0, $document_data);
    
    if (!$stmt->execute()) {
        throw new Exception("Query execution failed: " . $stmt->error);
    }
    
    $stmt->close();
    $conn->close();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Document uploaded successfully'
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 