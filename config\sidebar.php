<?php
$sidebarConfig = json_decode(file_get_contents(__DIR__ . '/sidebar.json'), true);

// Get current page URL for active state
$currentPage = strtolower($_SERVER['PHP_SELF']);

function isActiveMenuItem($url) {
    global $currentPage;
    return strtolower($url) === $currentPage ? 'active' : '';
}

function renderMenuItem($item) {
    $icon = isset($item['icon']) ? "<i class=\"{$item['icon']}\"></i>" : '';
    $url = isset($item['url']) ? $item['url'] : '#';
    $active = isActiveMenuItem($url);
    
    if (isset($item['type']) && $item['type'] === 'dropdown') {
        $dropdownId = isset($item['id']) ? "dropdown-{$item['id']}" : uniqid('dropdown-');
        $html = "<li class=\"nav-item dropdown\">";
        $html .= "<a class=\"nav-link dropdown-toggle {$active}\" href=\"#\" id=\"{$dropdownId}\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">";
        $html .= "{$icon} <span>{$item['title']}</span>";
        $html .= "</a>";
        $html .= "<ul class=\"dropdown-menu\" aria-labelledby=\"{$dropdownId}\">";
        
        foreach ($item['items'] as $subItem) {
            $subIcon = isset($subItem['icon']) ? "<i class=\"{$subItem['icon']}\"></i>" : '';
            $subUrl = isset($subItem['url']) ? $subItem['url'] : '#';
            $subActive = isActiveMenuItem($subUrl);
            
            $html .= "<li><a class=\"dropdown-item {$subActive}\" href=\"{$subUrl}\">{$subIcon} {$subItem['title']}</a></li>";
        }
        
        $html .= "</ul></li>";
        return $html;
    } else {
        return "<li class=\"nav-item\">
                <a class=\"nav-link {$active}\" href=\"{$url}\">
                    {$icon} <span>{$item['title']}</span>
                </a>
            </li>";
    }
}
?>

<!-- Sidebar -->
<nav id="sidebar" class="bg-light">
    <div class="sidebar-header">
        <a href="<?php echo $sidebarConfig['logo_url']; ?>" class="text-decoration-none">
            <h3 class="text-primary mb-0"><?php echo $sidebarConfig['logo']; ?></h3>
        </a>
    </div>
    
    <button id="toggle-sidebar" class="btn btn-primary d-lg-none position-absolute" style="left: -40px; top: 10px;">
        <i class="bi bi-list"></i>
    </button>

    <ul class="nav flex-column">
        <?php foreach ($sidebarConfig['menu_items'] as $menuItem): ?>
            <?php echo renderMenuItem($menuItem); ?>
        <?php endforeach; ?>
    </ul>
</nav> 