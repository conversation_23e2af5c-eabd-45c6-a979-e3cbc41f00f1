<?php
session_start();
$currentPage = '/pages/create_achievement_reports.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

$projects = [];
$contracts = [];
$selectedItem = null;
$attendanceRecords = [];
$selectedAttendance = null;
$achievementReports = [];
$error_message = '';
$success_message = '';

if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) throw new Exception('خطأ في قراءة ملف الإعدادات');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    function fetchAchievementReports($conn, $projectId, $contractId, $extensionId = null, $permanentDiapersId = null) {
        $query = "
            SELECT 
                ar.*,
                p.Project_name,
                c.name_ar_contract,
                pd.start_date_permanent_diapers,
                pd.end_date_permanent_diapers,
                pd.data as attendance_data,
                ec.extension_number
            FROM achievement_reports ar
            JOIN project p ON ar.id_Project = p.id_Project
            JOIN contract c ON ar.id_contract = c.id_contract
            JOIN permanent_diapers pd ON ar.id_permanent_diapers = pd.id_permanent_diapers
            LEFT JOIN extension_contract ec ON ar.id_extension_contract = ec.id_extension_contract
            WHERE ar.id_Project = ?
            AND ar.id_contract = ?
            AND c.status_contract != 0";
        
        $params = [$projectId, $contractId];
        $types = "ii";
        
        // Modified logic for extension contract handling
        if ($extensionId !== null && $extensionId !== '') {
            // If extension ID is provided, get records matching that extension
            $query .= " AND ar.id_extension_contract = ?";
            $params[] = $extensionId;
            $types .= "i";
        } else {
            // If no extension ID, only get records with NULL extension
            $query .= " AND ar.id_extension_contract IS NULL";
        }
        
        if ($permanentDiapersId) {
            $query .= " AND ar.id_permanent_diapers = ?";
            $params[] = $permanentDiapersId;
            $types .= "i";
        }
        
        $query .= " ORDER BY ar.start_date_achievement_reports DESC";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $reports = [];
        while ($row = $result->fetch_assoc()) {
            $reports[] = [
                'id' => $row['id_achievement_reports'],
                'project_name' => $row['Project_name'],
                'contract_name' => $row['name_ar_contract'],
                'start_date' => $row['start_date_achievement_reports'],
                'end_date' => $row['end_date_achievement_reports'],
                'actual_working_days' => $row['actual_working_days'],
                'todo_list' => json_decode($row['data_todo_list_achievement'], true),
                'attendance_period' => [
                    'start' => $row['start_date_permanent_diapers'],
                    'end' => $row['end_date_permanent_diapers']
                ],
                'attendance_data' => json_decode($row['attendance_data'], true),
                'extension_number' => $row['extension_number']
            ];
        }
        
        return $reports;
    }

    // Add this new function after the fetchAchievementReports function
    function getTodoListData($conn, $projectId, $contractId, $extensionId = null) {
        // First check if there are any achievement reports for this contract
        $query = "SELECT ar.data_todo_list_achievement, ar.add_achievement_reports 
                  FROM achievement_reports ar 
                  WHERE ar.id_Project = ? 
                  AND ar.id_contract = ?";
        
        $params = [$projectId, $contractId];
        $types = "ii";
        
        if ($extensionId !== null && $extensionId !== '') {
            $query .= " AND ar.id_extension_contract = ?";
            $params[] = $extensionId;
            $types .= "i";
        } else {
            $query .= " AND ar.id_extension_contract IS NULL";
        }
        
        // Order by add_achievement_reports DESC to get the latest report first
        $query .= " ORDER BY ar.add_achievement_reports DESC LIMIT 1";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            // If achievement reports exist, return the todo list from the latest report
            $todoList = json_decode($row['data_todo_list_achievement'], true);
            // Reset completion rate and total for all tasks to 0
            if (isset($todoList['jobDetails']['tasks'])) {
                foreach ($todoList['jobDetails']['tasks'] as &$task) {
                    $task['completionRate'] = 0;
                    $task['total'] = 0;
                }
            }
            return $todoList;
        } else {
            // If no achievement reports exist, get the todo list from the contract table
            $contractQuery = "SELECT data_todo_list_contract 
                             FROM contract 
                             WHERE id_Project = ? AND id_contract = ?";
            
            $stmt = $conn->prepare($contractQuery);
            $stmt->bind_param("ii", $projectId, $contractId);
            $stmt->execute();
            $contractResult = $stmt->get_result();
            
            if ($contractRow = $contractResult->fetch_assoc()) {
                return json_decode($contractRow['data_todo_list_contract'], true);
            }
        }
        
        return null;
    }

    if (isset($_GET['action']) && $_GET['action'] === 'get_contracts' && isset($_GET['project_id'])) {
        header('Content-Type: application/json');
        $projectId = intval($_GET['project_id']);
        
        // Fetch main contracts and their extensions
        $stmt = $conn->prepare("
            SELECT 
                c.id_contract AS main_id,
                e.name_ar_contract AS main_name,
                c.name_Job,
                c.contract_type,
                c.wage_contract,
                c.version_date AS main_version_date,
                ec.id_extension_contract AS extension_id,
                ec.version_date AS extension_version_date,
                ec.start_date_contract AS extension_start_date,
                ec.end_date_contract AS extension_end_date
            FROM contract c
            INNER JOIN employees e ON c.id_employees = e.id_employees
            LEFT JOIN extension_contract ec ON c.id_contract = ec.id_contract
            WHERE c.id_Project = ? AND c.status_contract != 0
            ORDER BY c.id_contract, ec.add_extension_contract DESC
        ");
        $stmt->bind_param("i", $projectId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $groupedContracts = [];
        while ($row = $result->fetch_assoc()) {
            $mainId = $row['main_id'];
            if (!isset($groupedContracts[$mainId])) {
                $groupedContracts[$mainId] = [
                    'main' => [
                        'id' => $row['main_id'],
                        'name' => $row['main_name'],
                        'job' => $row['name_Job'],
                        'contract_type' => $row['contract_type'],
                        'wage_contract' => $row['wage_contract'],
                        'version_date' => $row['main_version_date'],
                    ],
                    'extensions' => []
                ];
            }
            if ($row['extension_id']) {
                $groupedContracts[$mainId]['extensions'][] = [
                    'id' => $row['extension_id'],
                    'version_date' => $row['extension_version_date'],
                    'start_date' => $row['extension_start_date'],
                    'end_date' => $row['extension_end_date'],
                ];
            }
        }

        $response = [];
        foreach ($groupedContracts as $group) {
            $main = $group['main'];
            $response[] = [
                'type' => 'main',
                'id' => 'contract_' . $main['id'],
                'text' => sprintf('رقم العقد %d - %s - %s', 
                    $main['id'],
                    $main['name'],
                    date('d-m-Y', strtotime($main['version_date']))
                ),
                'extensions' => array_map(function($ext) {
                    return [
                        'type' => 'extension',
                        'id' => 'extension_' . $ext['id'],
                        'text' => sprintf('رقم تمديد العقد %d - %s إلى %s', 
                            $ext['id'],
                            date('d-m-Y', strtotime($ext['start_date'])),
                            $ext['end_date'] ? date('d-m-Y', strtotime($ext['end_date'])) : 'مفتوح'
                        )
                    ];
                }, $group['extensions'])
            ];
        }
        echo json_encode($response);
        exit;
    }

    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    while ($row = $result->fetch_assoc()) {
        $projects[] = $row;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['contract_id'])) {
            $selected = $_POST['contract_id'];
            if (strpos($selected, 'contract_') === 0) {
                $contractId = substr($selected, 9);
                $stmt = $conn->prepare("
                    SELECT c.id_contract, c.id_employees, c.name_Job, c.contract_type, c.wage_contract, 
                           c.version_date, c.start_date_contract, c.end_date_contract, c.data_todo_list_contract,
                           e.name_ar_contract 
                    FROM contract c
                    LEFT JOIN employees e ON c.id_employees = e.id_employees 
                    WHERE c.id_contract = ?
                ");
                $stmt->bind_param("i", $contractId);
                $stmt->execute();
                $selectedItem = $stmt->get_result()->fetch_assoc();
                $selectedItem['type'] = 'contract';
                
                // Fetch attendance records
                $stmt = $conn->prepare("
                    SELECT pd.*, ar.id_achievement_reports, 
                           ar.start_date_achievement_reports, 
                           ar.end_date_achievement_reports 
                    FROM permanent_diapers pd
                    LEFT JOIN achievement_reports ar ON pd.id_permanent_diapers = ar.id_permanent_diapers
                    WHERE pd.id_contract = ?
                    AND pd.id_extension_contract IS NULL
                ");
                $stmt->bind_param("i", $contractId);
                $stmt->execute();
                $result = $stmt->get_result();
                
                while ($row = $result->fetch_assoc()) {
                    $attendanceRecords[$row['id_permanent_diapers']]['info'] = $row;
                    if ($row['id_achievement_reports']) {
                        $attendanceRecords[$row['id_permanent_diapers']]['reports'][] = [
                            'id' => $row['id_achievement_reports'],
                            'start_date_achievement_reports' => $row['start_date_achievement_reports'],
                            'end_date_achievement_reports' => $row['end_date_achievement_reports']
                        ];
                    }
                }
                
            } elseif (strpos($selected, 'extension_') === 0) {
                $extensionId = substr($selected, 10);
                $stmt = $conn->prepare("SELECT * FROM extension_contract WHERE id_extension_contract = ?");
                $stmt->bind_param("i", $extensionId);
                $stmt->execute();
                $selectedItem = $stmt->get_result()->fetch_assoc();
                $selectedItem['type'] = 'extension';
                
                // Update this query to include employee name
                $stmt = $conn->prepare("
                    SELECT c.id_contract, c.id_employees, c.name_Job, c.contract_type, c.wage_contract, 
                           c.version_date, c.start_date_contract, c.end_date_contract, c.data_todo_list_contract,
                           e.name_ar_contract 
                    FROM contract c
                    LEFT JOIN employees e ON c.id_employees = e.id_employees 
                    WHERE c.id_contract = ?
                ");
                $stmt->bind_param("i", $selectedItem['id_contract']);
                $stmt->execute();
                $selectedItem['main_contract'] = $stmt->get_result()->fetch_assoc();
                
                // Fetch attendance records for extension
                $stmt = $conn->prepare("
                    SELECT pd.*, ar.id_achievement_reports, 
                           ar.start_date_achievement_reports, 
                           ar.end_date_achievement_reports 
                    FROM permanent_diapers pd
                    LEFT JOIN achievement_reports ar ON pd.id_permanent_diapers = ar.id_permanent_diapers
                    WHERE pd.id_extension_contract = ?
                ");
                $stmt->bind_param("i", $extensionId);
                $stmt->execute();
                $result = $stmt->get_result();
                
                while ($row = $result->fetch_assoc()) {
                    $attendanceRecords[$row['id_permanent_diapers']]['info'] = $row;
                    if ($row['id_achievement_reports']) {
                        $attendanceRecords[$row['id_permanent_diapers']]['reports'][] = [
                            'id' => $row['id_achievement_reports'],
                            'start_date_achievement_reports' => $row['start_date_achievement_reports'],
                            'end_date_achievement_reports' => $row['end_date_achievement_reports']
                        ];
                    }
                }
            }
        }
        
        // Handle attendance record selection
        if (isset($_POST['attendance_id'])) {
            $attendanceId = $_POST['attendance_id'];
            $stmt = $conn->prepare("
                SELECT pd.*, ar.* 
                FROM permanent_diapers pd
                LEFT JOIN achievement_reports ar ON pd.id_permanent_diapers = ar.id_permanent_diapers
                WHERE pd.id_permanent_diapers = ?
            ");
            $stmt->bind_param("i", $attendanceId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $selectedAttendance = $result->fetch_assoc();
            $selectedAttendance['data'] = json_decode($selectedAttendance['data'], true);
            
            // Fetch all achievement reports for this attendance record
            $stmt = $conn->prepare("
                SELECT * FROM achievement_reports 
                WHERE id_permanent_diapers = ?
            ");
            $stmt->bind_param("i", $attendanceId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $achievementReports[] = $row;
            }
        }
        
        if (isset($_POST['contract_id']) && isset($_POST['permanent_diapers_id'])) {
            $selected = $_POST['contract_id'];
            $permanentDiapersId = intval($_POST['permanent_diapers_id']);
            
            $contractId = null;
            $extensionId = null;
            
            if (strpos($selected, 'contract_') === 0) {
                $contractId = intval(substr($selected, 9));
            } elseif (strpos($selected, 'extension_') === 0) {
                $extensionId = intval(substr($selected, 10));
                
                // Get parent contract ID for the extension
                $stmt = $conn->prepare("SELECT id_contract FROM extension_contract WHERE id_extension_contract = ?");
                $stmt->bind_param("i", $extensionId);
                $stmt->execute();
                $result = $stmt->get_result();
                if ($row = $result->fetch_assoc()) {
                    $contractId = $row['id_contract'];
                }
            }
            
            if ($contractId) {
                $achievementReports = fetchAchievementReports(
                    $conn, 
                    $_POST['project_id'], 
                    $contractId, 
                    $extensionId, 
                    $permanentDiapersId
                );
            }
        }
    }

} catch (Exception $e) {
    $error_message = "خطأ في النظام: " . $e->getMessage();
} finally {
    if (isset($conn)) $conn->close();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء تقرير الإنجاز - نظام إدارة الموارد البشرية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Detail item styles */
        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            white-space: nowrap;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* Row spacing */
        .row.g-3 {
            --bs-gutter-y: 1rem;
        }

        /* Flex container for details */
        .d-flex.align-items-center {
            min-height: 40px;
            padding: 0.5rem 0;
        }

        /* Remove borders and backgrounds */
        .card-body .row.g-3 > div {
            background: transparent;
            border: none;
        }

        /* Ensure proper spacing between label and value */
        .detail-value.ms-2 {
            margin-right: 0.25rem;
        }
    </style>

    <style>
        /* Enhanced Table Styles for Attendance Details */
        #section-four .table {
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        #section-four .table th,
        #section-four .table td {
            text-align: center;
            vertical-align: middle;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
        }

        #section-four .table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            white-space: nowrap;
            border-top: 1px solid var(--border-color);
            border-bottom: 2px solid var(--border-color);
        }

        #section-four .table tbody tr:nth-child(even) {
            background-color: var(--bg-light);
        }

        #section-four .table tbody tr:hover {
            background-color: var(--bg-hover);
        }

        #section-four .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        #section-four .table .comments-cell {
            max-width: 200px;
            white-space: normal;
            word-break: break-word;
        }

        /* Fixed column widths */
        #section-four .table th[rowspan="3"],
        #section-four .table td:nth-child(1),
        #section-four .table td:nth-child(2) {
            width: 100px;
        }

        #section-four .table td:not(:first-child):not(:nth-child(2)):not(:last-child) {
            width: 120px;
        }

        #section-four .table td:last-child {
            width: 150px;
        }

        /* Responsive table */
        @media (max-width: 1200px) {
            #section-four .table-responsive {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            #section-four .table {
                min-width: 1000px;
            }
        }
        
        /* Dark theme specific styles */
        [data-theme="dark"] #section-four .table {
            border-color: var(--border-color);
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #section-four .table th,
        [data-theme="dark"] #section-four .table td {
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #section-four .table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #section-four .table tbody tr {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #section-four .table tbody tr:nth-child(even) {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] #section-four .table tbody tr:hover {
            background-color: var(--bg-hover);
        }

        /* Input styles for dark theme */
        [data-theme="dark"] #section-four .table input.form-control {
            background-color: var(--bg-primary) !important;
            color: var(--text-primary) !important;
            border-color: var(--border-color) !important;
        }

        [data-theme="dark"] #section-four .table input.form-control:focus {
            background-color: var(--bg-secondary) !important;
            border-color: var(--primary-color) !important;
            color: var(--text-primary) !important;
            box-shadow: none !important;
        }

        /* Status badge styles */
        [data-theme="dark"] #section-four .status-badge {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        /* Enhanced Table Styles for Attendance Details */
        #section-four .attendance-details-container {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            margin-bottom: 1rem;
        }

        #section-four .table-responsive {
            max-height: none;
            overflow-x: auto;
            border: none;
            margin-bottom: 0;
        }

        #section-four .table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: var(--bg-secondary);
            border-bottom: 2px solid var(--border-color);
        }

        #section-four .table th,
        #section-four .table td {
            text-align: center;
            vertical-align: middle;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
        }

        #section-four .table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            white-space: nowrap;
        }

        #section-four .table tbody tr:nth-child(even) {
            background-color: var(--bg-light);
        }

        #section-four .table tbody tr:hover {
            background-color: var(--bg-hover);
        }

        /* Scrollbar styling */
        #section-four .attendance-details-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        #section-four .attendance-details-container::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        #section-four .attendance-details-container::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 4px;
        }

        #section-four .attendance-details-container::-webkit-scrollbar-thumb:hover {
            background-color: var(--text-muted);
        }

        /* Firefox scrollbar */
        #section-four .attendance-details-container {
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) var(--bg-secondary);
        }

        /* Dark theme adjustments */
        [data-theme="dark"] #section-four .table thead th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #section-four .table tbody tr:nth-child(even) {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] #section-four .table tbody tr:hover {
            background-color: var(--bg-hover);
        }
    </style>
    <style>
        /* Enhanced Table Styles for Attendance Details */
        #section-four .table {
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
        }
        
        #section-four .table th {
            background-color: var(--header-bg);
            color: var(--header-text);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem;
        }
        
        #section-four .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
        }
        
        #section-four .table tbody tr:last-child td {
            border-bottom: none;
        }
        
        #section-four .table tbody tr:hover td {
            background-color: var(--hover-bg);
        }
    </style>
    <style>
        .report-details-row {
            background-color: var(--bg-light);
        }
        
        .report-details {
            border-radius: 8px;
            background-color: var(--bg-input);
            margin: 0.5rem;
        }
        
        .todo-list {
            list-style: none;
            padding-right: 0;
        }
        
        .todo-list li {
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background-color: var(--bg-light);
            border-radius: 4px;
        }
        
        .todo-list li strong {
            color: var(--text-primary);
            margin-left: 0.5rem;
        }
    </style>

<style>
        /* Retention Period Calendar */
        .retention-period-container {
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            gap: 2rem;
        }

        .calendar-section {
            flex-shrink: 0;
        }

        .date-fields-section {
            padding-top: 1rem;
            width: 250px; /* Limit the width of the fields */
        }

        .calendar-wrapper {
            --calendar-day-size: 40px; /* Increased size */
            width: calc(var(--calendar-day-size) * 7 + 16px);
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            user-select: none;
        }

        .calendar-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            padding: 4px;
            background-color: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
        }

        .calendar-day-header {
            width: var(--calendar-day-size);
            height: 24px;
            font-size: 0.8rem;
            text-align: center;
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .calendar-container {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            padding: 4px;
        }

        .calendar-day {
            width: var(--calendar-day-size);
            height: var(--calendar-day-size);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s;
            color: var(--text-primary);
            border: 1px solid transparent;
            user-select: none;
            position: relative;
        }

        .calendar-day:not(.out-of-period):hover {
            background-color: var(--hover-color);
            border-color: var(--primary-color);
            transform: scale(1.05);
            z-index: 1;
        }

        .calendar-day.selected {
            background-color: var(--primary-color);
            color: var(--text-light);
            font-weight: 600;
            transform: scale(1.05);
            z-index: 2;
        }

        .calendar-day.in-range {
            background-color: var(--primary-light);
            color: var(--text-primary);
            position: relative;
        }

        .calendar-day.in-range::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--primary-color);
            opacity: 0.2;
            border-radius: 4px;
            transition: opacity 0.2s;
        }

        .calendar-day.in-range:hover::before {
            opacity: 0.3;
        }

        .calendar-day.out-of-period {
            color: var(--text-muted);
            cursor: not-allowed;
            background-color: var(--bg-disabled);
            opacity: 0.5;
        }

        .calendar-day.disabled {
            color: var(--text-disabled);
            background-color: var(--bg-disabled);
            border: 1px solid var(--border-disabled);
            cursor: not-allowed;
            position: relative;
            opacity: 1;
        }

        .calendar-day.disabled::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 4px,
                rgba(160, 160, 160, 0.1) 4px,
                rgba(160, 160, 160, 0.1) 8px
            );
            pointer-events: none;
        }

        /* Special case for disabled days in dark theme */
        [data-theme="dark"] .calendar-day.disabled::after {
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 4px,
                rgba(0, 0, 0, 0.2) 4px,
                rgba(0, 0, 0, 0.2) 8px
            );
        }

        .calendar-day.disabled:hover {
            background-color: var(--bg-disabled);
            color: var(--text-disabled);
            transform: none;
        }

        .calendar-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background-color: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
        }

        .current-month {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            text-align: center;
            min-width: 120px;
        }

        .retention-date-input {
            background-color: var(--bg-input);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            border-radius: 6px;
            width: 100%;
            cursor: default;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .retention-date-input[readonly] {
            background-color: var(--bg-input);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
        }

        .retention-date-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        [data-theme="dark"] .calendar-wrapper {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .btn-outline-secondary {
            padding: 0.375rem 0.75rem;
            font-size: 0.9rem;
        }

        .btn-outline-secondary:hover {
            background-color: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
    </style>


<style>
        /* Retention Period Calendar */
        .retention-period-container {
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            gap: 2rem;
        }

        .calendar-section {
            flex-shrink: 0;
        }

        .date-fields-section {
            padding-top: 1rem;
            width: 250px; /* Limit the width of the fields */
        }

        .calendar-wrapper {
            --calendar-day-size: 40px; /* Increased size */
            width: calc(var(--calendar-day-size) * 7 + 16px);
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            user-select: none;
        }

        .calendar-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            padding: 4px;
            background-color: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
        }

        .calendar-day-header {
            width: var(--calendar-day-size);
            height: 24px;
            font-size: 0.8rem;
            text-align: center;
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .calendar-container {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            padding: 4px;
        }

        .calendar-day {
            width: var(--calendar-day-size);
            height: var(--calendar-day-size);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s;
            color: var(--text-primary);
            border: 1px solid transparent;
            user-select: none;
            position: relative;
        }

        .calendar-day:not(.out-of-period):hover {
            background-color: var(--hover-color);
            border-color: var(--primary-color);
            transform: scale(1.05);
            z-index: 1;
        }

        .calendar-day.selected {
            background-color: var(--primary-color);
            color: var(--text-light);
            font-weight: 600;
            transform: scale(1.05);
            z-index: 2;
        }

        .calendar-day.in-range {
            background-color: var(--primary-light);
            color: var(--text-primary);
            position: relative;
        }

        .calendar-day.in-range::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--primary-color);
            opacity: 0.2;
            border-radius: 4px;
            transition: opacity 0.2s;
        }

        .calendar-day.in-range:hover::before {
            opacity: 0.3;
        }

        .calendar-day.out-of-period {
            color: var(--text-muted);
            cursor: not-allowed;
            background-color: var(--bg-disabled);
            opacity: 0.5;
        }

        .calendar-day.disabled {
            color: var(--text-disabled);
            background-color: var(--bg-disabled);
            border: 1px solid var(--border-disabled);
            cursor: not-allowed;
            position: relative;
            opacity: 1;
        }

        .calendar-day.disabled::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 4px,
                rgba(160, 160, 160, 0.1) 4px,
                rgba(160, 160, 160, 0.1) 8px
            );
            pointer-events: none;
        }

        /* Special case for disabled days in dark theme */
        [data-theme="dark"] .calendar-day.disabled::after {
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 4px,
                rgba(0, 0, 0, 0.2) 4px,
                rgba(0, 0, 0, 0.2) 8px
            );
        }

        .calendar-day.disabled:hover {
            background-color: var(--bg-disabled);
            color: var(--text-disabled);
            transform: none;
        }

        .calendar-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background-color: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
        }

        .current-month {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            text-align: center;
            min-width: 120px;
        }

        .retention-date-input {
            background-color: var(--bg-input);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            border-radius: 6px;
            width: 100%;
            cursor: default;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .retention-date-input[readonly] {
            background-color: var(--bg-input);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
        }

        .retention-date-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        [data-theme="dark"] .calendar-wrapper {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .btn-outline-secondary {
            padding: 0.375rem 0.75rem;
            font-size: 0.9rem;
        }

        .btn-outline-secondary:hover {
            background-color: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
    </style>

    <!-- Add these styles after the existing styles in the head section -->

    <style>
        /* Achievement Report Table Styles */
        #achievement-tasks-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        #create-achievement-section .table-responsive {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            position: sticky;
            top: 0;
            z-index: 10;
            padding: 1rem;
            border-bottom: 2px solid var(--border-color);
            white-space: nowrap;
        }

        #achievement-tasks-table tbody td {
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        #achievement-tasks-table tbody tr:last-child td {
            border-bottom: none;
        }

        #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Column widths */
        #achievement-tasks-table td:nth-child(1) {
            min-width: 200px; /* Task name */
        }

        #achievement-tasks-table td:nth-child(2),
        #achievement-tasks-table td:nth-child(3),
        #achievement-tasks-table td:nth-child(4) {
            width: 120px; /* Number/percentage columns */
            min-width: 120px;
        }

        #achievement-tasks-table td:nth-child(5) {
            width: auto; /* Notes column - takes remaining space */
            min-width: 250px;
        }

        /* Input styles */
        #achievement-tasks-table .form-control {
            background-color: var(--bg-input);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 0.5rem;
            width: 100%;
            transition: all 0.2s ease;
        }

        #achievement-tasks-table .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-shadow);
            background-color: var(--bg-input-focus);
        }

        /* Number input specific styles */
        #achievement-tasks-table .current-progress {
            text-align: center;
            width: 80px !important;
            margin: 0 auto;
            display: block;
        }

        /* Notes input specific styles */
        #achievement-tasks-table .task-notes {
            width: 100%;
            min-width: 200px;
        }

        /* Dark mode specific adjustments */
        [data-theme="dark"] #achievement-tasks-table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody td {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Scrollbar styling */
        #create-achievement-section .table-responsive::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        #create-achievement-section .table-responsive::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        #create-achievement-section .table-responsive::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 4px;
        }

        #create-achievement-section .table-responsive::-webkit-scrollbar-thumb:hover {
            background-color: var(--text-muted);
        }

        /* Firefox scrollbar */
        #create-achievement-section .table-responsive {
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) var(--bg-secondary);
        }
    </style>

    <style>
        /* Achievement Button Styles */
        .achievement-btn-container {
            width: 100%;
            margin-top: 1rem;
        }

        .achievement-create-btn {
            width: 100%;
            height: 48px; /* Match the height of date inputs */
            font-size: 1rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--text-light);
        }

        .achievement-create-btn:hover:not(:disabled) {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .achievement-create-btn:active:not(:disabled) {
            transform: translateY(0);
            box-shadow: none;
        }

        .achievement-create-btn:disabled {
            background-color: var(--bg-disabled);
            border-color: var(--border-disabled);
            color: var(--text-disabled);
            cursor: not-allowed;
            opacity: 0.7;
        }

        /* Update date fields section to accommodate button */
        .date-fields-section {
            padding-top: 1rem;
            width: 250px;
            display: flex;
            flex-direction: column;
        }

        .retention-date-input {
            height: 48px;
            margin-bottom: 0.5rem;
        }
    </style>

    <style>
        /* Add these styles for the tooltip message */
        .input-help-text {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }

        [data-theme="dark"] .input-help-text {
            color: rgba(255, 255, 255, 0.7);
        }

        /* Warning message styles */
        .warning-message {
            position: absolute;
            font-size: 0.875rem;
            color: var(--danger-color);
            margin-top: 0.25rem;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>

    <style>
        /* Achievement Button Styles */
        #save-achievement-btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--text-light);
        }
        
        #save-achievement-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }
        
        #save-achievement-btn:active {
            transform: translateY(0);
            box-shadow: none;
        }
        
        #save-achievement-btn i {
            margin-left: 0.5rem;
        }
        
        #save-achievement-btn:disabled {
            background-color: var(--bg-disabled);
            border-color: var(--border-disabled);
            color: var(--text-disabled);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
    
    <!-- Add missing styles -->
    <style>
        .custom-alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }
        .custom-alert.error {
            background-color: var(--error-bg);
            color: var(--error-text);
            border: 1px solid var(--error-border);
        }
        .custom-alert.success {
            background-color: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-border);
        }
        .custom-alert.fade-out {
            opacity: 0;
        }
        
        /* Enhanced select field styling */
        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        .form-select.select2-search {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 0.625rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease, box-shadow 0.3s ease;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .form-select.select2-search:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.15);
            outline: none;
        }
        .form-select.select2-search:hover {
            border-color: #bdbdbd;
        }
        .form-label {
            font-size: 0.95rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        /* Contract Tree Structure */
        .contract-tree-item {
            position: relative;
            padding-right: 28px !important;
            margin: 4px 0 !important;
        }
        
        .contract-extension {
            position: relative;
            padding-right: 42px !important;
            font-size: 0.95em;
        }
        
        /* Horizontal line */
        .contract-extension::before {
            content: '';
            position: absolute;
            right: 22px;
            top: 50%;
            width: 16px;
            height: 2px;
            background-color: var(--select-text);
            opacity: 0.6;
        }
        
        /* Vertical line */
        .contract-extension::after {
            content: '';
            position: absolute;
            right: 22px;
            top: -12px;
            width: 2px;
            height: calc(100% + 12px);
            background-color: var(--select-text);
            opacity: 0.6;
        }
        
        /* Special cases for first and last extensions */
        .contract-extension.first-extension::after {
            top: 50%;
            height: calc(50% + 12px);
        }
        
        .contract-extension.last-extension::after {
            height: 50%;
        }
        
        /* Main contract styling */
        .contract-main {
            font-weight: 600;
            position: relative;
            margin-top: 8px !important;
        }
        
        /* Dot for main contract */
        .contract-main::before {
            content: '';
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--select-text);
            opacity: 0.8;
        }
        
        /* Select2 customization */
        .select2-container--default .select2-selection--single {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        
        .select2-container--default .select2-selection--single:hover {
            border-color: #bdbdbd;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 44px;
            padding-right: 1rem;
            padding-left: 1rem;
            color: #2d3748;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 46px;
            width: 30px;
        }
        
        .select2-dropdown {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .select2-search__field {
            border-radius: 8px !important;
            padding: 0.5rem !important;
        }
        
        .select2-results__option {
            padding: 6px 12px !important;
            text-align: right !important;
            color: var(--select-text) !important;
        }
        
        .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
        
        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
    </style>
    
    <!-- Add Select2 RTL and Theme Support -->
    <style>
        :root {
            --select-bg: #fff;
            --select-text: #495057;
            --select-border: #ced4da;
            --select-placeholder: #999;
            --select-hover-bg: #f8f9fa;
            --select-focus-border: #86b7fe;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
        }

        [data-theme="dark"] {
            --select-bg: #2b3035;
            --select-text: #e9ecef;
            --select-border: #495057;
            --select-placeholder: #6c757d;
            --select-hover-bg: #343a40;
            --select-focus-border: #0d6efd;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
        }

        /* Select2 RTL Fixes with Theme Support */
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px !important;
            display: flex !important;
            align-items: center !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            transition: all 0.3s ease, box-shadow 0.3s ease !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            width: 100% !important;
            padding-right: 8px !important;
            padding-left: 20px !important;
            display: block !important;
            position: static !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            color: var(--select-text) !important;
        }

        /* Placeholder color */
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
            color: var(--select-placeholder) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            position: absolute !important;
            left: 3px !important;
            right: auto !important;
        }

        .select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow {
            border-color: transparent transparent var(--select-text) transparent !important;
        }

        /* Dropdown styles */
        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--select-bg) !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            text-align: right !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            padding: 6px 12px !important;
            text-align: right !important;
            color: var(--select-text) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }

        /* Fix for extension options indentation */
        .extension-option {
            padding-right: 24px !important;
        }

        /* Focus state */
        .select2-container--bootstrap-5.select2-container--focus .select2-selection,
        .select2-container--bootstrap-5.select2-container--open .select2-selection {
            border-color: var(--select-focus-border) !important;
            box-shadow: 0 0 0 0.25rem var(--select-focus-shadow) !important;
        }

        /* Search field in dropdown */
        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            border: 1px solid var(--select-border) !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            border-radius: 0.375rem !important;
            padding: 0.375rem 0.75rem !important;
        }

        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field:focus {
            border-color: var(--select-focus-border) !important;
            outline: none !important;
        }
    </style>

    <!-- Add Attendance Records and Achievement Reports Styles -->
    <style>
        .attendance-records {
            max-height: 600px;
            overflow-y: auto;
            padding-right: 8px;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }
        
        .attendance-record-container {
            background: var(--bg-card);
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .attendance-record-main {
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--bg-light);
            border-radius: 0.75rem 0.75rem 0 0;
            border-bottom: 2px solid #0d6efd;
        }

        .record-info {
            font-size: 0.95rem;
            color: var(--text-color);
        }

        .record-id {
            font-weight: 600;
            color: var(--primary-color);
        }

        .record-separator {
            color: var(--text-muted);
            font-weight: 300;
        }

        .record-period {
            color: var(--text-color);
        }

        .reports-container {
            height: 64px; /* Fixed height for one report */
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
            padding: 0;
            margin: 0.5rem 0;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background: var(--bg-light);
            position: relative;
        }

        .reports-title {
            color: var(--text-primary);
            font-weight: 600;
            padding: 0.75rem;
            margin: 0;
            border-bottom: 2px solid #0d6efd;
        }

        .report-item {
            height: 64px; /* Fixed height for each report */
            padding: 0 1rem;
            background: transparent;
            border-radius: 0;
            margin: 0;
            border: none;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }

        .report-item:last-child {
            border-bottom: none;
        }

        .report-item:hover {
            background: var(--bg-hover);
        }

        .report-info {
            width: 100%;
        }

        .achievement-reports-list {
            padding: 1rem;
            position: relative;
        }

        .reports-title {
            color: var(--text-color);
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .reports-title::after {
            content: '(قم بالتمرير لعرض المزيد)';
            font-size: 0.8rem;
            font-weight: normal;
            color: var(--text-muted);
            margin-right: auto;
            margin-left: 0.5rem;
        }

        .no-reports-message {
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-muted);
            font-style: italic;
            text-align: center;
            padding: 0 1rem;
            background: transparent;
        }

        /* Scrollbar Styles */
        .reports-container::-webkit-scrollbar {
            width: 4px;
        }

        .reports-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .reports-container::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 2px;
        }

        /* Scroll indicator */
        .reports-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(to bottom, transparent, var(--border-color));
            opacity: 0.5;
            pointer-events: none;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .reports-container {
            background: var(--bg-secondary);
        }

        [data-theme="dark"] .report-item {
            border-color: var(--border-color);
        }

        [data-theme="dark"] .report-item:hover {
            background: var(--bg-hover);
        }

        .no-reports-message {
            color: var(--text-muted);
            font-style: italic;
            text-align: center;
            padding: 1rem;
            background: transparent;
            border-radius: 0;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .no-reports-message {
            background: var(--bg-secondary);
        }
    </style>

    <style>
        /* Add these styles after your existing styles */
        .actual-workdays-container {
            background-color: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .actual-workdays-container label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .actual-workdays-container .form-control {
            height: 48px;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: var(--bg-input);
            color: var(--text-primary);
        }

        .actual-workdays-container .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-shadow);
        }

        .actual-workdays-container .input-help-text {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .actual-workdays-container .required-asterisk {
            color: var(--danger-color);
            margin-right: 0.25rem;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .actual-workdays-container {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] .actual-workdays-container .form-control {
            background-color: var(--bg-input);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .actual-workdays-container .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
        }
    </style>

    <!-- Add this CSS block in the head section -->
    <style>
        /* Fixed height Select2 dropdowns */
        .select2-container--bootstrap-5 .select2-dropdown {
            max-height: 300px;
            overflow-y: auto;
        }

        .select2-container--bootstrap-5 .select2-results__options {
            max-height: 250px !important;
            overflow-y: auto !important;
            scrollbar-width: thin;
            scrollbar-color: var(--select-border) transparent;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar {
            width: 6px;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-track {
            background: transparent;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb {
            background-color: var(--select-border);
            border-radius: 3px;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb:hover {
            background-color: var(--select-text);
        }

        /* Ensure dropdown options are fully visible */
        .select2-container--bootstrap-5 .select2-results__option {
            white-space: normal;
            word-wrap: break-word;
        }

        /* Improve dropdown container appearance */
        .select2-container--bootstrap-5.select2-container--open .select2-dropdown {
            border-color: var(--select-focus-border);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Dark theme support for dropdowns */
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb {
            background-color: var(--dark-border-color);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb:hover {
            background-color: var(--dark-text-muted);
        }
    </style>

    <style>
        /* Alert styling */
        .alert-info {
            margin: -0.5rem; /* Align with card body padding */
            border-radius: 0 0 8px 8px; /* Round only bottom corners */
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }
    </style>

    <style>
        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: #2d303d;
        }

        /* Card styling with blue line under title */
        .card-title {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Contract details grid layout */
        .contract-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-group {
            padding: 0.75rem;
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* Button styling */
        .btn-primary {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .btn-primary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>

    <!-- Attendance Records Styling -->
    <style>
        /* Attendance Records Styling */
        .attendance-record-container {
            background: var(--bg-card);
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .attendance-record-main {
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--bg-light);
            border-radius: 0.75rem 0.75rem 0 0;
            border-bottom: 2px solid #0d6efd;
        }

        .record-info {
            font-size: 0.95rem;
            color: var(--text-color);
        }

        .record-id {
            font-weight: 600;
            color: var(--primary-color);
        }

        .reports-container {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin: 0.5rem;
        }

        .reports-title {
            color: var(--text-primary);
            font-weight: 600;
            padding: 0.75rem;
            margin: 0;
            border-bottom: 2px solid #0d6efd;
        }

        .report-item {
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .report-item:last-child {
            border-bottom: none;
        }

        .report-item:hover {
            background-color: var(--bg-hover);
        }

        /* Attendance Details Table */
        .attendance-details-container {
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .attendance-details-container .table {
            margin-bottom: 0;
        }

        .attendance-details-container .table th {
            background-color: var(--bg-secondary);
            border-bottom: 2px solid #0d6efd;
            color: var(--text-primary);
            font-weight: 600;
            text-align: center;
            padding: 1rem;
        }

        .attendance-details-container .table td {
            padding: 0.75rem;
            vertical-align: middle;
            border-color: var(--border-color);
        }

        /* Achievement Report Section */
        #create-achievement-section .table {
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            overflow: hidden;
        }

        #create-achievement-section .table th {
            background-color: var(--bg-secondary);
            border-bottom: 2px solid #0d6efd;
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
        }

        #create-achievement-section .table td {
            padding: 0.75rem;
            vertical-align: middle;
            border-color: var(--border-color);
        }

        #create-achievement-section .form-control {
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            padding: 0.625rem;
            transition: all 0.2s ease;
        }

        #create-achievement-section .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
    </style>

    <style>
        /* Achievement Report Table Styles - Matching Attendance Summary Table */
        #create-achievement-section .table-responsive {
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        #achievement-tasks-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background-color: var(--card-bg);
            margin-bottom: 0;
        }

        #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border-bottom: 2px solid var(--border-color);
            white-space: nowrap;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        #achievement-tasks-table tbody td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
        }

        #achievement-tasks-table tbody tr:last-child td {
            border-bottom: none;
        }

        #achievement-tasks-table tbody tr:nth-child(even) td {
            background-color: var(--bg-light);
        }

        #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Input styles within the table */
        #achievement-tasks-table .form-control {
            background-color: var(--bg-input);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 0.5rem;
            width: 100%;
            transition: all 0.2s ease;
        }

        #achievement-tasks-table .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-shadow);
            background-color: var(--bg-input-focus);
        }

        #achievement-tasks-table .current-progress {
            width: 100px !important;
            margin: 0 auto;
            text-align: center;
        }

        #achievement-tasks-table .task-notes {
            width: 100%;
            min-width: 200px;
        }

        /* Help text styling */
        #achievement-tasks-table .input-help-text {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
            text-align: center;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] #achievement-tasks-table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody tr:nth-child(even) td {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        [data-theme="dark"] #achievement-tasks-table .form-control {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #achievement-tasks-table .form-control:focus {
            background-color: var(--bg-secondary);
            border-color: var(--primary-color);
            color: var(--text-primary);
        }

        /* Saved data table styles */
        #create-achievement-section .table {
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            margin-bottom: 1rem;
        }

        #create-achievement-section .table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border-bottom: 2px solid var(--border-color);
        }

        #create-achievement-section .table td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
        }

        #create-achievement-section .table tbody tr:last-child td {
            border-bottom: none;
        }

        #create-achievement-section .table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Dark theme for saved data table */
        [data-theme="dark"] #create-achievement-section .table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #create-achievement-section .table th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #create-achievement-section .table td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #create-achievement-section .table tbody tr:hover td {
            background-color: var(--bg-hover);
        }
    </style>

    <style>
        /* Achievement Report Table Container */
        #create-achievement-section .table-responsive {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            position: relative;
        }

        /* Scrollbar styling for the container */
        #create-achievement-section .table-responsive::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        #create-achievement-section .table-responsive::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        #create-achievement-section .table-responsive::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 4px;
        }

        #create-achievement-section .table-responsive::-webkit-scrollbar-thumb:hover {
            background-color: var(--text-muted);
        }

        /* Firefox scrollbar */
        #create-achievement-section .table-responsive {
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) var(--bg-secondary);
        }

        /* Table Styles */
        #achievement-tasks-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background-color: var(--card-bg);
            margin-bottom: 0;
        }

        /* Header Styles */
        #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            border-bottom: 2px solid #0d6efd;
            white-space: nowrap;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* Cell Styles */
        #achievement-tasks-table tbody td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
        }

        /* Row Styles */
        #achievement-tasks-table tbody tr:nth-child(even) td {
            background-color: var(--bg-light);
        }

        #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Input Styles */
        #achievement-tasks-table .form-control {
            background-color: var(--bg-input);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 0.5rem;
            width: 100%;
            transition: all 0.2s ease;
        }

        #achievement-tasks-table .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-shadow);
            background-color: var(--bg-input-focus);
        }

        #achievement-tasks-table .current-progress {
            width: 100px !important;
            margin: 0 auto;
            text-align: center;
        }

        #achievement-tasks-table .task-notes {
            width: 100%;
            min-width: 200px;
        }

        /* Dark Theme Adjustments */
        [data-theme="dark"] #achievement-tasks-table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody tr:nth-child(even) td {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        [data-theme="dark"] #achievement-tasks-table .form-control {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #achievement-tasks-table .form-control:focus {
            background-color: var(--bg-secondary);
            border-color: var(--primary-color);
            color: var(--text-primary);
        }

        /* Fix for header border overlap */
        #achievement-tasks-table thead th:not(:last-child) {
            border-right: 1px solid var(--border-color);
        }

        /* Column width adjustments */
        #achievement-tasks-table td:first-child {
            min-width: 200px; /* Task name column */
        }

        #achievement-tasks-table td:nth-child(2),
        #achievement-tasks-table td:nth-child(3),
        #achievement-tasks-table td:nth-child(4) {
            width: 120px; /* Progress columns */
        }

        #achievement-tasks-table td:last-child {
            min-width: 250px; /* Notes column */
        }
    </style>

    <style>
        /* Data Summary Table Styles */
        #create-achievement-section .data-summary-table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            margin-bottom: 1rem;
        }

        #create-achievement-section .data-summary-table th,
        #create-achievement-section .data-summary-table td {
            border: 1px solid var(--border-color);
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
        }

        #create-achievement-section .data-summary-table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            border-bottom: 2px solid #0d6efd;
        }

        #create-achievement-section .data-summary-table td {
            background-color: var(--card-bg);
            color: var(--text-color);
        }

        #create-achievement-section .data-summary-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Dark theme adjustments for data summary table */
        [data-theme="dark"] #create-achievement-section .data-summary-table th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #create-achievement-section .data-summary-table td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] #create-achievement-section .data-summary-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }
    </style>

    <style>
        /* Contract details styling */
        .detail-item {
            background-color: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem 1.25rem;
            display: flex;
            align-items: center;
            gap: 0;
            height: 100%;
            min-height: 60px;
        }
        
        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            white-space: nowrap;
            margin-left: 4px;
        }
        
        .detail-separator {
            color: var(--text-muted);
            margin: 0 4px;
            flex-shrink: 0;
        }
        
        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
            flex-grow: 1;
            text-align: right;
            font-size: 1.1rem;
            min-width: 120px;
            direction: ltr;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .detail-item {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] .detail-label,
        [data-theme="dark"] .detail-separator {
            color: var(--dark-text-muted);
        }

        [data-theme="dark"] .detail-value {
            color: var(--dark-text-primary);
        }

        .selected-record-details .row {
            margin: 0 -0.75rem;
        }

        .selected-record-details .col-md-4 {
            padding: 0 0.75rem;
        }

        @media (max-width: 768px) {
            .detail-item {
                padding: 0.875rem 1rem;
            }
            
            .detail-label {
                font-size: 0.95rem;
            }
            
            .detail-value {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <main id="content">
        <div class="container-fluid py-4">
            <?php if (!empty($error_message)): ?>
                <div class="custom-alert error" role="alert">
                    <?= $error_message ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="custom-alert success" role="alert">
                    <?= $success_message ?>
                </div>
            <?php endif; ?>

            <!-- Added informational message -->
            <div class="alert alert-info mb-4" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                مرحباً بك في صفحة إنشاء تقارير الإنجاز. يمكنك من خلال هذه الصفحة إنشاء وإدارة تقارير الإنجاز للمشاريع والعقود المختلفة، وتتبع التقدم في المهام المحددة.
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-4">البحث عن العقود</h5>
                    <form method="post" id="searchForm">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                    <select class="form-select select2-search" id="project_id" name="project_id" required>
                                        <option value="">اختر المشروع</option>
                                        <?php foreach ($projects as $project): ?>
                                            <option value="<?= $project['id_Project'] ?>" <?= isset($_POST['project_id']) && $_POST['project_id'] == $project['id_Project'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($project['Project_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contract_id" class="form-label fw-bold mb-2">اختر عقد</label>
                                    <select class="form-select select2-search" id="contract_id" name="contract_id" required <?= empty($_POST['project_id']) ? 'disabled' : '' ?>>
                                        <option value="">اختر العقد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($selectedItem): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">تفاصيل العقد</h5>
                        <div class="row g-3">
                            <?php if ($selectedItem['type'] === 'contract'): ?>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">رقم العقد الأساسي</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= $selectedItem['id_contract'] ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">اسم صاحب العقد</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= htmlspecialchars($selectedItem['name_ar_contract']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">المسمى الوظيفي</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= htmlspecialchars($selectedItem['name_Job']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ الإصدار</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['version_date'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ البداية</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['start_date_contract'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ النهاية</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= $selectedItem['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['end_date_contract'])) : 'مفتوح' ?></span>
                                    </div>
                                </div>
                            <?php elseif ($selectedItem['type'] === 'extension'): ?>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">رقم العقد الأساسي</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= $selectedItem['main_contract']['id_contract'] ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">رقم التمديد</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= $selectedItem['id_extension_contract'] ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">اسم صاحب العقد</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= htmlspecialchars($selectedItem['main_contract']['name_ar_contract']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">المسمى الوظيفي</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= htmlspecialchars($selectedItem['main_contract']['name_Job']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ الإصدار</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['version_date'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ البداية</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['start_date_contract'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ النهاية</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value"><?= $selectedItem['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['end_date_contract'])) : 'مفتوح' ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($attendanceRecords)): ?>
                <div class="card mb-4" id="section-three">
                    <div class="card-body">
                        <h5 class="card-title">سجلات الحضور وتقارير الإنجاز المرتبطة</h5>
                        <div class="attendance-records">
                            <?php foreach ($attendanceRecords as $id => $record): ?>
                                <div class="attendance-record-container">
                                    <div class="attendance-record-main">
                                        <div class="record-info d-flex align-items-center">
                                            <span class="record-id">سجل <?= htmlspecialchars($id) ?></span>
                                            <span class="record-separator mx-2">|</span>
                                            <span class="record-period">
                                                الفترة (من <?= date('Y-m-d', strtotime($record['info']['start_date_permanent_diapers'])) ?>
                                                إلى <?= date('Y-m-d', strtotime($record['info']['end_date_permanent_diapers'])) ?>)
                                            </span>
                                        </div>
                                        <button class="btn btn-primary view-details" 
                                                data-id="<?= $id ?>" 
                                                onclick="showAttendanceDetails(<?= $id ?>, '<?= htmlspecialchars($record['info']['data'], ENT_QUOTES) ?>')">
                                            عرض التفاصيل
                                        </button>
                                    </div>
                                    <div class="achievement-reports-list">
                                        <h6 class="reports-title">تقارير الإنجاز المرتبطة:</h6>
                                        <div class="reports-container">
                                            <?php if (!empty($record['reports'])): ?>
                                                <ul class="reports-items">
                                                    <?php foreach ($record['reports'] as $report): ?>
                                                        <li class="report-item">
                                                            <div class="report-info d-flex align-items-center">
                                                                <span class="record-id">تقرير <?= htmlspecialchars($report['id']) ?></span>
                                                                <span class="record-separator mx-2">|</span>
                                                                <span class="record-period">
                                                                    الفترة (من <?= date('Y-m-d', strtotime($report['start_date_achievement_reports'])) ?>
                                                                    إلى <?= date('Y-m-d', strtotime($report['end_date_achievement_reports'])) ?>)
                                                                </span>
                                                            </div>
                                                        </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            <?php else: ?>
                                                <div class="no-reports-message">لا يوجد تقارير إنجاز مرتبطة</div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div id="section-four" style="display: none;">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">تفاصيل سجل الحضور والانصراف</h5>
                        
                        <div class="selected-record-details mb-4">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">رقم سجل الحضور</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value" id="selected-record-number"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ البداية</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value" id="selected-start-date"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ النهاية</span>
                                        <span class="detail-separator">:</span>
                                        <span class="detail-value" id="selected-end-date"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <h6 class="mb-3">تفاصيل الحضور والانصراف</h6>
                        <div class="attendance-details-container mb-4">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th rowspan="3" class="align-middle">اليوم</th>
                                            <th rowspan="3" class="align-middle">التاريخ</th>
                                            <th colspan="4" class="text-center">الدوام الرسمي</th>
                                            <th colspan="4" class="text-center">الدوام الإضافي</th>
                                            <th rowspan="3" class="align-middle">ملاحظات</th>
                                        </tr>
                                        <tr>
                                            <th colspan="2" class="text-center">وقت الحضور</th>
                                            <th colspan="2" class="text-center">وقت الانصراف</th>
                                            <th colspan="2" class="text-center">وقت الحضور</th>
                                            <th colspan="2" class="text-center">وقت الانصراف</th>
                                        </tr>
                                        <tr>
                                            <th class="text-center">الحالة</th>
                                            <th class="text-center">الوقت</th>
                                            <th class="text-center">الحالة</th>
                                            <th class="text-center">الوقت</th>
                                            <th class="text-center">الحالة</th>
                                            <th class="text-center">الوقت</th>
                                            <th class="text-center">الحالة</th>
                                            <th class="text-center">الوقت</th>
                                        </tr>
                                    </thead>
                                    <tbody id="attendance-details-body">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <hr class="my-4">

                        <h6 class="mb-3">ملخص الحضور والانصراف</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>الحالة</th>
                                        <th>عدد الأيام</th>
                                    </tr>
                                </thead>
                                <tbody id="attendance-summary-body">
                                    <tr>
                                        <td>أيام العمل (حاضر)</td>
                                        <td id="present-days">0</td>
                                    </tr>
                                    <tr>
                                        <td>أيام الغياب (غائب)</td>
                                        <td id="absent-days">0</td>
                                    </tr>
                                    <tr>
                                        <td>أيام الإجازة (إجازة)</td>
                                        <td id="leave-days">0</td>
                                    </tr>
                                    <tr>
                                        <td>أيام غير محددة (-)</td>
                                        <td id="unspecified-days">0</td>
                                    </tr>
                                    <tr class="table-active fw-bold">
                                        <td>نسبة أيام العمل الإجمالية</td>
                                        <td id="overall-workdays-percentage">0%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($selectedAttendance): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">تفاصيل سجل الحضور المحدد</h5>
                        
                        <!-- Basic Info -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">رقم السجل</label>
                                    <input type="text" class="form-control" 
                                        value="<?= $selectedAttendance['id_permanent_diapers'] ?>" 
                                        readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الفترة</label>
                                    <input type="text" class="form-control" 
                                        value="<?= date('Y-m-d', strtotime($selectedAttendance['start_date'])) ?> - <?= date('Y-m-d', strtotime($selectedAttendance['end_date'])) ?>" 
                                        readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Attendance Data Table -->
                        <div class="table-responsive">
                            <table class="table data-table table-bordered">
                                <thead>
                                    <tr>
                                        <th>اليوم</th>
                                        <th>التاريخ</th>
                                        <th>الحضور</th>
                                        <th>وقت الدخول</th>
                                        <th>وقت الخروج</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($selectedAttendance['data']['AttendanceData'] as $day => $data): ?>
                                        <tr>
                                            <td><?= $data['BasicInfo']['DayName'] ?></td>
                                            <td><?= $data['BasicInfo']['DayNumber'] ?>/<?= $data['BasicInfo']['MonthNumber'] ?></td>
                                            <td><?= $data['RegularShift']['CheckIn']['Status'] ?></td>
                                            <td><?= $data['RegularShift']['CheckIn']['Time'] ?></td>
                                            <td><?= $data['RegularShift']['CheckOut']['Time'] ?></td>
                                            <td><?= $data['AdditionalNotes'] ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h6 class="mb-3">ملخص الحضور:</h6>
                                <?php
                                $totalDays = count($selectedAttendance['data']['AttendanceData']);
                                $presentDays = 0;
                                $absentDays = 0;
                                
                                foreach ($selectedAttendance['data']['AttendanceData'] as $day) {
                                    if ($day['RegularShift']['CheckIn']['Status'] === 'حاضر') {
                                        $presentDays++;
                                    } else {
                                        $absentDays++;
                                    }
                                }
                                ?>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-primary summary-badge">إجمالي الأيام: <?= $totalDays ?></span>
                                    <span class="badge bg-success summary-badge">أيام الحضور: <?= $presentDays ?></span>
                                    <span class="badge bg-danger summary-badge">أيام الغياب: <?= $absentDays ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($selectedAttendance && !empty($achievementReports)): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">فترة تقرير الإنجاز</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ البدء</label>
                                    <input type="date" class="form-control" 
                                        value="<?= date('Y-m-d', strtotime($selectedAttendance['start_date'])) ?>" 
                                        readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الانتهاء</label>
                                    <input type="date" class="form-control" 
                                        value="<?= date('Y-m-d', strtotime($selectedAttendance['end_date'])) ?>" 
                                        readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h6>فترات التقارير المرتبطة:</h6>
                            <div class="list-group">
                                <?php foreach ($achievementReports as $report): ?>
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <?= date('Y-m-d', strtotime($report['start_date_achievement_reports'])) ?> - 
                                                <?= date('Y-m-d', strtotime($report['end_date_achievement_reports'])) ?>
                                            </div>
                                            <div>
                                                <span class="badge bg-secondary">أيام العمل الفعلية: <?= $report['actual_working_days'] ?></span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($achievementReports)): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">تقارير الإنجاز</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>الفترة</th>
                                        <th>أيام العمل الفعلية</th>
                                        <th>قائمة المهام المنجزة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($achievementReports as $report): ?>
                                        <tr>
                                            <td>
                                                <?= date('Y-m-d', strtotime($report['start_date'])) ?>
                                                إلى
                                                <?= date('Y-m-d', strtotime($report['end_date'])) ?>
                                            </td>
                                            <td><?= $report['actual_working_days'] ?></td>
                                            <td>
                                                <?php if (!empty($report['todo_list'])): ?>
                                                    <ul class="list-unstyled mb-0">
                                                        <?php foreach ($report['todo_list'] as $task): ?>
                                                            <li><?= htmlspecialchars($task) ?></li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                <?php else: ?>
                                                    <em>لا توجد مهام مسجلة</em>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-primary view-details" 
                                                        data-report-id="<?= $report['id'] ?>">
                                                    عرض التفاصيل
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- After the section-four div, modify the Completion Period Selection card to include an id -->
            <div class="card mb-4" id="completion-period-section" style="display: none;">
                <div class="card-body">
                    <h5 class="card-title mb-4">تحديد فترة الإنجاز</h5>
                    
                    <div class="retention-period-container d-flex">
                        <div class="calendar-section ms-4">
                            <div class="calendar-wrapper">
                                <div class="calendar-navigation">
                                    <button type="button" class="btn btn-sm btn-outline-secondary prev-month">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                    <span class="current-month"></span>
                                    <button type="button" class="btn btn-sm btn-outline-secondary next-month">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                </div>
                                <div class="calendar-header">
                                    <div class="calendar-day-header">Sun</div>
                                    <div class="calendar-day-header">Mon</div>
                                    <div class="calendar-day-header">Tue</div>
                                    <div class="calendar-day-header">Wed</div>
                                    <div class="calendar-day-header">Thu</div>
                                    <div class="calendar-day-header">Fri</div>
                                    <div class="calendar-day-header">Sat</div>
                                </div>
                                <div class="calendar-container"></div>
                            </div>
                        </div>

                        <div class="date-fields-section flex-grow-1">
                            <div class="mb-3">
                                <label for="retention_start_date" class="form-label">تاريخ البداية</label>
                                <input type="text" 
                                       class="retention-date-input" 
                                       id="retention_start_date" 
                                       readonly 
                                       placeholder="Select from calendar">
                            </div>
                            <div class="mb-3">
                                <label for="retention_end_date" class="form-label">تاريخ النهاية</label>
                                <input type="text" 
                                       class="retention-date-input" 
                                       id="retention_end_date" 
                                       readonly 
                                       placeholder="Select from calendar">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد أيام الفترة</label>
                                <input type="text" 
                                       class="retention-date-input" 
                                       id="period_days" 
                                       readonly 
                                       value="-">
                            </div>
                            <div class="achievement-btn-container">
                                <button type="button" 
                                        class="btn btn-primary achievement-create-btn" 
                                        id="create-report-btn" 
                                        disabled>
                                    إنشاء تقرير الإنجاز
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- After the completion-period-section div, add the following -->
            <div class="card mb-4" id="create-achievement-section" style="display: none;">
                <div class="card-body">
                    <h5 class="card-title mb-4">إنشاء تقرير الإنجاز</h5>
                    <div id="achievement-form-container">
                        <div class="table-responsive">
                            <table class="table" id="achievement-tasks-table">
                                <thead>
                                    <!-- Will be populated dynamically -->
                                </thead>
                                <tbody>
                                    <!-- Will be populated with tasks -->
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            <h6 class="mb-3 pb-2 border-bottom border-primary">ملخص البيانات</h6>
                            <div class="table-responsive">
                                <table class="table data-summary-table">
                                    <thead>
                                        <tr>
                                            <th>رقم البرنامج الأساسي</th>
                                            <th>رقم العقد الأساسي</th>
                                            <th>رقم التمديد</th>
                                            <th>رقم سجل الحضور</th>
                                            <th>تاريخ البداية</th>
                                            <th>تاريخ النهاية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr id="saved-data-row">
                                            <td id="project-number">-</td>
                                            <td id="contract-number">-</td>
                                            <td id="extension-number">-</td>
                                            <td id="attendance-number">-</td>
                                            <td id="start-date">-</td>
                                            <td id="end-date">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add the JSON Display Section -->
            <div class="card mb-4" id="json-display-section" style="display: none;">
                <div class="card-body">
                    <h5 class="card-title mb-4 d-flex align-items-center justify-content-between">
                        عرض كود JSON
                        <button class="btn btn-link p-0 text-decoration-none" type="button" data-bs-toggle="collapse" data-bs-target="#jsonDisplayCollapse" aria-expanded="false" aria-controls="jsonDisplayCollapse">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </h5>
                    <div class="collapse" id="jsonDisplayCollapse">
                        <textarea id="json_display" class="form-control" rows="10" readonly 
                            style="direction: ltr; text-align: left; font-family: monospace;"></textarea>
                    </div>
                </div>
            </div>

            <script>
            $(document).ready(function() {
                let startDate = null;
                let endDate = null;
                
                $(".calendar-day").on("mousedown", function() {
                    startDate = $(this).data("date");
                    $(this).addClass("selected");
                });

                $(".calendar-day").on("mouseover", function() {
                    if (startDate) {
                        endDate = $(this).data("date");
                        highlightRange(startDate, endDate);
                    }
                });

                $(document).on("mouseup", function() {
                    if (startDate && endDate) {
                        $("#start_date").val(startDate);
                        $("#end_date").val(endDate);
                    }
                    startDate = null;
                    endDate = null;
                });

                function highlightRange(start, end) {
                    $(".calendar-day").removeClass("selected");
                    let selecting = false;
                    $(".calendar-day").each(function() {
                        let date = $(this).data("date");
                        if (date === start) selecting = true;
                        if (selecting) $(this).addClass("selected");
                        if (date === end) selecting = false;
                    });
                }

                // Store the attendance record ID when showing details
                window.showAttendanceDetails = function(id, attendanceData) {
                    selectedAttendanceRecordId = id;
                    // ... rest of the existing showAttendanceDetails function
                };

                // Update the selected period and enable button
                window.updateSelectedPeriod = function() {
                    if (selectedStartDate && selectedEndDate) {
                        const startInput = document.getElementById('retention_start_date');
                        const endInput = document.getElementById('retention_end_date');
                        const periodDaysInput = document.getElementById('period_days');
                        const createReportBtn = document.getElementById('create-report-btn');
                        
                        startInput.value = formatDate(selectedStartDate);
                        endInput.value = formatDate(selectedEndDate);
                        
                        // Calculate the number of days between dates
                        const timeDiff = Math.abs(selectedEndDate.getTime() - selectedStartDate.getTime());
                        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // Adding 1 to include both start and end dates
                        periodDaysInput.value = daysDiff + ' يوم';
                        
                        // Enable the create report button when dates are selected
                        if (createReportBtn) {
                            createReportBtn.disabled = false;
                        }
                    }
                };
            });
            </script>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script>
        // Theme handling
        function setTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            // Update theme toggle button icon
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                const icon = themeToggle.querySelector('i');
                if (theme === 'dark') {
                    icon.classList.remove('bi-sun-fill');
                    icon.classList.add('bi-moon-fill');
                } else {
                    icon.classList.remove('bi-moon-fill');
                    icon.classList.add('bi-sun-fill');
                }
            }
        }

        // Initialize theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);

        // Theme toggle handler
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Initialize Select2
        $(document).ready(function() {
            // Initialize Select2 for project dropdown
            $('#project_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر المشروع',
                allowClear: true
            });

            // Initialize Select2 for contract dropdown with custom formatting
            $('#contract_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر العقد',
                allowClear: true,
                templateResult: formatContract,
                templateSelection: formatContractSelection
            });

            // Custom formatting for contract options in dropdown
            function formatContract(contract) {
                if (!contract.id) {
                    return contract.text;
                }

                var isExtension = contract.element.className.includes('extension-option');
                var text = isExtension ? contract.text : '• ' + contract.text;

                var $contract = $(
                    '<span class="contract-tree-item ' + 
                    (isExtension ? 'contract-extension' : 'contract-main') + 
                    '">' + text + '</span>'
                );

                return $contract;
            }

            // Custom formatting for selected contract
            function formatContractSelection(contract) {
                if (!contract.id) {
                    return contract.text;
                }
                var isExtension = contract.element.className.includes('extension-option');
                return isExtension ? contract.text : '• ' + contract.text;
            }

            // Project selection change handler
            $('#project_id').on('change', function() {
                const projectId = $(this).val();
                const contractSelect = $('#contract_id');
                
                // Reset and disable contract select if no project selected
                if (!projectId) {
                    contractSelect.empty().prop('disabled', true).trigger('change');
                    return;
                }

                // Enable contract select and load data
                contractSelect.prop('disabled', false);
                
                $.get(`?action=get_contracts&project_id=${projectId}`, function(data) {
                    contractSelect.empty().append('<option value="">اختر العقد</option>');
                    
                    data.forEach(contract => {
                        // Add main contract
                        contractSelect.append($('<option>', {
                            value: contract.id,
                            text: contract.text,
                            class: 'contract-main-option'
                        }));
                        
                        // Add extensions if any
                        if (contract.extensions && contract.extensions.length > 0) {
                            contract.extensions.forEach((ext, index) => {
                                contractSelect.append($('<option>', {
                                    value: ext.id,
                                    text: ext.text,
                                    class: 'extension-option' + 
                                          (index === 0 ? ' first-extension' : '') + 
                                          (index === contract.extensions.length - 1 ? ' last-extension' : '')
                                }));
                            });
                        }
                    });
                    
                    contractSelect.trigger('change');
                });
            });

            // Form submission handler
            $('#searchForm').on('submit', function(e) {
                if (!$(this).find(':submit').hasClass('clicked')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Submit button click handler
            $('#searchForm :submit').on('click', function() {
                $(this).addClass('clicked');
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.custom-alert').fadeOut('slow');
            }, 5000);
        });
        
        // Achievement reports details view handler
        $(document).on('click', '.view-details', function(e) {
            e.preventDefault();
            const reportId = $(this).data('report-id');
            const row = $(this).closest('tr');
            
            // Toggle the details section for this row
            const detailsRow = row.next('.report-details-row');
            if (detailsRow.length) {
                detailsRow.toggle();
                return;
            }
            
            // Create and insert the details row
            const todoList = $(this).data('todo-list');
            const startDate = $(this).data('start-date');
            const endDate = $(this).data('end-date');
            const actualWorkingDays = $(this).data('working-days');
            
            const detailsContent = `
                <tr class="report-details-row">
                    <td colspan="7">
                        <div class="report-details p-3">
                            <div class="mb-3">
                                <strong>فترة التقرير:</strong> 
                                <span>${startDate} - ${endDate}</span>
                            </div>
                            <div class="mb-3">
                                <strong>أيام العمل الفعلية:</strong> 
                                <span>${actualWorkingDays}</span>
                            </div>
                            <div>
                                <strong>قائمة المهام:</strong>
                                <ul class="todo-list mt-2">
                                    ${todoList ? Object.entries(todoList).map(([key, value]) => 
                                        `<li>
                                            <strong>${key}:</strong> 
                                            <span>${value}</span>
                                        </li>`
                                    ).join('') : '<li>لا توجد مهام مسجلة</li>'}
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>`;
            
            row.after(detailsContent);
        });
    </script>
    <script>
        // Add these variables at the start of your script
        let isSelecting = false;
        let selectedStartDate = null;
        let selectedEndDate = null;
        let tempEndDate = null;
        let currentMonth = new Date();
        let disabledDates = null; // Add this variable to track valid date range
        let existingReportPeriods = []; // Array to store existing report periods

        function showAttendanceDetails(id, attendanceData) {
            // Store the selected attendance record ID globally
            selectedAttendanceRecordId = id;
            
            // Show section four
            document.getElementById('section-four').style.display = 'block';
            
            // Show the completion period section
            const completionPeriodSection = document.getElementById('completion-period-section');
            completionPeriodSection.style.display = 'block';

            // Add smooth scrolling to the completion period section
            setTimeout(() => {
                completionPeriodSection.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
            
            // Parse the attendance data if it's a string
            const data = typeof attendanceData === 'string' ? JSON.parse(attendanceData) : attendanceData;
            
            // Update selected record details
            document.getElementById('selected-record-number').textContent = id;
            
            // Find the corresponding record in the attendance records
            const recordElement = document.querySelector(`[data-id="${id}"]`);
            if (recordElement) {
                const recordContainer = recordElement.closest('.attendance-record-container');
                const periodElement = recordContainer.querySelector('.record-period');
                if (periodElement) {
                    const periodText = periodElement.textContent.trim();
                    const matches = periodText.match(/من\s+(\S+)\s+إلى\s+(\S+)\)?/);
                    if (matches) {
                        // Parse the dates and ensure they are set to the start and end of their respective days
                        const startDate = new Date(matches[1]);
                        startDate.setHours(0, 0, 0, 0);
                        
                        const endDate = new Date(matches[2].replace(')', ''));
                        endDate.setHours(23, 59, 59, 999);  // Set to end of day
                        
                        // Update the display dates
                        document.getElementById('selected-start-date').textContent = matches[1];
                        document.getElementById('selected-end-date').textContent = matches[2].replace(')', '');
                        
                        // Initialize calendar with the period dates
                        currentMonth = new Date(startDate);
                        
                        // Set up disabled dates for the attendance period
                        disabledDates = {
                            before: startDate,
                            after: endDate
                        };

                        console.log('Setting disabled dates:', {
                            before: startDate.toISOString(),
                            after: endDate.toISOString()
                        });

                        // Get existing report periods from the reports list
                        existingReportPeriods = [];
                        const reportsContainer = recordContainer.querySelector('.reports-items');
                        if (reportsContainer) {
                            const reportItems = reportsContainer.querySelectorAll('.report-item');
                            reportItems.forEach(item => {
                                const periodSpan = item.querySelector('.record-period');
                                if (periodSpan) {
                                    const periodText = periodSpan.textContent.trim();
                                    const periodMatches = periodText.match(/الفترة \(من\s+(\S+)\s+إلى\s+(\S+)\)/);
                                    if (periodMatches) {
                                        existingReportPeriods.push({
                                            start: new Date(periodMatches[1]),
                                            end: new Date(periodMatches[2])
                                        });
                                    }
                                }
                            });
                        }

                        console.log('Attendance Period:', { startDate, endDate });
                        console.log('Existing Report Periods:', existingReportPeriods);
                        
                        // Reset selection state
                        isSelecting = false;
                        selectedStartDate = null;
                        selectedEndDate = null;
                        tempEndDate = null;
                        
                        // Clear the input fields
                        document.getElementById('retention_start_date').value = '';
                        document.getElementById('retention_end_date').value = '';
                        document.getElementById('period_days').value = '-';
                        
                        // Show the completion period section
                        document.getElementById('completion-period-section').style.display = 'block';
                        
                        // Render calendar with updated disabled dates
                        setTimeout(() => {
                            renderCalendar();
                        }, 100);
                    }
                }
            }
            
            // Get the attendance details body element
            const tableBody = document.getElementById('attendance-details-body');
            tableBody.innerHTML = ''; // Clear existing content
            
            // Initialize counters for summary
            let presentDays = 0;
            let absentDays = 0;
            let leaveDays = 0;
            let unspecifiedDays = 0;
            
            // Loop through the attendance data and create table rows
            if (data && data.AttendanceData) {
                Object.entries(data.AttendanceData).forEach(([date, dayData]) => {
                    const row = document.createElement('tr');
                    
                    // Create cells for the row
                    row.innerHTML = `
                        <td>${dayData.BasicInfo.DayName}</td>
                        <td>${dayData.BasicInfo.DayNumber}/${dayData.BasicInfo.MonthNumber}</td>
                        <td>${dayData.RegularShift.CheckIn.Status || '-'}</td>
                        <td>${dayData.RegularShift.CheckIn.Time || '-'}</td>
                        <td>${dayData.RegularShift.CheckOut.Status || '-'}</td>
                        <td>${dayData.RegularShift.CheckOut.Time || '-'}</td>
                        <td>${dayData.OvertimeShift.CheckIn.Status || '-'}</td>
                        <td>${dayData.OvertimeShift.CheckIn.Time || '-'}</td>
                        <td>${dayData.OvertimeShift.CheckOut.Status || '-'}</td>
                        <td>${dayData.OvertimeShift.CheckOut.Time || '-'}</td>
                        <td>${dayData.AdditionalNotes || '-'}</td>
                    `;
                    
                    tableBody.appendChild(row);
                    
                    // Update counters for summary
                    if (dayData.RegularShift.CheckIn.Status === 'حاضر') {
                        presentDays++;
                    } else if (dayData.RegularShift.CheckIn.Status === 'غائب') {
                        absentDays++;
                    } else if (dayData.RegularShift.CheckIn.Status === 'إجازة') {
                        leaveDays++;
                    } else {
                        unspecifiedDays++;
                    }
                });
            }
            
            // Update summary counts
            const totalDays = presentDays + absentDays + leaveDays + unspecifiedDays;
            
            // Update summary table
            document.getElementById('present-days').textContent = presentDays;
            document.getElementById('absent-days').textContent = absentDays;
            document.getElementById('leave-days').textContent = leaveDays;
            document.getElementById('unspecified-days').textContent = unspecifiedDays;
            
            // Calculate overall workdays percentage (considering leave days as workdays)
            const workDays = presentDays + leaveDays;
            const overallWorkdaysPercentage = totalDays ? ((workDays / totalDays) * 100).toFixed(1) : 0;
            document.getElementById('overall-workdays-percentage').textContent = `${overallWorkdaysPercentage}%`;
            
            // Show the completion period section and saved data section
            document.getElementById('completion-period-section').style.display = 'block';
            
            // Update the saved data section with the new attendance record ID
            updateSavedDataSection();
            
            // Initialize the create report button functionality
            initializeCreateReportButton();
            
            setTimeout(() => {
                renderCalendar();
            }, 300);
        }

        function initializeCreateReportButton() {
            const createReportBtn = document.getElementById('create-report-btn');
            if (createReportBtn) {
                // Remove any existing event listeners
                createReportBtn.replaceWith(createReportBtn.cloneNode(true));
                
                // Get the fresh reference to the button
                const newCreateReportBtn = document.getElementById('create-report-btn');
                
                // Add the click event listener
                newCreateReportBtn.addEventListener('click', function() {
                    console.log('Create report button clicked');
                    showAchievementSection();
                    
                    // Show the achievement section
                    const achievementSection = document.getElementById('create-achievement-section');
                    if (achievementSection) {
                        achievementSection.style.display = 'block';
                        achievementSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            }
        }

        function isDateDisabled(date) {
            if (!disabledDates) return true;
            
            // Set the compare date to the start of the day for consistent comparison
            const compareDate = new Date(date);
            compareDate.setHours(0, 0, 0, 0);
            
            // Get the before date (start of day)
            const beforeDate = new Date(disabledDates.before);
            beforeDate.setHours(0, 0, 0, 0);
            
            // Get the after date (end of day)
            const afterDate = new Date(disabledDates.after);
            afterDate.setHours(23, 59, 59, 999);
            
            // Check if date is outside the attendance period
            // For the end date comparison, we compare with the end of day
            if (compareDate < beforeDate || compareDate > afterDate) {
                return true;
            }

            // Check if date falls within any existing report period
            for (const period of existingReportPeriods) {
                const periodStart = new Date(period.start);
                periodStart.setHours(0, 0, 0, 0);
                
                const periodEnd = new Date(period.end);
                periodEnd.setHours(23, 59, 59, 999);
                
                if (compareDate >= periodStart && compareDate <= periodEnd) {
                    return true;
                }
            }

            return false;
        }

        function isRangeValid(startDate, endDate) {
            if (!startDate || !endDate) return false;

            const start = new Date(startDate);
            start.setHours(0, 0, 0, 0);
            
            const end = new Date(endDate);
            end.setHours(0, 0, 0, 0);
            
            // Check each day in the range
            const currentDate = new Date(start);
            while (currentDate <= end) {
                if (isDateDisabled(currentDate)) {
                    return false;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }
            
            return true;
        }

        function renderCalendar() {
            if (!disabledDates) return;
            
            const calendarContainer = $('.calendar-container');
            const year = currentMonth.getFullYear();
            const month = currentMonth.getMonth();
            
            // Update month display
            $('.current-month').text(currentMonth.toLocaleString('en-US', { 
                month: 'long',
                year: 'numeric'
            }));

            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startingDay = firstDay.getDay();
            const monthLength = lastDay.getDate();

            const totalDays = startingDay + monthLength;
            const rows = Math.ceil(totalDays / 7);
            const totalCells = rows * 7;

            let calendarHtml = '';

            // Create calendar grid
            for (let i = 0; i < totalCells; i++) {
                const day = i - startingDay + 1;
                const date = new Date(year, month, day);
                const isCurrentMonth = day > 0 && day <= monthLength;
                const dateString = isCurrentMonth ? `${date.getFullYear()}-${String(date.getMonth()+1).padStart(2,'0')}-${String(date.getDate()).padStart(2,'0')}` : '';
                
                let classes = ['calendar-day'];
                let title = '';
                
                if (!isCurrentMonth) {
                    classes.push('out-of-period');
                } else {
                    // Check if date should be disabled
                    if (isDateDisabled(date)) {
                        classes.push('disabled');
                        // Check if it's disabled due to existing report
                        for (const period of existingReportPeriods) {
                            if (date >= period.start && date <= period.end) {
                                title = 'يوجد تقرير إنجاز لهذه الفترة';
                                break;
                            }
                        }
                        if (!title) {
                            title = 'خارج فترة سجل الحضور';
                        }
                    }

                    // Add today class if it's today
                    if (date.toDateString() === new Date().toDateString()) {
                        classes.push('today');
                    }

                    // Show temporary selection during drag
                    if (isSelecting && selectedStartDate && !isDateDisabled(date)) {
                        const endDate = tempEndDate || selectedStartDate;
                        const minDate = new Date(Math.min(selectedStartDate.getTime(), endDate.getTime()));
                        const maxDate = new Date(Math.max(selectedStartDate.getTime(), endDate.getTime()));
                        
                        if (isRangeValid(minDate, maxDate)) {
                            const compareDate = new Date(date);
                            compareDate.setHours(0, 0, 0, 0);
                            if (compareDate >= minDate && compareDate <= maxDate) {
                                classes.push('in-range');
                            }
                        }
                    }
                    // Show permanent selection
                    else if (selectedStartDate && selectedEndDate && !isDateDisabled(date)) {
                        const compareDate = new Date(date);
                        compareDate.setHours(0, 0, 0, 0);
                        if (compareDate >= selectedStartDate && compareDate <= selectedEndDate) {
                            classes.push('in-range');
                        }
                    }
                }
                
                // Add selected class for start and end dates
                if (isCurrentMonth && !isDateDisabled(date)) {
                    const compareDate = new Date(date);
                    compareDate.setHours(0, 0, 0, 0);
                    if (selectedStartDate && compareDate.getTime() === selectedStartDate.getTime()) {
                        classes.push('selected start-date');
                    }
                    if (selectedEndDate && compareDate.getTime() === selectedEndDate.getTime()) {
                        classes.push('selected end-date');
                    }
                }

                calendarHtml += `
                    <div class="${classes.join(' ')}" 
                         data-date="${dateString}"
                         ${isCurrentMonth ? `title="${formatDate(date)}${isDateDisabled(date) ? ' (غير متاح)' : ''}"` : ''}>
                        ${isCurrentMonth ? day : ''}
                    </div>`;
            }

            calendarContainer.html(calendarHtml);

            // Attach mouse event handlers
            attachCalendarEventHandlers();
        }

        function attachCalendarEventHandlers() {
            const calendarDays = $('.calendar-day:not(.out-of-period):not(.disabled)');
            
            // Mouse down event - start selection
            calendarDays.off('mousedown touchstart').on('mousedown touchstart', function(e) {
                e.preventDefault();
                if ($(this).hasClass('disabled')) return;
                
                isSelecting = true;
                const dateStr = $(this).data('date');
                if (dateStr) {
                    selectedStartDate = new Date(dateStr + 'T00:00:00');
                    selectedStartDate.setHours(0, 0, 0, 0);
                    selectedEndDate = null;
                    tempEndDate = null;
                    renderCalendar();
                }
            });

            // Mouse move event - update selection
            calendarDays.off('mousemove touchmove').on('mousemove touchmove', function(e) {
                if (!isSelecting || $(this).hasClass('disabled')) return;
                
                const dateStr = $(this).data('date');
                if (dateStr) {
                    e.preventDefault();
                    const newTempEndDate = new Date(dateStr + 'T00:00:00');
                    newTempEndDate.setHours(0, 0, 0, 0);
                    
                    if (isRangeValid(selectedStartDate, newTempEndDate)) {
                        tempEndDate = newTempEndDate;
                        renderCalendar();
                    }
                }
            });

            // Mouse up event - end selection
            $(document).off('mouseup touchend').on('mouseup touchend', function() {
                if (isSelecting) {
                    isSelecting = false;
                    if (tempEndDate && isRangeValid(selectedStartDate, tempEndDate)) {
                        selectedEndDate = new Date(tempEndDate);
                        selectedEndDate.setHours(0, 0, 0, 0);
                        tempEndDate = null;
                        updateSelectedPeriod();
                        renderCalendar();
                    }
                }
            });

            // Handle mouse leaving calendar
            $('.calendar-wrapper').off('mouseleave').on('mouseleave', function() {
                if (isSelecting && tempEndDate) {
                    renderCalendar();
                }
            });
        }

        function formatDate(date) {
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        function updateSelectedPeriod() {
            if (selectedStartDate && selectedEndDate) {
                const startInput = document.getElementById('retention_start_date');
                const endInput = document.getElementById('retention_end_date');
                const periodDaysInput = document.getElementById('period_days');
                const createReportBtn = document.getElementById('create-report-btn');
                
                startInput.value = formatDate(selectedStartDate);
                endInput.value = formatDate(selectedEndDate);
                
                // Calculate the number of days between dates
                const timeDiff = Math.abs(selectedEndDate.getTime() - selectedStartDate.getTime());
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // Adding 1 to include both start and end dates
                periodDaysInput.value = daysDiff + ' يوم';
                
                // Enable the create report button when dates are selected
                if (createReportBtn) {
                    createReportBtn.disabled = false;
                }
            }
        }

        // Add event listeners for calendar navigation
        $(document).ready(function() {
            $('.prev-month').on('click', function() {
                currentMonth.setMonth(currentMonth.getMonth() - 1);
                renderCalendar();
            });

            $('.next-month').on('click', function() {
                currentMonth.setMonth(currentMonth.getMonth() + 1);
                renderCalendar();
            });

            // Initial render
            renderCalendar();
        });
    </script>
    <style>
        .attendance-details-container {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            overflow: hidden;
        }
        
        .attendance-details-container .table-responsive {
            border: none;
        }
    </style>
    <style>
        .detail-item {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
        }
        
        .detail-value {
            color: var(--text-color);
            font-size: 1rem;
        }
    </style>
    <style>
        .selected-record-details {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem 2rem;
            align-items: center;
        }
        
        .detail-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            white-space: nowrap;
        }
        .detail-value {
            color: var(--text-color);
            font-weight: 500;
        }
    </style>
    <style>
        .retention-period-container {
            display: none; 
        }
    </style>

    <script>
    // Global variables for achievement functionality
    let selectedContractId = null;
    let selectedAttendanceRecordId = null;

    function showAchievementSection() {
        console.log('showAchievementSection called');
        
        // Get the contract ID directly from the selectedItem PHP variable
        const selectedItem = <?php echo json_encode($selectedItem ?? null); ?>;
        console.log('Selected item:', selectedItem);
        
        if (selectedItem) {
            if (selectedItem.type === 'contract') {
                // If it's a main contract, use its ID directly
                contractId = selectedItem.id_contract;
            } else if (selectedItem.type === 'extension') {
                // If it's an extension, use the main contract ID
                contractId = selectedItem.id_contract;
            }
        }
        
        console.log('Resolved contract ID:', contractId);
        
        if (contractId) {
            // Store the contract ID
            selectedContractId = contractId;
            
            // Show the achievement section
            $('#create-achievement-section').show();
            
            // Initialize the achievement report form
            initializeAchievementReport(contractId);

            // Update saved data section
            updateSavedDataSection();
            
            // Scroll to the section
            $('#create-achievement-section')[0].scrollIntoView({ 
                behavior: 'smooth' 
            });
        } else {
            console.error('No contract ID found');
            alert('Could not determine contract ID');
        }
    }

    function initializeAchievementReport(contractId) {
        console.log('Initializing achievement report for contract:', contractId);
        
        // Get the selected attendance record ID
        if (!selectedAttendanceRecordId) {
            console.error('No attendance record selected');
            alert('Please select an attendance record first');
            return;
        }

        $.ajax({
            url: 'ajax/get_contract_data.php',
            method: 'GET',
            data: { 
                contract_id: contractId,
                permanent_diapers_id: selectedAttendanceRecordId
            },
            success: function(response) {
                console.log('Contract data response:', response);
                if (response.success) {
                    let todoList = JSON.parse(response.data.data_todo_list_contract);
                    
                    // Initialize total field for all tasks if not present
                    if (todoList.jobDetails && todoList.jobDetails.tasks) {
                        todoList.jobDetails.tasks = todoList.jobDetails.tasks.map(task => ({
                            ...task,
                            total: typeof task.total !== 'undefined' ? task.total : 0
                        }));
                    }
                    
                    setupAchievementTable(todoList);
                } else {
                    console.error('Failed to fetch contract data:', response.message);
                    alert('Failed to fetch contract data: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Ajax error:', error);
                alert('Error fetching contract data: ' + error);
            }
        });
    }

    function setupAchievementTable(todoList) {
        const thead = document.querySelector('#achievement-tasks-table thead');
        const tbody = document.querySelector('#achievement-tasks-table tbody');
        
        // Store the current todoList for updates
        window.currentTodoList = todoList;
        
        // Initialize tasks with total and notes for all contract types
        if (window.currentTodoList.jobDetails && window.currentTodoList.jobDetails.tasks) {
            window.currentTodoList.jobDetails.tasks.forEach(task => {
                // Initialize total if not present
                if (typeof task.total === 'undefined') {
                    task.total = 0;
                }
                // Reset notes to default value
                task.notes = "-";
            });
        }
        
        // Clear existing content
        thead.innerHTML = '';
        tbody.innerHTML = '';
        
        // Determine evaluation type
        const isPercentageBased = todoList.evaluation.percentageEvaluation === "yes";
        
        // Get total period days
        const periodDaysInput = document.getElementById('period_days');
        const totalPeriodDays = parseInt(periodDaysInput.value) || 0;

        // Remove any existing actual workdays container
        const existingContainer = document.querySelector('.actual-workdays-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Add actual workdays field if not percentage based
        if (!isPercentageBased) {
            const actualWorkdaysContainer = document.createElement('div');
            actualWorkdaysContainer.className = 'actual-workdays-container';
            actualWorkdaysContainer.innerHTML = `
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <label for="actual-workdays" class="form-label">
                            أيام العمل الفعلية
                            <span class="required-asterisk">*</span>
                        </label>
                        <div class="input-group">
                            <input type="number" 
                                   class="form-control" 
                                   id="actual-workdays" 
                                   min="1" 
                                   max="${totalPeriodDays}"
                                   placeholder="أدخل عدد أيام العمل الفعلية"
                                   required>
                        </div>
                        <div class="input-help-text">
                            <i class="bi bi-info-circle me-1"></i>
                            الحد الأقصى: ${totalPeriodDays} يوم
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="alert alert-info mb-0" role="alert">
                            <i class="bi bi-lightbulb me-2"></i>
                            يجب إدخال أيام العمل الفعلية قبل تسجيل تفاصيل المهام
                        </div>
                    </div>
                </div>
            `;
            
            // Insert before the table
            const table = document.querySelector('#achievement-tasks-table');
            table.parentElement.insertBefore(actualWorkdaysContainer, table);

            // Add event listener for actual workdays input
            const actualWorkdaysInput = document.getElementById('actual-workdays');
            actualWorkdaysInput.addEventListener('input', function() {
                let value = parseInt(this.value) || 0;
                
                // Validate input
                if (value <= 0) {
                    showInputWarning(this, 'يجب أن تكون أيام العمل الفعلية أكبر من 0');
                    this.value = '';
                } else if (value > totalPeriodDays) {
                    showInputWarning(this, `لا يمكن أن تتجاوز أيام العمل الفعلية ${totalPeriodDays} يوم`);
                    this.value = totalPeriodDays;
                    value = totalPeriodDays;
                }

                // Reset all task inputs when actual workdays changes
                const progressInputs = document.querySelectorAll('.current-progress');
                const notesInputs = document.querySelectorAll('.task-notes');
                
                progressInputs.forEach(input => {
                    input.value = 0;
                    input.disabled = !value;
                    updateProgress(input, isPercentageBased);
                });
                
                notesInputs.forEach(input => {
                    input.value = "-";
                    input.disabled = !value;
                    updateNotes(input);
                });

                // Update max values for progress inputs
                progressInputs.forEach(input => {
                    input.max = value;
                    const helpText = input.parentElement.querySelector('.input-help-text');
                    if (helpText) {
                        helpText.textContent = `الحد الأقصى: ${value} يوم`;
                    }
                });

                validateForm();
            });
        }
        
        // Create header based on evaluation type
        const headerRow = document.createElement('tr');
        headerRow.innerHTML = `
            <th>اسم المهمة</th>
            <th>${isPercentageBased ? 'نسبة الإنجاز حتى الآن' : 'أيام العمل المنجزة حتى الآن'}</th>
            <th>${isPercentageBased ? 'نسبة الإنجاز للفترة المحددة' : 'أيام العمل للفترة المحددة'}</th>
            <th>${isPercentageBased ? 'إجمالي نسبة الإنجاز' : 'إجمالي أيام العمل'}</th>
            <th>ملاحظات</th>
        `;
        thead.appendChild(headerRow);
        
        // Add tasks
        todoList.jobDetails.tasks.forEach((task, index) => {
            const row = document.createElement('tr');
            
            // Set the initial total as the "progress so far" value
            const progressSoFar = task.total || 0;
            task.completionRate = 0; // Reset completion rate for new entries
            
            row.innerHTML = `
                <td>${task.taskName}</td>
                <td class="text-center previous-progress">
                    ${isPercentageBased ? progressSoFar + '%' : progressSoFar}
                </td>
                <td class="text-center">
                    <input type="number" 
                        class="form-control current-progress" 
                        data-task-index="${index}"
                        min="0" 
                        ${isPercentageBased ? 
                            `max="${100 - progressSoFar}"` : 
                            'disabled'}
                        value="0"
                        style="width: 100px; margin: 0 auto;">
                    ${isPercentageBased ? 
                        `<div class="input-help-text">الحد الأقصى: ${100 - progressSoFar}%</div>` : 
                        `<div class="input-help-text">الحد الأقصى: 0 يوم</div>`}
                </td>
                <td class="text-center total-progress">
                    ${progressSoFar}${isPercentageBased ? '%' : ''}
                </td>
                <td class="text-center">
                    <input type="text" 
                        class="form-control task-notes" 
                        data-task-index="${index}"
                        value="-"
                        ${!isPercentageBased ? 'disabled' : ''}
                        placeholder="أدخل ملاحظاتك هنا">
                </td>
            `;
            tbody.appendChild(row);
            
            // Add event listeners for inputs
            const progressInput = row.querySelector('.current-progress');
            const notesInput = row.querySelector('.task-notes');
            
            progressInput.addEventListener('input', function() {
                let value = parseFloat(this.value) || 0;
                const progressSoFar = parseFloat(row.querySelector('.previous-progress').textContent) || 0;
                
                if (isPercentageBased) {
                    // Validate percentage input
                    const maxAllowed = 100 - progressSoFar;
                    if (value > maxAllowed) {
                        value = maxAllowed;
                        this.value = maxAllowed;
                        showInputWarning(this, `لا يمكن أن تتجاوز نسبة الإنجاز ${maxAllowed}%`);
                    }
                } else {
                    // Validate workdays input
                    const actualWorkdays = parseInt(document.getElementById('actual-workdays').value) || 0;
                    if (value > actualWorkdays) {
                        value = actualWorkdays;
                        this.value = actualWorkdays;
                        showInputWarning(this, `لا يمكن أن يتجاوز عدد أيام العمل ${actualWorkdays} يوم`);
                    }
                }
                updateProgress(this, isPercentageBased);
                validateForm();
            });
            
            notesInput.addEventListener('input', function() {
                updateNotes(this);
                validateForm();
            });
        });

        // Show and update the JSON display section
        updateJsonDisplay();
        validateForm();
    }

    function showInputWarning(inputElement, message) {
        // Remove any existing warning
        const existingWarning = inputElement.parentElement.querySelector('.warning-message');
        if (existingWarning) {
            existingWarning.remove();
        }

        // Create and show new warning
        const warningDiv = document.createElement('div');
        warningDiv.className = 'warning-message text-danger mt-1';
        warningDiv.style.fontSize = '0.875rem';
        warningDiv.textContent = message;
        
        // Insert warning after the input
        inputElement.parentElement.appendChild(warningDiv);
        
        // Remove warning after 3 seconds
        setTimeout(() => {
            warningDiv.remove();
        }, 3000);
    }

    function updateProgress(input, isPercentageBased) {
        const taskIndex = input.dataset.taskIndex;
        const row = input.closest('tr');
        const currentProgress = parseFloat(input.value) || 0;
        
        // Get the progress so far from the display (previous-progress cell)
        const progressSoFar = parseFloat(row.querySelector('.previous-progress').textContent) || 0;
        
        // Calculate new total by adding current progress to progress so far
        let total = progressSoFar + currentProgress;
        
        if (isPercentageBased) {
            // Ensure total doesn't exceed 100%
            total = Math.min(100, total);
            
            // Update the max attribute and help text to reflect remaining percentage
            const maxRemaining = 100 - progressSoFar;
            input.max = maxRemaining;
            const helpText = row.querySelector('.input-help-text');
            if (helpText) {
                helpText.textContent = `الحد الأقصى: ${maxRemaining}%`;
            }
        }
        
        // Update total display in table
        row.querySelector('.total-progress').textContent = isPercentageBased ? total + '%' : total;
        
        // Update the task in the JSON data
        window.currentTodoList.jobDetails.tasks[taskIndex].completionRate = currentProgress;
        window.currentTodoList.jobDetails.tasks[taskIndex].total = total;
        
        // Update JSON display
        updateJsonDisplay();
    }
    
    // Replace the existing updateNotes function with this one
    function updateNotes(input) {
        const taskIndex = input.dataset.taskIndex;
        const newNote = input.value.trim() || "-";
        
        // Update only the specific task's notes
        window.currentTodoList.jobDetails.tasks[taskIndex].notes = newNote;
        
        // Update JSON display
        updateJsonDisplay();
    }
    
    // Add Save Achievement Button HTML with tooltip
    const savedDataTable = document.querySelector('#saved-data-row').closest('.table-responsive');
    if (savedDataTable) {
        const saveButton = document.createElement('div');
        saveButton.className = 'text-center mt-4';
        saveButton.innerHTML = `
            <button type="button" id="save-achievement-btn" class="btn btn-primary btn-lg" disabled
                    title="يجب إدخال قيمة واحدة على الأقل في عمود نسبة الإنجاز أو أيام العمل للفترة المحددة">
                <i class="bi bi-save"></i>
                حفظ تقرير الإنجاز
            </button>
        `;
        savedDataTable.after(saveButton);
    }

    function validateForm() {
        const saveButton = document.getElementById('save-achievement-btn');
        const progressInputs = document.querySelectorAll('.current-progress');
        const actualWorkdaysInput = document.getElementById('actual-workdays');
        const notesInputs = document.querySelectorAll('.task-notes');
        
        // Check if we're in workdays mode (actual workdays input exists)
        const isWorkdaysMode = actualWorkdaysInput !== null;
        
        // Check if at least one progress input has a value greater than 0
        let hasProgress = false;
        progressInputs.forEach(input => {
            if (parseFloat(input.value) > 0) {
                hasProgress = true;
            }
        });

        // Check if at least one note has been entered (not equal to "-")
        let hasNotes = false;
        notesInputs.forEach(input => {
            if (input.value.trim() !== "-") {
                hasNotes = true;
            }
        });
        
        if (saveButton) {
            if (isWorkdaysMode) {
                // For workdays mode, require either actual workdays and progress, or notes
                const actualWorkdaysValue = parseInt(actualWorkdaysInput.value) || 0;
                const isValid = hasNotes || (actualWorkdaysValue > 0 && hasProgress);
                saveButton.disabled = !isValid;
                
                if (!isValid) {
                    if (!hasNotes && actualWorkdaysValue <= 0) {
                        saveButton.title = 'يجب إدخال أيام العمل الفعلية أو إضافة ملاحظات';
                    } else if (!hasNotes && !hasProgress) {
                        saveButton.title = 'يجب إدخال قيمة واحدة على الأقل في عمود أيام العمل للفترة المحددة أو إضافة ملاحظات';
                    } else {
                        saveButton.title = '';
                    }
                } else {
                    saveButton.title = '';
                }
            } else {
                // For percentage mode, require either progress or notes
                saveButton.disabled = !(hasProgress || hasNotes);
                if (!hasProgress && !hasNotes) {
                    saveButton.title = 'يجب إدخال قيمة واحدة على الأقل في عمود نسبة الإنجاز للفترة المحددة أو إضافة ملاحظات';
                } else {
                    saveButton.title = '';
                }
            }
        }
    }

    // Handle save button click
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'save-achievement-btn') {
            // Get all the necessary data
            const projectId = document.getElementById('project-number').textContent;
            const contractId = document.getElementById('contract-number').textContent;
            const extensionId = document.getElementById('extension-number').textContent;
            const attendanceId = document.getElementById('attendance-number').textContent;
            const startDate = document.getElementById('start-date').textContent;
            const endDate = document.getElementById('end-date').textContent;
            const todoListData = document.getElementById('json_display').value;

            // Validate data
            if (projectId === '-' || contractId === '-' || attendanceId === '-' || 
                startDate === '-' || endDate === '-' || !todoListData) {
                alert('يرجى التأكد من اكتمال جميع البيانات المطلوبة');
                return;
            }

            // Function to convert date from MM/DD/YYYY to YYYY-MM-DD
            function formatDateForMySQL(dateStr) {
                const [month, day, year] = dateStr.split('/');
                return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            }

            // Prepare the data for submission
            const formData = new FormData();
            formData.append('id_Project', projectId);
            formData.append('id_contract', contractId);
            // Handle extension ID - send null if it's '-' or empty
            formData.append('id_extension_contract', extensionId && extensionId !== '-' ? extensionId : '');
            formData.append('id_permanent_diapers', attendanceId);
            formData.append('start_date_achievement_reports', formatDateForMySQL(startDate));
            formData.append('end_date_achievement_reports', formatDateForMySQL(endDate));
            formData.append('data_todo_list_achievement', todoListData);

            // Add actual working days value if in workdays mode
            const actualWorkdaysInput = document.getElementById('actual-workdays');
            const isWorkdaysMode = actualWorkdaysInput !== null;
            if (isWorkdaysMode) {
                formData.append('actual_working_days', actualWorkdaysInput.value);
            } else {
                formData.append('actual_working_days', '');  // Send empty string for NULL in percentage mode
            }

            // Send the data to the server
            fetch('ajax/save_achievement_report.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حفظ تقرير الإنجاز بنجاح');
                    // Reset the page by reloading
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء حفظ التقرير: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ أثناء الاتصال بالخادم: ' + error);
            });
        }
    });
    
    function updateJsonDisplay() {
        const jsonDisplay = document.getElementById('json_display');
        const jsonDisplaySection = document.getElementById('json-display-section');
        
        if (window.currentTodoList) {
            jsonDisplaySection.style.display = 'block';
            jsonDisplay.value = JSON.stringify(window.currentTodoList, null, 2);
        }
    }

    function updateSavedDataSection() {
        // Get project number from the selected project option
        const projectId = $('#project_id').val();
        
        // Get contract and extension details from the selectedItem
        const selectedItem = <?php echo json_encode($selectedItem ?? null); ?>;
        
        // Get start and end dates from the retention period inputs
        const startDate = $('#retention_start_date').val();
        const endDate = $('#retention_end_date').val();
        
        // Update the table cells
        $('#project-number').text(projectId ? projectId : '-');
        $('#contract-number').text(selectedItem?.type === 'contract' ? selectedItem.id_contract : 
                                 selectedItem?.type === 'extension' ? selectedItem.main_contract.id_contract : '-');
        $('#extension-number').text(selectedItem?.type === 'extension' ? selectedItem.id_extension_contract : '-');
        $('#attendance-number').text(selectedAttendanceRecordId || '-');
        $('#start-date').text(startDate || '-');
        $('#end-date').text(endDate || '-');
    }

    // Add event listeners to update saved data when dates are selected
    function updateSelectedPeriod() {
        if (selectedStartDate && selectedEndDate) {
            const startInput = document.getElementById('retention_start_date');
            const endInput = document.getElementById('retention_end_date');
            const periodDaysInput = document.getElementById('period_days');
            const createReportBtn = document.getElementById('create-report-btn');
            
            startInput.value = formatDate(selectedStartDate);
            endInput.value = formatDate(selectedEndDate);
            
            // Calculate the number of days between dates
            const timeDiff = Math.abs(selectedEndDate.getTime() - selectedStartDate.getTime());
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
            periodDaysInput.value = daysDiff + ' يوم';
            
            // Enable the create report button when dates are selected
            if (createReportBtn) {
                createReportBtn.disabled = false;
            }
            
            // Update the saved data section
            updateSavedDataSection();
        }
    }
    </script>
    <script>
        // Add event listener for collapse toggle button icon rotation
        $('#jsonDisplayCollapse').on('show.bs.collapse hide.bs.collapse', function (e) {
            const icon = $(this).siblings('.card-title').find('.bi');
            if (e.type === 'show') {
                icon.removeClass('bi-chevron-down').addClass('bi-chevron-up');
            } else {
                icon.removeClass('bi-chevron-up').addClass('bi-chevron-down');
            }
        });
    </script>
</body>
</html>


