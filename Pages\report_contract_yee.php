<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    $employee_data = null;
    $error_message = '';
    $success_message = '';

    // Handle search request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search'])) {
        $conditions = [];
        $params = [];
        $types = '';

        // Build search conditions
        if (!empty($_POST['name_search'])) {
            $searchTerm = '%' . $_POST['name_search'] . '%';
            $conditions[] = "(name_ar_contract LIKE ? OR name_en_contract LIKE ?)";
            $params = array_merge($params, [$searchTerm, $searchTerm]);
            $types .= 'ss';
        }

        if (!empty($_POST['identity_number'])) {
            $conditions[] = "Identity_number_contract = ?";
            $params[] = $_POST['identity_number'];
            $types .= 's';
        }

        if (!empty($_POST['phone_number'])) {
            $conditions[] = "phone_number = ?";
            $params[] = $_POST['phone_number'];
            $types .= 'i';
        }

        if (!empty($_POST['status'])) {
            $conditions[] = "status = ?";
            $params[] = $_POST['status'];
            $types .= 'i';
        }

        // Base SQL query
        $sql = "SELECT * FROM employees";

        // Add search conditions if any
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Add ORDER BY clause
        $sql .= " ORDER BY `add` DESC";

        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $search_results = [];
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $search_results[] = $row;
            }
            $success_message = 'تم العثور على ' . count($search_results) . ' موظف';
        } else {
            $error_message = 'لم يتم العثور على أي موظفين يطابقون معايير البحث';
        }

        $stmt->close();

        // If a specific employee is selected
        if (isset($_POST['selected_employee'])) {
            $employee_id = (int)$_POST['selected_employee'];
            
            $sql = "SELECT * FROM employees WHERE id_employees = ?";
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Query preparation failed: " . $conn->error);
            }
            
            $stmt->bind_param("i", $employee_id);
            
            if (!$stmt->execute()) {
                throw new Exception("Query execution failed: " . $stmt->error);
            }

            $result = $stmt->get_result();
            if ($result && $result->num_rows > 0) {
                $employee_data = $result->fetch_assoc();
                $success_message = 'تم العثور على بيانات الموظف';
            } else {
                $error_message = 'لم يتم العثور على بيانات الموظف';
            }
            $stmt->close();
        }
    }

} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

function formatDate($date) {
    if (empty($date)) return "غير محدد";
    return date("Y/m/d", strtotime($date));
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير عقد العمل - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        body {
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif;
        }

        /* Update sidebar positioning */
        #sidebar {
            right: 0;
            left: auto;
        }

        /* Update main content positioning */
        #content {
            margin-right: 280px;
            margin-left: 0;
        }

        /* Update dropdown menu positioning */
        .dropdown-menu {
            text-align: right;
        }

        /* Update input groups */
        .input-group > .form-control {
            border-radius: 0 0.375rem 0.375rem 0 !important;
        }

        .input-group > .input-group-text {
            border-radius: 0.375rem 0 0 0.375rem !important;
        }

        /* Update icons in buttons */
        .btn i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Update modal positioning */
        .modal-header .btn-close {
            margin: -0.5rem auto -0.5rem -0.5rem;
        }

        /* Update search icon positioning */
        .search-icon {
            right: 1rem;
            left: auto;
        }

        .search-input {
            padding-right: 2.5rem;
            padding-left: 1rem;
        }

        /* Update document cards */
        .document-status {
            left: 1.25rem;
            right: auto;
        }

        /* Update extension items */
        .extension-item {
            padding-right: 2rem !important;
            padding-left: 1rem !important;
            margin-right: 2rem;
            margin-left: 0;
            border-right: 4px solid var(--border-color);
            border-left: none;
        }

        .extension-item::before {
            right: -2rem;
            left: auto;
        }

        /* Update Select2 RTL support */
        .select2-container--bootstrap-5 .select2-selection {
            text-align: right;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding-right: 0.75rem;
            padding-left: 2.25rem;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            left: 0.75rem;
            right: auto;
        }

        /* Update form controls */
        .form-control {
            text-align: right;
        }

        /* Update badges and status indicators */
        .badge {
            margin-right: 0;
            margin-left: 0.5rem;
        }

        /* Update info groups */
        .info-group {
            text-align: right;
        }

        /* Update document actions */
        .document-actions {
            flex-direction: row-reverse;
        }

        /* Update alert positioning */
        .alert {
            text-align: right;
        }

        .alert .btn-close {
            margin-right: auto;
            margin-left: -0.5rem;
        }

        /* Update table alignments */
        .table th,
        .table td {
            text-align: right;
        }

        /* Update progress bars */
        .progress-bar {
            float: right;
        }

        /* Update list items */
        .list-group-item {
            text-align: right;
        }

        /* Update quick filters */
        .quick-filters .btn i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Update document upload forms */
        .upload-zone-text {
            text-align: center;
        }

        /* Update section titles */
        .section-title i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Update result items */
        .result-item-info i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Update theme toggle button */
        #theme-toggle {
            left: 1rem;
            right: auto;
        }

        /* Updated styles for better dark theme compatibility */
        .search-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 2px 15px var(--shadow-color);
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filters {
            padding: 1rem;
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filter-item {
            min-width: 150px;
            position: relative;
        }

        .quick-filter-item .form-select,
        .quick-filter-item .select2-container .select2-selection--single {
            border-radius: 20px;
            padding: 0.4rem 2rem 0.4rem 1rem;
            border: 1px solid var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .quick-filter-item .form-select:hover,
        .quick-filter-item .select2-container .select2-selection--single:hover {
            border-color: var(--primary-color);
        }

        .quick-filter-item .form-select:focus,
        .quick-filter-item .select2-container .select2-selection--single:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .advanced-filters-container {
            padding: 1.5rem;
            background: var(--bg-main);
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .filter-group {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            box-shadow: 0 1px 3px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .filter-group-title {
            color: var(--text-color);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-group-title i {
            color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .form-control {
            background-color: var(--bg-input);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .form-control:focus {
            background-color: var(--bg-input);
            border-color: var(--primary-color);
            color: var(--text-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
            border-radius: 20px;
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Results styles with dark theme support */
        .results-container {
            margin-top: 1.5rem;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .results-header {
            position: sticky;
            top: 0;
            background: var(--bg-main);
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            z-index: 1;
            transition: all 0.3s ease;
        }

        .results-search input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-input);
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .results-search input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .result-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-card);
        }

        .result-item:hover {
            background: var(--hover-color);
        }

        .result-item-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .result-item-info {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .select-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Select2 Dark Theme Adjustments */
        .select2-container--bootstrap-5 .select2-selection {
            background-color: var(--bg-input) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--bg-card) !important;
            border-color: var(--border-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            color: var(--text-color) !important;
            background-color: var(--bg-card) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--hover-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: var(--primary-color) !important;
            color: white !important;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .search-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .quick-filters {
            background: var(--bg-card);
        }

        [data-theme="dark"] .advanced-filters-container {
            background: var(--bg-main);
        }

        [data-theme="dark"] .filter-group {
            background: var(--bg-card);
        }

        [data-theme="dark"] input[type="date"] {
            color-scheme: dark;
        }

        /* Scrollbar styling */
        .results-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .results-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .quick-filter-item {
                min-width: 120px;
            }
            
            .filter-group {
                margin-bottom: 1rem;
            }
            
            .advanced-filters-container {
                padding: 1rem;
            }
            
            .filter-actions {
                justify-content: center;
            }
        }

        /* Add these styles for the Contract Details section */
        .report-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 2px 15px var(--shadow-color);
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            color: var(--text-color);
            margin: 0;
            font-weight: 600;
        }

        .report-header .btn-print {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .report-header .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .report-section {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1.25rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            background: var(--bg-card);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        .info-value.text-primary {
            color: var(--primary-color) !important;
        }

        .info-value.text-success {
            color: var(--success-color) !important;
        }

        .badge {
            padding: 0.5em 0.75em;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .badge-primary {
            background: var(--primary-color);
            color: white;
        }

        .badge-success {
            background: var(--success-color);
            color: white;
        }

        .task-list {
            display: grid;
            gap: 1rem;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .task-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .task-name-en {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .task-progress {
            width: 100px;
            background: var(--bg-main);
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-value {
            height: 6px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .progress-label {
            color: var(--text-muted);
            font-size: 0.75rem;
            text-align: center;
            margin-top: 0.25rem;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .report-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .report-section {
            background: var(--bg-main);
        }

        [data-theme="dark"] .info-item {
            background: var(--bg-card);
        }

        [data-theme="dark"] .task-item {
            background: var(--bg-card);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .report-container {
                margin: 0;
                padding: 0;
                box-shadow: none;
            }

            .report-section {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }

        .extension-item {
            padding-right: 2rem !important;
            position: relative;
            font-size: 0.95em;
            background-color: var(--bg-secondary) !important;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: 1rem;
            top: 50%;
            width: 0.5rem;
            height: 1px;
            background-color: var(--text-muted);
        }

        .result-item-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .result-item:not(.extension-item) {
            margin-top: 0.5rem;
        }

        .result-item:not(.extension-item) + .extension-item {
            margin-top: 0.25rem;
        }

        .tasks-container {
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.5rem;
        }

        .task-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-color);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-size: 0.95rem;
            line-height: 1.4;
        }

        /* Scrollbar styling */
        .tasks-container::-webkit-scrollbar {
            width: 6px;
        }

        .tasks-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Add these styles to your existing style section */
        .scrollable-section {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--bg-main);
        }

        .scrollable-section::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-section::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .scrollable-section::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .search-box {
            margin-left: 1rem;
        }

        .list-group-item {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            background-color: var(--hover-color);
        }

        .list-group-item h6 {
            color: var(--text-color);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .list-group-item small {
            font-size: 0.8rem;
        }

        /* Add these new styles */
        .inner-section {
            background: var(--bg-main);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .inner-section .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.35em 0.65em;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        /* Add these new styles */
        .stats-pills {
            display: flex;
            gap: 1rem;
        }

        .stat-pill {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.35rem 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 0.875rem;
        }

        .stat-pill i {
            color: var(--primary-color);
        }

        .stat-value {
            font-weight: 600;
            color: var(--text-color);
        }

        .stat-label {
            color: var(--text-muted);
        }

        .nav-tabs-custom {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background: var(--bg-main);
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .tab-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: var(--text-muted);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .tab-btn:hover {
            color: var(--text-color);
            background: var(--hover-color);
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .tab-content-wrapper {
            background: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .tab-content {
            display: none;
            height: 400px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .search-box {
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .search-input {
            padding-left: 2.5rem;
            background: var(--bg-main);
        }

        .scrollable-section {
            height: calc(100% - 65px);
            overflow-y: auto;
            padding: 1rem;
        }

        .list-item {
            padding: 1rem;
            border-radius: 8px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
        }

        .item-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .item-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 8px;
            flex-shrink: 0;
        }

        .item-details {
            flex: 1;
        }

        .item-title {
            color: var(--text-color);
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .item-meta {
            color: var(--text-muted);
            font-size: 0.8rem;
            display: flex;
            gap: 1rem;
        }

        .item-meta i {
            margin-right: 0.25rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
        }

        .btn-icon:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        /* Add to the existing style section */
        .extension-list-item {
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .extension-list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .extension-list-item .item-icon {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .extension-list-item .item-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .extension-list-item .btn-icon:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Add to your existing styles */
        .result-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .result-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-left: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        .extension-item {
            margin-left: 2rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            left: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .result-item-details {
            flex: 1;
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            border-radius: 4px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .select-contract-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.25em 0.5em;
            border-radius: 4px;
        }

        /* Update these styles in your existing style section */

        .extension-item {
            margin-right: 2rem;
            margin-bottom: 0.5rem;
            border-right: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-right: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        /* Add new style for the arrow icon */
        .extension-item .bi-arrow-return-right {
            transform: scaleX(-1);  /* Flip the arrow icon horizontally */
        }

        /* Optional: Add a connecting line from bottom extension to top extension */
        .extension-item::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -2rem;
            width: 2px;
            height: calc(-100% - 0.5rem);
            background-color: var(--border-color);
            z-index: 0;
        }

        /* Remove the vertical line from the first extension */
        .extension-item:first-child::after {
            display: none;
        }

        /* Add hover effect for the connection lines */
        .extension-item:hover::before,
        .extension-item:hover::after {
            background-color: var(--primary-color);
        }

        /* Add these styles to make the modal content more readable */
        .modal-xl {
            max-width: 90%; /* Makes modal wider */
        }
        
        .modal-body {
            padding: 2rem;
        }
        
        .report-details .section {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .info-item {
            background: var(--bg-main);
            padding: 1rem;
            border-radius: 6px;
            height: 100%;
        }
        
        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        /* Modal Styling */
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Modal Header Styling */
        .modal-header {
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header .modal-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            color: var(--text-color);
            margin: 0;
            order: 1; /* Changed from 2 to 1 */
        }

        .modal-header .modal-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .modal-header .btn-close {
            order: 2; /* Changed from 1 to 2 */
            margin: 0;
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-main);
            border-radius: 8px;
            opacity: 1;
            transition: all 0.2s ease;
            position: relative;
        }

        /* Custom close button design */
        .modal-header .btn-close::before,
        .modal-header .btn-close::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 2px;
            background-color: var(--text-color);
            border-radius: 1px;
            transition: all 0.2s ease;
        }

        .modal-header .btn-close::before {
            transform: rotate(45deg);
        }

        .modal-header .btn-close::after {
            transform: rotate(-45deg);
        }

        .modal-header .btn-close:hover {
            background-color: var(--hover-color);
            transform: rotate(90deg);
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .modal-header .btn-close {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] .modal-header .btn-close::before,
        [data-theme="dark"] .modal-header .btn-close::after {
            background-color: var(--text-muted);
        }

        [data-theme="dark"] .modal-header .btn-close:hover {
            background-color: var(--primary-color);
        }

        [data-theme="dark"] .modal-header .btn-close:hover::before,
        [data-theme="dark"] .modal-header .btn-close:hover::after {
            background-color: white;
        }

        .modal-body {
            padding: 2rem;
            background: var(--bg-main);
        }

        /* Report Details Styling */
        .report-details {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .report-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .report-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-header i {
            color: var(--primary-color);
            font-size: 1.25rem;
            background: var(--primary-light);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .section-header h6 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--text-color);
        }

        /* Info Grid Styling */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            background: var(--hover-color);
            border-color: var(--primary-color);
        }

        .info-card .label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-card .value {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 500;
        }

        /* Task List Styling */
        .task-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .task-item {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.75rem;
        }

        .progress-wrapper {
            background: var(--bg-card);
            border-radius: 6px;
            padding: 0.75rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-muted);
            font-size: 0.75rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 48px;
            height: 48px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Achievement Timeline Report Table Styles */
        .achievement-schedule-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 0;
            border: 1px solid var(--border-color);
        }

        .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            white-space: nowrap;
            position: relative;
        }

        .achievement-schedule-table tbody td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
            transition: all 0.2s ease;
        }

        .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Task name column specific styling */
        .achievement-schedule-table td:first-child {
            text-align: right;
            font-weight: 500;
            color: var(--primary-color);
        }

        /* Percentage/Days values styling */
        .achievement-schedule-table td:not(:first-child):not(:last-child) {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Notes column styling */
        .achievement-schedule-table td:last-child {
            color: var(--text-muted);
            font-style: italic;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .achievement-schedule-table {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border-bottom-color: var(--primary-color);
        }

        [data-theme="dark"] .achievement-schedule-table tbody td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Responsive table container */
        .achievement-schedule-container {
            width: 100%;
            overflow-x: auto;
            border-radius: 8px;
            position: relative;
        }

        /* Custom scrollbar styling */
        .achievement-schedule-container::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .achievement-schedule-container::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Value highlighting */
        .value-percentage {
            color: var(--success-color);
            background: var(--success-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        .value-days {
            color: var(--info-color);
            background: var(--info-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .achievement-schedule-table thead th,
            .achievement-schedule-table tbody td {
                padding: 0.75rem;
                font-size: 0.9rem;
            }
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            margin-bottom: 0.25rem;
        }

        .status-badge:empty::after {
            content: "-";
            color: var(--text-muted);
        }

        .status-badge.present {
            background-color: var(--success-bg);
            color: var(--success-color);
        }

        .time-display {
            font-size: 0.875rem;
            color: var(--text-primary);
            text-align: center;
        }

        .time-display:empty::after {
            content: "00:00";
            color: var(--text-muted);
        }

        .table th {
            text-align: center;
            vertical-align: middle;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            white-space: nowrap;
        }

        .table td {
            text-align: center;
            vertical-align: middle;
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        /* Attendance Tables Styling */
        .attendance-summary-table,
        .attendance-details-table {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
        }

        .attendance-summary-table th,
        .attendance-details-table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            border-color: var(--border-color);
            text-align: center;
            white-space: nowrap;
        }

        .attendance-summary-table td,
        .attendance-details-table td {
            background-color: var(--bg-card);
            color: var(--text-primary);
            border-color: var(--border-color);
            padding: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .attendance-details-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Status Badge Styling */
        .status-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.35rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            min-width: 80px;
        }

        .status-badge.present {
            background-color: var(--success-light);
            color: var(--success-color);
        }

        .status-badge.absent {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }

        .status-badge.leave {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }

        /* Time Display Styling */
        .time-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .time-value {
            font-family: monospace;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        /* Dark Theme Specific Adjustments */
        [data-theme="dark"] .attendance-summary-table,
        [data-theme="dark"] .attendance-details-table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] .attendance-summary-table th,
        [data-theme="dark"] .attendance-details-table th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .attendance-summary-table td,
        [data-theme="dark"] .attendance-details-table td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.absent {
            background-color: rgba(var(--danger-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.leave {
            background-color: rgba(var(--warning-rgb), 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .attendance-summary-table,
            .attendance-details-table {
                font-size: 0.875rem;
            }

            .attendance-summary-table th,
            .attendance-details-table th,
            .attendance-summary-table td,
            .attendance-details-table td {
                padding: 0.625rem;
            }

            .status-badge {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                min-width: 60px;
            }
        }

        /* Add these styles to the existing style section */
        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .report-header .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .report-header .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .report-header .btn i {
            font-size: 1rem;
        }

        .document-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            transition: all 0.3s ease;
        }

        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .document-title {
            color: var(--text-color);
            font-size: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .document-title i {
            color: var(--primary-color);
        }

        /* Document Section Specific Styles */
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .document-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.25rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .document-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .document-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .document-card:hover::before {
            opacity: 1;
        }

        .document-icon {
            width: 42px;
            height: 42px;
            background: var(--primary-light);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .document-icon i {
            font-size: 1.25rem;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .document-card:hover .document-icon {
            background: var(--primary-color);
        }

        .document-card:hover .document-icon i {
            color: white;
        }

        .document-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .document-status {
            position: absolute;
            top: 1.25rem;
            right: 1.25rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .document-status.uploaded {
            background: var(--success-color);
        }

        .document-status.not-uploaded {
            background: var(--text-muted);
        }

        .document-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .upload-zone {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-zone:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .upload-zone input[type="file"] {
            display: none;
        }

        .upload-zone-text {
            text-align: center;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .upload-zone:hover .upload-zone-text {
            color: var(--primary-color);
        }

        .btn-document {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .btn-document.btn-upload {
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
        }

        .btn-document.btn-upload:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-document.btn-download {
            background: var(--bg-main);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-document.btn-download:hover {
            background-color: #27ae60;
        }
        
        .btn-document.btn-view {
            background-color: #3498db;
            color: white;
        }
        
        .btn-document.btn-view:hover {
            background-color: #2980b9;
        }
        
        .document-upload-form.uploading .btn-upload {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Loading animation */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .uploading .document-icon {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Main Container -->
                    <div class="search-container">
                        <!-- Quick Filters -->
                        <div class="quick-filters">
                            <form method="post" id="searchForm" class="mb-0">
                                <div class="d-flex flex-column gap-3">
                                    <!-- Primary Filters -->
                                    <div class="d-flex align-items-center gap-2 flex-wrap">
                                        <div class="quick-filter-item">
                                            <select class="form-select form-select-sm select2" id="status" name="status">
                                                <option value="">حالة الموظف</option>
                                                <option value="1">نشط</option>
                                                <option value="2">معلق</option>
                                            </select>
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                                            <i class="bi bi-funnel"></i> فلترة متقدمة
                                        </button>
                                    </div>

                                    <!-- Employee Information -->
                                    <div class="filter-group mb-0">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-person"></i> معلومات الموظف
                                        </h6>
                                        <div class="d-flex gap-2 flex-wrap">
                                            <div class="flex-grow-1">
                                                <input type="text" class="form-control form-control-sm" id="name_search" name="name_search" placeholder="البحث عن الموظف (الاسم بالعربية أو الإنجليزية)">
                                            </div>
                                            <div>
                                                <button type="submit" name="search" class="btn btn-primary btn-sm">
                                                    <i class="bi bi-search"></i> بحث
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced Filters -->
                                <div class="collapse" id="advancedFilters">
                                    <div class="advanced-filters-container">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="filter-group">
                                                    <h6 class="filter-group-title">
                                                        <i class="bi bi-person-vcard"></i> معلومات الهوية
                                                    </h6>
                                                    <div class="input-group input-group-sm">
                                                        <input type="text" name="identity_number" class="form-control" placeholder="رقم الهوية">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="filter-group">
                                                    <h6 class="filter-group-title">
                                                        <i class="bi bi-telephone"></i> معلومات الاتصال
                                                    </h6>
                                                    <div class="input-group input-group-sm">
                                                        <input type="text" name="phone_number" class="form-control" placeholder="رقم الهاتف">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Results Section -->
                        <?php if (isset($search_results) && !empty($search_results)): ?>
                        <div class="results-container">
                            <div class="results-header">
                                <div class="results-search">
                                    <input type="text" id="resultSearch" placeholder="البحث في النتائج..." class="form-control">
                                </div>
                            </div>
                            <div class="results-list">
                                <?php foreach ($search_results as $employee): ?>
                                    <div class="result-item" data-search-text="<?= htmlspecialchars($employee['name_ar_contract'] . ' ' . $employee['name_en_contract'] . ' ' . $employee['Identity_number_contract']) ?>">
                                        <div class="result-item-details">
                                            <div class="result-item-name">
                                                <i class="bi bi-person text-primary me-2"></i>
                                                <?= htmlspecialchars($employee['name_ar_contract']) ?>
                                                <small class="text-muted">(<?= htmlspecialchars($employee['name_en_contract']) ?>)</small>
                                            </div>
                                            <div class="result-item-info small text-muted mt-1">
                                                <span class="me-3">
                                                    <i class="bi bi-person-vcard"></i>
                                                    <?= htmlspecialchars($employee['Identity_number_contract']) ?>
                                                </span>
                                                <span>
                                                    <i class="bi bi-telephone"></i>
                                                    <?= htmlspecialchars($employee['phone_number'] ?? 'غير متوفر') ?>
                                                </span>
                                                <span class="ms-3">
                                                    <i class="bi bi-circle-fill <?= $employee['status'] == 1 ? 'text-success' : 'text-warning' ?>"></i>
                                                    <?= $employee['status'] == 1 ? 'نشط' : 'معلق' ?>
                                                </span>
                                            </div>
                                        </div>
                                        <form method="post" style="display: inline;">
                                            <input type="hidden" name="search" value="1">
                                            <?php foreach ($_POST as $key => $value): ?>
                                                <?php if ($key !== 'selected_employee'): ?>
                                                    <input type="hidden" name="<?= htmlspecialchars($key) ?>" 
                                                           value="<?= htmlspecialchars($value) ?>">
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                            <input type="hidden" name="selected_employee" value="<?= $employee['id_employees'] ?>">
                                            <button type="submit" class="select-contract-btn">
                                                <i class="bi bi-eye me-1"></i>عرض
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if (isset($_POST['selected_employee']) && $employee_data): ?>
                    <div class="row">
                        <!-- Right Half - Employee Details -->
                        <div class="col-md-6">
                            <div class="report-container">
                                <!-- Employee Information -->
                                <div class="report-section">
                                    <h6 class="section-title mb-3">معلومات الموظف</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <div class="info-label">الاسم بالعربية</div>
                                                <div class="info-value"><?php echo htmlspecialchars($employee_data['name_ar_contract']); ?></div>
                                            </div>
                                            <div class="info-group mt-3">
                                                <div class="info-label">الاسم بالإنجليزية</div>
                                                <div class="info-value"><?php echo htmlspecialchars($employee_data['name_en_contract']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <div class="info-label">رقم الهاتف</div>
                                                <div class="info-value"><?php echo htmlspecialchars($employee_data['phone_number'] ?? 'غير متوفر'); ?></div>
                                            </div>
                                            <div class="info-group mt-3">
                                                <div class="info-label">العنوان</div>
                                                <div class="info-value"><?php echo htmlspecialchars($employee_data['address'] ?? 'غير متوفر'); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Identity Information -->
                                <div class="report-section">
                                    <h6 class="section-title">معلومات الهوية</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">نوع الهوية</div>
                                                <div class="info-value">
                                                    <?php echo htmlspecialchars($employee_data['Identity_contract_ar']); ?>
                                                    <div class="small text-muted">
                                                        <?php echo htmlspecialchars($employee_data['Identity_contract_en']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">رقم الهوية</div>
                                                <div class="info-value"><?php echo htmlspecialchars($employee_data['Identity_number_contract']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-group">
                                                <div class="info-label">مكان الإصدار</div>
                                                <div class="info-value">
                                                    <?php echo htmlspecialchars($employee_data['Identity_issue_contract_ar']); ?>
                                                    <div class="small text-muted">
                                                        <?php echo htmlspecialchars($employee_data['Identity_issue_contract_en']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="info-group">
                                                <div class="info-label">تاريخ إصدار الهوية</div>
                                                <div class="info-value"><?php echo formatDate($employee_data['Identity_issue_date_contract']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="report-section">
                                    <h6 class="section-title">معلومات إضافية</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <div class="info-label">طريقة الاستقطاب</div>
                                                <div class="info-value"><?php echo htmlspecialchars($employee_data['polarization_method']); ?></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <div class="info-label">الحالة</div>
                                                <div class="info-value">
                                                    <span class="badge <?php echo $employee_data['status'] == 1 ? 'bg-success' : 'bg-warning'; ?>">
                                                        <?php echo $employee_data['status'] == 1 ? 'نشط' : 'معلق'; ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Left Half - Documents -->
                        <div class="col-md-6">
                            <!-- Documents Section -->
                            <div class="report-container">
                                <div class="report-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-file-earmark-text"></i>
                                        المستندات
                                    </h5>
                                </div>
                                <div class="documents-grid">
                                    <!-- Identity Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['Identity_document']) && $employee_data['Identity_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-person-badge"></i>
                                        </div>
                                        <h6 class="document-title">مستند الهوية</h6>
                                        <form class="document-upload-form" data-document-type="Identity_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="Identity_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['Identity_document']) && $employee_data['Identity_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=Identity_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=Identity_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Test Results Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['test_results_document']) && $employee_data['test_results_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-file-text"></i>
                                        </div>
                                        <h6 class="document-title">نتائج الاختبار</h6>
                                        <form class="document-upload-form" data-document-type="test_results_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="test_results_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['test_results_document']) && $employee_data['test_results_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=test_results_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=test_results_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Interview Results Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['Interview_results_document']) && $employee_data['Interview_results_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-chat-text"></i>
                                        </div>
                                        <h6 class="document-title">نتائج المقابلة</h6>
                                        <form class="document-upload-form" data-document-type="Interview_results_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="Interview_results_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['Interview_results_document']) && $employee_data['Interview_results_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=Interview_results_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=Interview_results_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Guarantee Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['guarantee_document']) && $employee_data['guarantee_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-shield-check"></i>
                                        </div>
                                        <h6 class="document-title">مستند الضمان</h6>
                                        <form class="document-upload-form" data-document-type="guarantee_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="guarantee_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['guarantee_document']) && $employee_data['guarantee_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=guarantee_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=guarantee_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Medical Examination Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['medical_examination_document']) && $employee_data['medical_examination_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-heart-pulse"></i>
                                        </div>
                                        <h6 class="document-title">الفحص الطبي</h6>
                                        <form class="document-upload-form" data-document-type="medical_examination_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="medical_examination_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['medical_examination_document']) && $employee_data['medical_examination_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=medical_examination_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=medical_examination_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- CV Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['cv_document']) && $employee_data['cv_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-file-person"></i>
                                        </div>
                                        <h6 class="document-title">السيرة الذاتية</h6>
                                        <form class="document-upload-form" data-document-type="cv_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="cv_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['cv_document']) && $employee_data['cv_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=cv_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=cv_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Request for Resignation Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['request_for_resignation_document']) && $employee_data['request_for_resignation_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-file-earmark-text"></i>
                                        </div>
                                        <h6 class="document-title">طلب الاستقالة</h6>
                                        <form class="document-upload-form" data-document-type="request_for_resignation_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="request_for_resignation_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['request_for_resignation_document']) && $employee_data['request_for_resignation_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=request_for_resignation_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=request_for_resignation_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Reconciliation Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['reconciliation_document']) && $employee_data['reconciliation_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-file-check"></i>
                                        </div>
                                        <h6 class="document-title">مستند المخالصة</h6>
                                        <form class="document-upload-form" data-document-type="reconciliation_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="reconciliation_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['reconciliation_document']) && $employee_data['reconciliation_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=reconciliation_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=reconciliation_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Resignation Approval Document -->
                                    <div class="document-card">
                                        <div class="document-status <?php echo isset($employee_data['resignation_approval_document']) && $employee_data['resignation_approval_document'] ? 'uploaded' : 'not-uploaded'; ?>"></div>
                                        <div class="document-icon">
                                            <i class="bi bi-file-check"></i>
                                        </div>
                                        <h6 class="document-title">موافقة الاستقالة</h6>
                                        <form class="document-upload-form" data-document-type="resignation_approval_document">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee_data['id_employees']; ?>">
                                            <input type="hidden" name="document_type" value="resignation_approval_document">
                                            <label class="upload-zone">
                                                <input type="file" class="form-control" name="document_file" accept="application/pdf" required>
                                                <div class="upload-zone-text">
                                                    <i class="bi bi-cloud-arrow-up"></i>
                                                    <span>اختر ملف PDF للرفع</span>
                                                </div>
                                            </label>
                                            <div class="document-actions">
                                                <button type="submit" class="btn-document btn-upload">
                                                    <i class="bi bi-upload"></i> رفع
                                                </button>
                                                <?php if (isset($employee_data['resignation_approval_document']) && $employee_data['resignation_approval_document']): ?>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=resignation_approval_document&action=view" 
                                                       class="btn-document btn-view" target="_blank">
                                                        <i class="bi bi-eye"></i> عرض
                                                    </a>
                                                    <a href="get_report_contract_con/handle_employee_documents.php?employee_id=<?php echo $employee_data['id_employees']; ?>&document_type=resignation_approval_document" 
                                                       class="btn-document btn-download">
                                                        <i class="bi bi-download"></i> تحميل
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php elseif (isset($search_results) && !empty($search_results)): ?>
                    <div class="text-center mt-4 text-muted">
                        <i class="bi bi-arrow-up-circle fs-1"></i>
                        <p class="mt-2">الرجاء اختيار موظف من القائمة أعلاه لعرض التفاصيل</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for all select elements
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                language: {
                    noResults: function() {
                        return "لا توجد نتائج";
                    }
                }
            });

            // Handle document uploads
            $('.document-upload-form').on('submit', function(e) {
                e.preventDefault();
                
                const $form = $(this);
                const $card = $form.closest('.document-card');
                
                // Add uploading state
                $form.addClass('uploading');
                $card.find('.document-icon').addClass('uploading');
                
                const formData = new FormData();
                const fileInput = $form.find('input[type="file"]')[0];
                const employeeId = $form.find('input[name="employee_id"]').val();
                const documentType = $form.find('input[name="document_type"]').val();
                
                formData.append('document_file', fileInput.files[0]);
                formData.append('employee_id', employeeId);
                formData.append('document_type', documentType);

                $.ajax({
                    url: 'get_report_contract_con/handle_employee_documents.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        try {
                            const result = JSON.parse(response);
                            if (result.success) {
                                // Update status indicator
                                $card.find('.document-status').removeClass('not-uploaded').addClass('uploaded');
                                showAlert('success', 'تم رفع المستند بنجاح');
                                location.reload();
                            } else {
                                showAlert('error', 'حدث خطأ أثناء رفع الملف: ' + (result.error || 'خطأ غير معروف'));
                            }
                        } catch (e) {
                            showAlert('error', 'حدث خطأ غير متوقع');
                        }
                    },
                    error: function(xhr, status, error) {
                        showAlert('error', 'حدث خطأ أثناء رفع الملف: ' + error);
                    },
                    complete: function() {
                        // Remove uploading state
                        $form.removeClass('uploading');
                        $card.find('.document-icon').removeClass('uploading');
                    }
                });
            });

            // File input visual feedback
            $('.upload-zone input[type="file"]').on('change', function() {
                const fileName = this.files[0]?.name || '';
                const $uploadZone = $(this).closest('.upload-zone');
                const $text = $uploadZone.find('.upload-zone-text');
                
                if (fileName) {
                    $text.html(`<i class="bi bi-check-circle"></i><span>${fileName}</span>`);
                    $uploadZone.addClass('has-file');
                } else {
                    $text.html(`<i class="bi bi-cloud-arrow-up"></i><span>اختر ملف PDF للرفع</span>`);
                    $uploadZone.removeClass('has-file');
                }
            });

            // Helper function to show alerts
            function showAlert(type, message) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                
                // Remove any existing alerts
                $('.alert').remove();
                
                // Add the new alert at the top of the page
                $('.container-fluid').prepend(alertHtml);
                
                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    $('.alert').fadeOut('slow', function() {
                        $(this).remove();
                    });
                }, 5000);
            }

            // Search within results functionality
            $('#resultSearch').on('input', function() {
                const searchText = $(this).val().toLowerCase();
                $('.result-item').each(function() {
                    const itemText = $(this).data('search-text').toLowerCase();
                    $(this).toggle(itemText.includes(searchText));
                });
            });

            // Auto-scroll to contract details when a contract is selected
            <?php if (isset($_POST['selected_employee']) && $employee_data): ?>
                $('html, body').animate({
                    scrollTop: $('.report-container').offset().top - 20
                }, 300);
            <?php endif; ?>
        });
    </script>
</body>
</html> 

