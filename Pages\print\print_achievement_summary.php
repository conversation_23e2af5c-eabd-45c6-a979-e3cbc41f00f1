<?php
// Start output buffering at the very beginning
ob_start();

// Include the TCPDF library
require_once('../../TCPDF-6.6.2/tcpdf.php');

// Get parameters from the request
$data = json_decode($_POST['data'], true);

if (!$data) {
    die('No data provided');
}

// Fetch institution data
$institutionData = null;
try {
    // Connect to the database
    $file = fopen(__DIR__ . "/../../Pages/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch institution data
    $stmt = $db->query("SELECT * FROM documents LIMIT 1");
    $institutionData = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die('Error fetching institution data: ' . $e->getMessage());
}

// Process institution data
if ($institutionData) {
    $agency_name_ar = explode("\n", trim($institutionData['name_ar']));
    $address_ar = explode("\n", trim($institutionData['address_ar']));
    $agency_name_en = explode("\n", trim($institutionData['name_en']));
    $address_en = explode("\n", trim($institutionData['address_en']));
    $numbers = explode("\n", trim($institutionData['numbers']));
    $logoData = $institutionData['logo'];
}

// Clean any output before generating PDF
ob_end_clean();

// Extend TCPDF class to customize header and footer
class MYPDF extends TCPDF {
    protected $institutionData;
    
    public function setInstitutionData($data) {
        $this->institutionData = $data;
    }

    public function Header() {
        if (!$this->institutionData) return;

        // Save current position
        $x = $this->GetX();
        $y = $this->GetY();

        // Calculate widths with equal margins
        $pageWidth = $this->getPageWidth();
        $margin = 2; // Equal margin on both sides
        $logoWidth = 35; // Increased from 25 to 35
        
        // New header layout - Logo at the top center
        if ($this->institutionData['logo']) {
            $this->Image('@'.$this->institutionData['logo'], 
                ($pageWidth - $logoWidth) / 2,
                $y + 2,
                $logoWidth, 
                $logoWidth,
                '', '', '', false, 300, '', false, false, 0);
        }
        
        // Add space after logo
        $y_after_logo = $y + $logoWidth + 5;
        
        // Draw purple lines with text - similar to offer.php style
        // Top purple lines (3 lines)
        $lineY = $y_after_logo;
        $lineHeight = 0.5;
        $lineGap = 1;
        $purpleColor = array(128, 0, 128); // Purple RGB
        
        $this->SetLineWidth(0.2);
        $this->SetDrawColor($purpleColor[0], $purpleColor[1], $purpleColor[2]);
        
        // Draw top 3 purple lines
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Add text in the middle - only English text between lines
        $this->SetY($y_after_logo + 1.5);
        $this->SetFont('times', 'B', 10);
        $this->SetTextColor($purpleColor[0], $purpleColor[1], $purpleColor[2]); // Set text color to purple
        $this->Cell(0, 6, 'Assistance for Response and Development', 0, 1, 'L'); // Changed from 'C' to 'L' for left alignment
        $this->SetTextColor(0, 0, 0); // Reset text color to black for the rest of the document
        
        // Draw bottom 3 purple lines
        $lineY = $y_after_logo + 8;
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Set ending position
        $this->SetY($lineY + 5);
    }

    public function Footer() {
        $this->SetY(-25);
        $this->SetFont('aealarabiya', '', 9);
        $this->Cell(0, 10, 'توقيع الموظف                                  الموارد البشرية                                  رئيس المؤسسة', 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
}

// Create new PDF document (Portrait A4)
$pdf = new MYPDF('P', 'mm', 'A4', true, 'UTF-8', false);

// Set institution data
$pdf->setInstitutionData([
    'agency_name_ar' => $agency_name_ar,
    'address_ar' => $address_ar,
    'agency_name_en' => $agency_name_en,
    'address_en' => $address_en,
    'numbers' => $numbers,
    'logo' => $logoData
]);

// Set document information
$pdf->SetCreator('HR System');
$pdf->SetAuthor('HR System');
$pdf->SetTitle('Achievement Reports Summary');

// Set equal minimal margins on both sides
$pdf->SetMargins(2, 55, 2);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 25);

// Add a page
$pdf->AddPage();

// Set font for Arabic text
$pdf->SetFont('aealarabiya', '', 9);

// Set font for header information
$pdf->SetFont('aealarabiya', 'B', 10);

// Add a light gray background for the header section
$pdf->SetFillColor(245, 245, 245);

// Calculate page width and adjust column widths
$pageWidth = 210 - 4; // Total page width minus total margins (2mm + 2mm)

// Add title with date of printing
$pdf->SetFont('aealarabiya', 'B', 14);
$pdf->Cell(0, 10, 'ملخص تقارير الإنجاز', 0, 1, 'C');

// Setup for header information
$pdf->SetFont('aealarabiya', 'B', 10);

// Store starting Y position
$startY = $pdf->GetY();

// Project name row with full width (first row)
$leftStart = $pdf->GetMargins()['left'];

// Project name label and field with full width
$projectLabelWidth = 40; // Width for 'المشروع' label
$projectNameWidth = $pageWidth - $projectLabelWidth; // Remaining width for project name

// Get project name
$projectName = !empty($data['project_filter']) ? $data['project_name'] : 'كل المشاريع';

// Store starting position for project name label
$pdf->SetX($leftStart);
$startX = $pdf->GetX();
$startY = $pdf->GetY();

// Project name with MultiCell - allowing vertical expansion
$pdf->MultiCell($projectNameWidth, 6, $projectName, 1, 'R', false);

// Get ending Y position after project name content
$endY = $pdf->GetY();

// Add project label at the right height
$pdf->SetXY($startX + $projectNameWidth, $startY);
$pdf->Cell($projectLabelWidth, $endY - $startY, 'المشروع', 1, 1, 'R', false);

// Add spacing after project name
$pdf->Ln(2);

// Add employee name information if an employee filter was applied
if (!empty($data['employee_filter'])) {
    $employeeName = $data['employee_name'];
    $employeeLabelWidth = 40; // Width for 'الموظف' label
    $employeeNameWidth = $pageWidth - $employeeLabelWidth; // Remaining width for employee name
    
    // Store starting position for employee name label
    $pdf->SetX($leftStart);
    $startX = $pdf->GetX();
    $startY = $pdf->GetY();
    
    // Employee name with MultiCell - allowing vertical expansion
    $pdf->MultiCell($employeeNameWidth, 6, $employeeName, 1, 'R', false);
    
    // Get ending Y position after employee name content
    $endY = $pdf->GetY();
    
    // Add employee label at the right height
    $pdf->SetXY($startX + $employeeNameWidth, $startY);
    $pdf->Cell($employeeLabelWidth, $endY - $startY, 'الموظف', 1, 1, 'R', false);
    
    // Add spacing after employee name
    $pdf->Ln(2);
}

// Second row - Print Date and Record Count in two columns
// Calculate column widths for the second row
$columnWidth = $pageWidth / 2 - 1; // Split in half with 2mm spacing
$labelWidth = 40;
$valueWidth = $columnWidth - $labelWidth;

// First column - Print Date
$pdf->SetX($leftStart);
$pdf->Cell($valueWidth, 6, date('Y/m/d'), 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'تاريخ الطباعة', 1, 0, 'R', false);

// Spacing between columns
$pdf->Cell(2, 6, '', 0, 0, 'C', false);

// Second column - Record Count
$recordCount = isset($data['rows']) ? count($data['rows']) : 0;
$pdf->Cell($valueWidth, 6, $recordCount . ' سجل', 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'إجمالي السجلات', 1, 1, 'R', false);

// Add spacing before the table
$pdf->Ln(6);

// Create achievement reports summary table
$pdf->SetFont('aealarabiya', '', 9);

// Table header
$pdf->SetFillColor(220, 220, 220);
$pdf->SetDrawColor(128, 128, 128);
$pdf->SetLineWidth(0.15);

// Calculate available width for the table (A4 width minus margins)
$availableWidth = $pageWidth - 4; // 2mm margin on each side

// Define base column widths (these will be adjusted)
$baseWidths = array(
    'number' => 10,        // Row number column
    'project' => 25,       // Project number
    'employee' => 40,      // Employee name
    'job' => 35,          // Job title
    'id' => 25,           // ID number
    'start_date' => 25,   // Start date
    'end_date' => 25,     // End date
    'days' => 20          // Working days
);

// Calculate total base width
$totalBaseWidth = array_sum($baseWidths);

// Calculate adjustment factor to fill available width
$adjustmentFactor = $availableWidth / $totalBaseWidth;

// Create final column widths array
$w = array();
foreach ($baseWidths as $width) {
    $w[] = round($width * $adjustmentFactor);
}

// Define which columns to hide based on filters
$hideProjectColumn = !empty($data['project_filter']);
$hideEmployeeColumn = !empty($data['employee_filter']);

// Keep track of column indices
$projectColumnIndex = 1; // رقم المشروع
$employeeColumnIndex = 2; // اسم الموظف

// Calculate the total width of columns that will be hidden
$hiddenWidth = 0;
if ($hideProjectColumn) {
    $hiddenWidth += $w[$projectColumnIndex];
}
if ($hideEmployeeColumn) {
    $hiddenWidth += $w[$employeeColumnIndex];
}

// Calculate how much width to redistribute
$redistributionFactor = 0;

// Calculate redistribution factor only if columns are hidden
if ($hiddenWidth > 0) {
    // Count visible columns (excluding the ones we're hiding)
    $visibleColumnsCount = count($w);
    if ($hideProjectColumn) $visibleColumnsCount--;
    if ($hideEmployeeColumn) $visibleColumnsCount--;
    
    // Calculate how much extra width each column gets
    $redistributionFactor = $hiddenWidth / $visibleColumnsCount;
}

// Update column widths if hiding columns
$adjustedWidth = array();
for ($i = 0; $i < count($w); $i++) {
    if (($i == $projectColumnIndex && $hideProjectColumn) || 
        ($i == $employeeColumnIndex && $hideEmployeeColumn)) {
        $adjustedWidth[$i] = 0; // Set width to 0 for hidden columns
    } else {
        // Add extra width to visible columns
        $adjustedWidth[$i] = $w[$i] + $redistributionFactor;
    }
}

// Create header row
$headers = array(
    'أيام العمل الفعلية',
    'تاريخ النهاية',
    'تاريخ البداية',
    'رقم الهوية',
    'المسمى الوظيفي',
    'اسم الموظف',
    'رقم المشروع',
    ''  // Empty header for numbering column
);

// Determine maximum header height needed
$headerHeight = 7; // Minimum height
$pdf->SetFont('aealarabiya', 'B', 9); // Set bold font for headers

foreach ($headers as $index => $header) {
    if (($index == $projectColumnIndex && $hideProjectColumn) || 
        ($index == $employeeColumnIndex && $hideEmployeeColumn)) {
        continue; // Skip hidden columns
    }
    $height = $pdf->getStringHeight($adjustedWidth[$index], $header);
    $headerHeight = max($headerHeight, $height + 2); // Add padding
}

// Now draw the header cells with the calculated height
$x_start = $pdf->GetX();
$y_start = $pdf->GetY();
$current_x = $x_start;

// Draw cells in reverse order (RTL layout)
for ($i = 7; $i >= 0; $i--) {
    // Skip hidden columns
    if (($i == $projectColumnIndex && $hideProjectColumn) || 
        ($i == $employeeColumnIndex && $hideEmployeeColumn)) {
        continue;
    }
    
    $x_pos = $current_x;
    $y_pos = $y_start;
    
    // Use MultiCell for header text to allow wrapping
    // For the numbering column (index 0), use a different style
    if ($i == 0) {
        // Draw an empty cell with border but no text for the numbering column header
        $pdf->SetXY($x_pos, $y_pos);
        $pdf->Cell($adjustedWidth[$i], $headerHeight, '', 1, 0, 'C', true);
    } else {
        $pdf->SetXY($x_pos, $y_pos);
        $pdf->MultiCell($adjustedWidth[$i], $headerHeight, $headers[7-$i], 1, 'C', true);
    }
    
    $current_x += $adjustedWidth[$i];
}

// Reset position for data rows
$pdf->SetY($y_start + $headerHeight);

// Data rows
$pdf->SetFillColor(245, 245, 245);
$pdf->SetFont('aealarabiya', '', 9); // Reset to normal font
$fill = false;
$rowNumber = 1; // Initialize row counter

if (isset($data['rows']) && count($data['rows']) > 0) {
    foreach ($data['rows'] as $row) {
        // Store starting position
        $x_pos = $pdf->GetX();
        $y_pos = $pdf->GetY();
        $max_height = 7; // Minimum row height
        
        // Calculate row height based on content
        $temp_y = $y_pos;
        
        // Check height needed for job title (which might be longer)
        $job_title = isset($row['name_Job']) ? $row['name_Job'] : '';
        
        // Only calculate height for visible columns
        if (!$hideEmployeeColumn) {
            $pdf->SetXY($x_pos + array_sum(array_slice($adjustedWidth, 0, 5)), $temp_y);
            $job_title_height = $pdf->getStringHeight($adjustedWidth[3], $job_title);
            $max_height = max($max_height, $job_title_height);
            
            // Check height needed for employee name
            $name = $row['name_ar_contract'];
            $pdf->SetXY($x_pos + array_sum(array_slice($adjustedWidth, 0, 4)), $temp_y);
            $name_height = $pdf->getStringHeight($adjustedWidth[2], $name);
            $max_height = max($max_height, $name_height);
        }
        
        // Now draw cells with the calculated maximum height
        $pdf->SetXY($x_pos, $y_pos);
        
        // Using the requested column order with dynamic heights
        $pdf->Cell($adjustedWidth[7], $max_height, $row['actual_working_days'], 1, 0, 'C', $fill);
        $pdf->Cell($adjustedWidth[6], $max_height, $row['end_date_achievement_reports'], 1, 0, 'C', $fill);
        $pdf->Cell($adjustedWidth[5], $max_height, $row['start_date_achievement_reports'], 1, 0, 'C', $fill);
        $pdf->Cell($adjustedWidth[4], $max_height, $row['Identity_number_contract'], 1, 0, 'C', $fill);
        
        // For text cells that might wrap, use MultiCell with the same height
        $x_pos = $pdf->GetX();
        $y_pos = $pdf->GetY();
        $pdf->MultiCell($adjustedWidth[3], $max_height, $job_title, 1, 'R', $fill);
        $pdf->SetXY($x_pos + $adjustedWidth[3], $y_pos);
        
        // Only show employee name if not filtered
        if (!$hideEmployeeColumn) {
            $x_pos = $pdf->GetX();
            $y_pos = $pdf->GetY();
            $pdf->MultiCell($adjustedWidth[2], $max_height, $row['name_ar_contract'], 1, 'R', $fill);
            $pdf->SetXY($x_pos + $adjustedWidth[2], $y_pos);
        }
        
        // Only show project number if not filtered
        if (!$hideProjectColumn) {
            $pdf->Cell($adjustedWidth[1], $max_height, $row['id_Project'], 1, 0, 'C', $fill);
        }
        
        $pdf->Cell($adjustedWidth[0], $max_height, $rowNumber, 1, 1, 'C', $fill); // Add row number
        
        $fill = !$fill;
        $rowNumber++; // Increment row counter
    }
} else {
    // Calculate total visible width
    $visibleWidth = 0;
    foreach ($adjustedWidth as $width) {
        $visibleWidth += $width;
    }
    
    $pdf->Cell($visibleWidth, 7, 'لا توجد بيانات متاحة', 1, 1, 'C', false);
}

// Output the PDF
$pdf->Output('achievement_reports_summary.pdf', 'I');
?> 