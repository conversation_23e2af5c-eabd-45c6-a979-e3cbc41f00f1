<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

$projects = [];
$contracts = [];
$selectedItem = null;
$error_message = '';
$success_message = '';

if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) throw new Exception('خطأ في قراءة ملف الإعدادات');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_permanent_diapers'])) {
        try {
            $data = json_decode($_POST['attendance_data'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON data');
            }

            $basicData = json_decode($_POST['basic_data'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid basic data');
            }

            // Validate required fields
            if (empty($basicData['id_Project']) || empty($basicData['id_contract']) || 
                empty($basicData['start_date_permanent_diapers']) || empty($basicData['end_date_permanent_diapers'])) {
                throw new Exception('جميع الحقول المطلوبة يجب أن تكون موجودة');
            }

            $stmt = $conn->prepare("INSERT INTO permanent_diapers 
                (id_Project, id_contract, id_extension_contract, start_date_permanent_diapers, 
                 end_date_permanent_diapers, data) 
                VALUES (?, ?, ?, ?, ?, ?)");

            if ($stmt === false) {
                throw new Exception('Query preparation failed: ' . $conn->error);
            }

            $stmt->bind_param("iiisss", 
                $basicData['id_Project'],
                $basicData['id_contract'],
                $basicData['id_extension_contract'],
                $basicData['start_date_permanent_diapers'],
                $basicData['end_date_permanent_diapers'],
                $_POST['attendance_data']
            );

            if ($stmt->execute()) {
                $_SESSION['success_message'] = 'تم حفظ البيانات بنجاح';
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit;
            } else {
                throw new Exception('فشل في حفظ البيانات');
            }
        } catch (Exception $e) {
            $error_message = "خطأ: " . $e->getMessage();
        }
    }

    if (isset($_GET['action']) && $_GET['action'] === 'get_contracts' && isset($_GET['project_id'])) {
        header('Content-Type: application/json');
        $projectId = intval($_GET['project_id']);
        
        // Fetch main contracts and their extensions
        $stmt = $conn->prepare("
            SELECT 
                c.id_contract AS main_id,
                e.name_ar_contract AS main_name,
                c.name_Job,
                c.version_date AS main_version_date,
                ec.id_extension_contract AS extension_id,
                ec.version_date AS extension_version_date,
                ec.start_date_contract AS extension_start_date,
                ec.end_date_contract AS extension_end_date
            FROM contract c
            INNER JOIN employees e ON c.id_employees = e.id_employees
            LEFT JOIN extension_contract ec ON c.id_contract = ec.id_contract
            WHERE c.id_Project = ? AND c.status_contract != 0
            ORDER BY c.id_contract, ec.add_extension_contract DESC
        ");
        $stmt->bind_param("i", $projectId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $groupedContracts = [];
        while ($row = $result->fetch_assoc()) {
            $mainId = $row['main_id'];
            if (!isset($groupedContracts[$mainId])) {
                $groupedContracts[$mainId] = [
                    'main' => [
                        'id' => $row['main_id'],
                        'name' => $row['main_name'],
                        'job' => $row['name_Job'],
                        'version_date' => $row['main_version_date'],
                    ],
                    'extensions' => []
                ];
            }
            if ($row['extension_id']) {
                $groupedContracts[$mainId]['extensions'][] = [
                    'id' => $row['extension_id'],
                    'version_date' => $row['extension_version_date'],
                    'start_date' => $row['extension_start_date'],
                    'end_date' => $row['extension_end_date'],
                ];
            }
        }

        $response = [];
        foreach ($groupedContracts as $group) {
            $main = $group['main'];
            $response[] = [
                'type' => 'main',
                'id' => 'contract_' . $main['id'],
                'text' => sprintf('رقم العقد %d - %s - %s', 
                    $main['id'],
                    $main['name'],
                    date('d-m-Y', strtotime($main['version_date']))
                ),
                'extensions' => array_map(function($ext) {
                    return [
                        'type' => 'extension',
                        'id' => 'extension_' . $ext['id'],
                        'text' => sprintf('رقم تمديد العقد %d - %s إلى %s', 
                            $ext['id'],
                            date('d-m-Y', strtotime($ext['start_date'])),
                            $ext['end_date'] ? date('d-m-Y', strtotime($ext['end_date'])) : 'مفتوح'
                        )
                    ];
                }, $group['extensions'])
            ];
        }
        echo json_encode($response);
        exit;
    }

    if (isset($_GET['action']) && $_GET['action'] === 'get_saved_dates' && isset($_GET['contract_id'])) {
        $contractId = intval($_GET['contract_id']);
        
        $stmt = $conn->prepare("SELECT DISTINCT work_date FROM permanent_diapers WHERE contract_id = ?");
        $stmt->bind_param("i", $contractId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $dates = [];
        while ($row = $result->fetch_assoc()) {
            $dates[] = $row['work_date'];
        }
        
        header('Content-Type: application/json');
        echo json_encode($dates);
        exit;
    }

    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    while ($row = $result->fetch_assoc()) {
        $projects[] = $row;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contract_id'])) {
        $selected = $_POST['contract_id'];
        if (strpos($selected, 'contract_') === 0) {
            $contractId = substr($selected, 9);
            // Update this query to include employee details
            $stmt = $conn->prepare("
                SELECT c.*, e.name_ar_contract 
                FROM contract c
                LEFT JOIN employees e ON c.id_employees = e.id_employees 
                WHERE c.id_contract = ?
            ");
            $stmt->bind_param("i", $contractId);
            $stmt->execute();
            $contractDetails = $stmt->get_result()->fetch_assoc();
            $stmt->close();

            // Fetch permanent diapers records for main contract (where id_extension_contract is NULL)
            $diapersQuery = "SELECT 
                p.id_permanent_diapers,
                pr.id_Project,
                p.id_contract,
                ec.id_extension_contract AS extended_contract,
                p.start_date_permanent_diapers AS start_date,
                p.end_date_permanent_diapers AS end_date,
                p.data
            FROM permanent_diapers p
            INNER JOIN project pr ON p.id_Project = pr.id_Project
            INNER JOIN contract c ON p.id_contract = c.id_contract
            LEFT JOIN extension_contract ec ON p.id_extension_contract = ec.id_extension_contract
            WHERE p.id_contract = ? AND p.id_extension_contract IS NULL";
            
            $diapersStmt = $conn->prepare($diapersQuery);
            if ($diapersStmt === false) {
                die("Prepare failed: " . $conn->error);
            }
            $diapersStmt->bind_param('i', $contractId);
            $diapersStmt->execute();
            $diapersResult = $diapersStmt->get_result();
            $diapersRecords = [];
            if ($diapersResult->num_rows > 0) {
                while ($row = $diapersResult->fetch_assoc()) {
                    $diapersRecords[] = $row;
                }
            }
            $diapersStmt->close();

            $selectedItem = [
                'type' => 'contract',
                'contractDetails' => $contractDetails,
                'diapersRecords' => $diapersRecords
            ];
        } elseif (strpos($selected, 'extension_') === 0) {
            $extensionId = substr($selected, 10);
            $stmt = $conn->prepare("SELECT * FROM extension_contract WHERE id_extension_contract = ?");
            $stmt->bind_param("i", $extensionId);
            $stmt->execute();
            $selectedItem = $stmt->get_result()->fetch_assoc();
            $selectedItem['type'] = 'extension';
            $stmt->close();

            // Fetch main contract details
            $stmt = $conn->prepare("
                SELECT c.*, e.name_ar_contract 
                FROM contract c
                LEFT JOIN employees e ON c.id_employees = e.id_employees 
                WHERE c.id_contract = ?
            ");
            $stmt->bind_param("i", $selectedItem['id_contract']);
            $stmt->execute();
            $selectedItem['main_contract'] = $stmt->get_result()->fetch_assoc();
            $stmt->close();

            // Fetch permanent diapers records for extension contract
            $diapersQuery = "SELECT 
                p.id_permanent_diapers,
                pr.id_Project AS project_number,
                c.id_contract AS main_contract,
                ec.id_extension_contract AS extended_contract,
                p.start_date_permanent_diapers AS start_date,
                p.end_date_permanent_diapers AS end_date,
                p.data
            FROM permanent_diapers p
            INNER JOIN project pr ON p.id_Project = pr.id_Project
            INNER JOIN contract c ON p.id_contract = c.id_contract
            INNER JOIN extension_contract ec ON p.id_extension_contract = ec.id_extension_contract
            WHERE p.id_extension_contract = ?";
            
            $diapersStmt = $conn->prepare($diapersQuery);
            if ($diapersStmt === false) {
                die("Prepare failed: " . $conn->error);
            }
            $diapersStmt->bind_param('i', $extensionId);
            $diapersStmt->execute();
            $diapersResult = $diapersStmt->get_result();
            $diapersRecords = [];
            if ($diapersResult->num_rows > 0) {
                while ($row = $diapersResult->fetch_assoc()) {
                    $diapersRecords[] = $row;
                }
            }
            $diapersStmt->close();

            $selectedItem['diapersRecords'] = $diapersRecords;
        }
    }

} catch (Exception $e) {
    $error_message = "خطأ في النظام: " . $e->getMessage();
} finally {
    if (isset($conn)) $conn->close();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء بدل دائم - نظام إدارة الموارد البشرية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        :root {
            --select-bg: #fff;
            --select-text: #495057;
            --select-border: #ced4da;
            --select-placeholder: #999;
            --select-hover-bg: #f8f9fa;
            --select-focus-border: #86b7fe;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
            --bg-disabled: #f7f7f7;
            --text-disabled: #a0a0a0;
            --border-disabled: #e5e5e5;
        }

        [data-theme="dark"] {
            --select-bg: #2b3035;
            --select-text: #e9ecef;
            --select-border: #495057;
            --select-placeholder: #6c757d;
            --select-hover-bg: #343a40;
            --select-focus-border: #0d6efd;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
            --bg-disabled: #1e2227;
            --text-disabled: #6c757d;
            --border-disabled: #2d333b;
        }

        /* Select2 RTL Fixes with Theme Support */
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px !important;
            display: flex !important;
            align-items: center !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            transition: all 0.3s ease !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            width: 100% !important;
            padding-right: 8px !important;
            padding-left: 20px !important;
            display: block !important;
            position: static !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            color: var(--select-text) !important;
        }

        /* Placeholder color */
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
            color: var(--select-placeholder) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            position: absolute !important;
            left: 3px !important;
            right: auto !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
            border-color: var(--select-text) transparent transparent transparent !important;
        }

        .select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow b {
            border-color: transparent transparent var(--select-text) transparent !important;
        }

        /* Dropdown styles */
        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--select-bg) !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            text-align: right !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            padding: 6px 12px !important;
            text-align: right !important;
            color: var(--select-text) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
            background-color: rgba(var(--primary-rgb), 0.1);
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
        
        /* Rest of the existing styles */
        .custom-alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }
        .custom-alert.error {
            background-color: var(--error-bg);
            color: var(--error-text);
            border: 1px solid var(--error-border);
        }
        .custom-alert.success {
            background-color: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-border);
        }
        .custom-alert.fade-out {
            opacity: 0;
        }
        /* Enhanced select field styling */
        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        .form-select.select2-search {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 0.625rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .form-select.select2-search:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.15);
            outline: none;
        }
        .form-select.select2-search:hover {
            border-color: #bdbdbd;
        }
        .form-label {
            font-size: 0.95rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
            display: block;
        }
        /* Select2 customization */
        .select2-container--default .select2-selection--single {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        .select2-container--default .select2-selection--single:hover {
            border-color: #bdbdbd;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 44px;
            padding-right: 1rem;
            padding-left: 1rem;
            display: block;
            position: static;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #2d3748;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 46px;
            width: 30px;
        }
        .select2-dropdown {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .select2-search__field {
            border-radius: 8px !important;
            padding: 0.5rem !important;
        }
        .select2-results__option {
            padding: 0.75rem 1rem;
        }
        .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: rgba(var(--primary-rgb), 0.1) !important;
        }
        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
        .contract-item { 
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
            color: var(--text-primary);
        }
        .contract-item:hover { 
            background-color: var(--bg-hover);
            border-color: var(--primary-color);
        }
        .contract-item.selected { 
            background-color: var(--bg-selected);
            border-color: var(--primary-color);
        }
        .extension-item { 
            padding: 0.5rem 2rem;
            margin-bottom: 0.5rem;
            border-radius: 0.75rem;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }
        .detail-label { 
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        .detail-value { 
            color: var(--text-primary);
            font-weight: 500;
        }
        .card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
        }
        .card-title {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
        }
        .form-control, .form-select {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.625rem;
            color: var(--text-primary);
            transition: all 0.2s;
        }
        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: var(--primary-color);
            color: var(--text-primary);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--text-light);
            padding: 0.625rem 1.25rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }
        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }
        .btn-primary:focus {
            box-shadow: 0 0 0 0.2rem var(--primary-shadow);
        }
    </style>
    <style>
        /* Retention Period Calendar */
        .retention-period-container {
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            gap: 2rem;
        }

        .calendar-section {
            flex-shrink: 0;
        }

        .date-fields-section {
            padding-top: 1rem;
            width: 250px; /* Limit the width of the fields */
        }

        .calendar-wrapper {
            --calendar-day-size: 40px; /* Increased size */
            width: calc(var(--calendar-day-size) * 7 + 16px);
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            user-select: none;
        }

        .calendar-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            padding: 4px;
            background-color: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
        }

        .calendar-day-header {
            width: var(--calendar-day-size);
            height: 24px;
            font-size: 0.8rem;
            text-align: center;
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .calendar-container {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            padding: 4px;
        }

        .calendar-day {
            width: var(--calendar-day-size);
            height: var(--calendar-day-size);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s;
            color: var(--text-primary);
            border: 1px solid transparent;
            user-select: none;
            position: relative;
        }

        .calendar-day:not(.out-of-period):hover {
            background-color: var(--hover-color);
            border-color: var(--primary-color);
            transform: scale(1.05);
            z-index: 1;
        }

        .calendar-day.selected {
            background-color: var(--primary-color);
            color: var(--text-light);
            font-weight: 600;
            transform: scale(1.05);
            z-index: 2;
        }

        .calendar-day.in-range {
            background-color: var(--primary-light);
            color: var(--text-primary);
            position: relative;
        }

        .calendar-day.in-range::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--primary-color);
            opacity: 0.2;
            border-radius: 4px;
            transition: opacity 0.2s;
        }

        .calendar-day.in-range:hover::before {
            opacity: 0.3;
        }

        .calendar-day.out-of-period {
            color: var(--text-muted);
            cursor: not-allowed;
            background-color: var(--bg-disabled);
            opacity: 0.5;
        }

        .calendar-day.disabled {
            color: var(--text-disabled);
            background-color: var(--bg-disabled);
            border: 1px solid var(--border-disabled);
            cursor: not-allowed;
            position: relative;
            opacity: 1;
        }

        .calendar-day.disabled::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 4px,
                rgba(160, 160, 160, 0.1) 4px,
                rgba(160, 160, 160, 0.1) 8px
            );
            pointer-events: none;
        }

        /* Special case for disabled days in dark theme */
        [data-theme="dark"] .calendar-day.disabled::after {
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 4px,
                rgba(0, 0, 0, 0.2) 4px,
                rgba(0, 0, 0, 0.2) 8px
            );
        }

        .calendar-day.disabled:hover {
            background-color: var(--bg-disabled);
            color: var(--text-disabled);
            transform: none;
        }

        .calendar-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background-color: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
        }

        .current-month {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            text-align: center;
            min-width: 120px;
        }

        .retention-date-input {
            background-color: var(--bg-input);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            border-radius: 6px;
            width: 100%;
            cursor: default;
            font-size: 1rem;
            transition: all 0.2s;
            text-align: center;
            font-weight: 500;
        }

        .retention-date-input[readonly] {
            background-color: var(--bg-input);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
        }

        .retention-date-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .retention-date-input::placeholder {
            color: var(--text-muted);
            opacity: 0.7;
        }

        [data-theme="dark"] .calendar-wrapper {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .btn-outline-secondary {
            padding: 0.375rem 0.75rem;
            font-size: 0.9rem;
        }

        .btn-outline-secondary:hover {
            background-color: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
    </style>
    <style>
        /* Contract Tree Structure */
        .contract-tree-item {
            position: relative;
            padding-right: 28px !important;
            margin: 4px 0 !important;
            display: block;
            width: 100%;
        }

        .contract-extension {
            position: relative;
            padding-right: 42px !important;
            font-size: 0.95em;
        }

        /* Horizontal line */
        .contract-extension::before {
            content: '';
            position: absolute;
            right: 22px;
            top: 50%;
            width: 16px;
            height: 2px;
            background-color: var(--select-text);
            opacity: 0.6;
        }

        /* Vertical line */
        .contract-extension::after {
            content: '';
            position: absolute;
            right: 22px;
            top: -12px;
            width: 2px;
            height: calc(100% + 12px);
            background-color: var(--select-text);
            opacity: 0.6;
        }

        /* Special cases for first and last extensions */
        .first-extension::after {
            top: 50%;
            height: calc(50% + 12px);
        }

        .last-extension::after {
            height: 50%;
        }

        /* Main contract styling */
        .contract-main-option {
            font-weight: 600;
            position: relative;
            margin-top: 8px !important;
        }

        /* Extension option styling */
        .extension-option {
            position: relative;
            padding-right: 60px !important;
            margin-right: 35px !important;
        }

        /* Extension connecting lines */
        .extension-option::before,
        .extension-option::after {
            content: '';
            position: absolute;
            background-color: var(--select-text);
            opacity: 0.6;
        }

        /* Horizontal line */
        .extension-option::before {
            right: 40px;
            width: 20px;
        }

        /* Vertical line */
        .extension-option::after {
            right: 40px;
        }

        /* First extension vertical line */
        .first-extension::after {
            top: 50%;
            height: calc(50% + 15px);
        }

        /* Middle extension vertical line */
        .extension-option:not(.first-extension):not(.last-extension)::after {
            top: -15px;
            height: calc(100% + 30px);
        }

        /* Last extension vertical line */
        .last-extension::after {
            top: -15px;
            height: calc(50% + 15px);
        }

        /* Select2 specific styles */
        .select2-container--bootstrap-5 .select2-results__options {
            padding: 8px 4px !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            padding: 6px 12px;
            transition: all 0.2s ease;
        }

        /* Hover and selected states */
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: rgba(var(--primary-rgb), 0.1) !important;
            color: var(--primary-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
            background-color: rgba(var(--primary-rgb), 0.15) !important;
            color: var(--primary-color) !important;
        }

        /* Spacing between groups */
        .select2-results__option + .select2-results__option {
            margin-top: 4px !important;
        }

        /* Hover effects for tree items */
        .select2-results__option:hover .contract-tree-item::before,
        .select2-results__option:hover .contract-tree-item::after,
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] .contract-tree-item::before,
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] .contract-tree-item::after,
        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] .contract-tree-item::before,
        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] .contract-tree-item::after {
            opacity: 1;
            color: var(--primary-color);
            border-color: var(--primary-color);
            background-color: var(--primary-color);
        }
    </style>
    <style>
        /* Scrollable List Styles */
        .scrollable-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .list-header {
            padding: 0.75rem 1rem;
            background-color: var(--bg-input);
            border-radius: 0.25rem;
            font-weight: 600;
        }

        .list-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s;
        }

        .list-item:hover {
            background-color: var(--bg-hover);
        }

        .list-item:last-child {
            border-bottom: none;
        }
    </style>
    <style>
        /* JSON Display Styles */
        .json-display {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--text-primary);
        }

        /* Table Styles */
        .table {
            color: var(--text-primary);
            background-color: var(--bg-card);
            border-color: var(--border-color);
        }

        .table th {
            background-color: var(--bg-input);
            color: var(--text-primary);
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            white-space: nowrap;
        }

        .table td {
            vertical-align: middle;
        }

        .table input[type="time"] {
            width: 120px;
            text-align: center;
        }

        .table select {
            width: 100%;
            min-width: 100px;
        }

        .table textarea {
            width: 100%;
            min-height: 38px;
            resize: vertical;
        }

        /* Time Input Styles */
        .time-input-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .time-input-group select {
            flex: 0 0 100px;
        }

        .time-input-group input[type="time"] {
            flex: 1;
        }

        /* Copy Button Styles */
        #copyJsonBtn {
            transition: all 0.2s ease-in-out;
        }

        #copyJsonBtn:hover {
            background-color: var(--bg-hover);
            border-color: var(--border-color);
        }

        #copyJsonBtn:active {
            transform: translateY(1px);
        }

        /* Dark theme specific styles */
        [data-theme="dark"] .json-display {
            background-color: var(--bg-input);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .table {
            border-color: var(--border-color);
        }

        [data-theme="dark"] .table th {
            background-color: var(--bg-input);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .table td {
            border-color: var(--border-color);
        }
    </style>
    <style>
        /* Enhanced Table Styles for Attendance Register */
        #attendanceTableSection .table {
            border-radius: 0.5rem;
            overflow: hidden;
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            width: 100%;
        }

        #attendanceTableSection .table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            border-bottom: 2px solid var(--border-color);
            transition: background-color 0.3s ease;
        }

        #attendanceTableSection .table td {
            padding: 0.875rem 1rem;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        #attendanceTableSection .table tbody tr:hover {
            background-color: var(--bg-hover);
        }

        #attendanceTableSection .table .time-input-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        #attendanceTableSection .table .attendance-input {
            background-color: var(--bg-input);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 0.375rem 0.75rem;
            transition: all 0.2s ease-in-out;
        }

        #attendanceTableSection .table .attendance-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
            outline: none;
        }

        /* Dark theme specific enhancements */
        [data-theme="dark"] #attendanceTableSection .table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #attendanceTableSection .table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        [data-theme="dark"] #attendanceTableSection .table td {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #attendanceTableSection .table tbody tr:hover {
            background-color: var(--bg-hover);
        }

        [data-theme="dark"] #attendanceTableSection .table .attendance-input {
            background-color: var(--bg-input);
            color: var(--text-primary);
        }

        [data-theme="dark"] #attendanceTableSection .table .attendance-input:focus {
            border-color: var(--primary);
        }

        /* Responsive table styles */
        @media (max-width: 768px) {
            #attendanceTableSection .table-responsive {
                border-radius: 0.5rem;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            #attendanceTableSection .table th,
            #attendanceTableSection .table td {
                white-space: nowrap;
                min-width: 120px;
            }

            #attendanceTableSection .table .time-input-group {
                min-width: 150px;
            }
        }
    </style>
    <style>
        /* Attendance Register Table Specific Styles */
        #attendanceTableSection .table {
            border: 1px solid var(--border-color);
            border-collapse: separate;
            border-spacing: 0;
        }

        #attendanceTableSection .table th,
        #attendanceTableSection .table td {
            border: 1px solid var(--border-color);
        }

        /* Fix for select dropdown arrow position */
        #attendanceTableSection .table select.attendance-input {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23666' d='M0 2l4 4 4-4z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: left 0.75rem center;
            padding-left: 2rem;
            padding-right: 0.75rem;
            direction: rtl;
        }

        [data-theme="dark"] #attendanceTableSection .table select.attendance-input {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M0 2l4 4 4-4z'/%3E%3C/svg%3E");
        }
    </style>
    <style>
        /* Attendance Table Field Sizes */
        #attendanceTableSection .table .attendance-input {
            min-width: 140px;
            height: 38px;
            width: 100%;
        }

        #attendanceTableSection .table td:last-child .attendance-input {
            min-width: 300px; /* Larger width for Notes field */
            width: 100%;
        }

        /* Ensure table can scroll horizontally on smaller screens */
        #attendanceTableSection .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
    </style>
    <style>
        /* Fixed height Select2 dropdowns */
        .select2-container--bootstrap-5 .select2-dropdown {
            max-height: 300px;
            overflow-y: auto;
        }

        .select2-container--bootstrap-5 .select2-results__options {
            max-height: 250px !important;
            overflow-y: auto !important;
            scrollbar-width: thin;
            scrollbar-color: var(--select-border) transparent;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar {
            width: 6px;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-track {
            background: transparent;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb {
            background-color: var(--select-border);
            border-radius: 3px;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb:hover {
            background-color: var(--select-text);
        }

        /* Ensure dropdown options are fully visible */
        .select2-container--bootstrap-5 .select2-results__option {
            white-space: normal;
            word-wrap: break-word;
        }

        /* Improve dropdown container appearance */
        .select2-container--bootstrap-5.select2-container--open .select2-dropdown {
            border-color: var(--select-focus-border);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Dark theme support for dropdowns */
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb {
            background-color: var(--dark-border-color);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb:hover {
            background-color: var(--dark-text-muted);
        }

        /* Enhanced Select2 RTL Support */
        .select2-container--bootstrap-5 .select2-selection--single {
            padding-right: 1rem !important;
            padding-left: 2.25rem !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            left: 0.75rem !important;
            right: auto !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            text-align: right !important;
            padding-right: 1rem !important;
        }
    </style>
    <style>
        /* Alert styling */
        .alert-info {
            margin: -0.5rem; /* Align with card body padding */
            border-radius: 0 0 8px 8px; /* Round only bottom corners */
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Custom scrollbar for Firefox */
        .achievement-reports {
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }
    </style>
    <style>
        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: #2d303d;
        }

        .section-title {
            color: #0d6efd;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Card styling */
        .card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
        }

        .card-title {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Form controls */
        .form-control, .form-select {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.625rem;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: #0d6efd;
            color: var(--text-primary);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* Contract details styling */
        .contract-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-group {
            padding: 0.75rem;
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* Alert styling */
        .custom-alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }

        .custom-alert.success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .custom-alert.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
            background-color: var(--bg-info);
            color: var(--text-info);
            border-color: var(--border-info);
        }

        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Select2 customization */
        .select2-container--default .select2-selection--single {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background-color: var(--bg-input);
            transition: all 0.3s ease;
        }

        .select2-container--default .select2-selection--single:hover {
            border-color: #0d6efd;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 46px;
            padding-right: 1rem;
            color: var(--text-primary);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 46px;
        }

        /* Button styling */
        .btn-primary {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .btn-primary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Calendar styling */
        .calendar-wrapper {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
            overflow: hidden;
        }

        .calendar-navigation {
            background-color: var(--bg-card);
            border-bottom: 2px solid #0d6efd;
            padding: 1rem;
        }

        .calendar-day {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .calendar-day:hover:not(.disabled):not(.out-of-period) {
            background-color: var(--bg-hover);
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
        }

        .calendar-day.selected {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            color: #fff;
            border-color: #0d6efd;
            font-weight: 600;
        }

        /* Table styling */
        .table {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            border-bottom: 2px solid #0d6efd;
            padding: 1rem;
        }

        .table td {
            padding: 0.875rem;
            vertical-align: middle;
            border-color: var(--border-color);
        }

        .table tbody tr:hover {
            background-color: var(--bg-hover);
        }

        /* Attendance table specific */
        #attendanceTableSection .table .attendance-input {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem;
            transition: all 0.2s ease;
        }

        #attendanceTableSection .table .attendance-input:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* JSON display */
        .json-display {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1rem;
            color: var(--text-primary);
        }

        /* Scrollable list */
        .scrollable-list {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1rem;
        }

        .list-header {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            border-bottom: 2px solid #0d6efd;
            margin-bottom: 1rem;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
        }

        .list-item {
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .list-item:hover {
            background-color: var(--bg-hover);
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
        }
    </style>
    <style>
        /* Extension list styling */
        .extension-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .extension-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
            min-width: min-content;
        }

        .extension-item:hover {
            background-color: var(--bg-hover);
            border-color: #0d6efd;
        }

        .extension-info {
            display: flex;
            align-items: center;
            gap: 2rem;
            flex: 1;
            white-space: nowrap;
        }

        .extension-info > div {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .extension-info .detail-label {
            margin-bottom: 0;
            color: var(--text-muted);
        }

        .extension-info .detail-value {
            margin-right: 0.5rem;
            color: var(--text-primary);
        }

        /* Scrollbar styling for the extension list */
        .extension-list::-webkit-scrollbar {
            width: 8px;
        }

        .extension-list::-webkit-scrollbar-track {
            background: var(--bg-card);
            border-radius: 4px;
        }

        .extension-list::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        .extension-list::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        .no-extensions {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px dashed var(--border-color);
        }
    </style>
    <style>
        /* Attendance Table Scrolling */
        #attendanceTableSection .table-responsive {
            max-height: 500px;
            overflow-y: auto;
        }

        #attendanceTableSection .table thead {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: var(--bg-card);
        }

        #attendanceTableSection .table thead tr:first-child th {
            position: sticky;
            top: 0;
            z-index: 2;
        }

        #attendanceTableSection .table thead tr:last-child th {
            position: sticky;
            top: 43px; /* Adjust based on your first header row height */
            z-index: 2;
        }
    </style>
    <style>
        /* Calendar styles */
        .calendar-wrapper {
            --calendar-day-size: 40px; /* Increased size */
            width: calc(var(--calendar-day-size) * 7 + 16px);
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            user-select: none;
        }
        
        /* Rest of calendar styles */
        // ... existing code ...
    </style>
    <style>
        /* Custom styles for days list table */
        #includedDaysList .table {
            margin-bottom: 0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        #includedDaysList .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        #includedDaysList .table tr:hover {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.2s;
        }
        
        #includedDaysList .table tr.selected {
            background-color: rgba(13, 110, 253, 0.1) !important;
        }
        
        #includedDaysList .table td {
            vertical-align: middle;
        }
        
        .form-check-input.day-selector {
            cursor: pointer;
            width: 18px;
            height: 18px;
        }
        
        .form-check-input.day-selector:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .days-actions {
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            margin-bottom: 10px;
        }
        
        #selectedDaysCount {
            font-size: 0.9rem;
            padding: 5px 8px;
        }
        
        /* Dark theme support */
        [data-theme="dark"] #includedDaysList .table thead th {
            background-color: #343a40;
            border-bottom: 2px solid #495057;
            color: #e9ecef;
        }
        
        [data-theme="dark"] .days-actions {
            background-color: #343a40;
            border-color: #495057;
        }
        
        [data-theme="dark"] #includedDaysList .table tr.selected {
            background-color: rgba(13, 110, 253, 0.2) !important;
        }
        
        /* Make weekend days visually distinct */
        #includedDaysList .table tr.table-light td {
            color: #0d6efd;
            font-weight: 500;
        }
    </style>
    <style>
        /* Style for time inputs with 00:00 value */
        input[type="time"].time-placeholder {
            color: transparent;
            position: relative;
        }
        
        input[type="time"].time-placeholder::before {
            content: "-";
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            z-index: 1;
        }
        
        /* Disable browser's time input UI when showing placeholder */
        input[type="time"].time-placeholder::-webkit-calendar-picker-indicator {
            display: none;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <main id="content">
        <div class="container-fluid py-4">
            <?php if (!empty($error_message)): ?>
                <div class="custom-alert error" role="alert">
                    <?= $error_message ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="custom-alert success" role="alert">
                    <?= $success_message ?>
                </div>
            <?php endif; ?>

            <!-- Added informational message -->
            <div class="alert alert-info mb-4" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                مرحباً بك في صفحة إنشاء سجلات الحضور التي تم إنشاؤها مسبقًا. يمكنك من خلال هذه الصفحة إنشاء وإدارة سجلات الحضور للموظفين وتتبع أوقات الدوام العادي والإضافي.
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-4">البحث عن العقود</h5>
                    <form method="post" id="searchForm">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                    <select class="form-select select2-search" id="project_id" name="project_id" required>
                                        <option value="">اختر المشروع</option>
                                        <?php foreach ($projects as $project): ?>
                                            <option value="<?= $project['id_Project'] ?>" <?= isset($_POST['project_id']) && $_POST['project_id'] == $project['id_Project'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($project['Project_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contract_id" class="form-label fw-bold mb-2">اختر عقد</label>
                                    <select class="form-select select2-search" id="contract_id" name="contract_id" required <?= empty($_POST['project_id']) ? 'disabled' : '' ?>>
                                        <option value="">اختر العقد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($selectedItem): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">تفاصيل العقد</h5>
                        <div class="row g-4">
                            <?php if ($selectedItem['type'] === 'contract'): ?>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">رقم العقد</label>
                                        <span class="detail-value" data-contract-id="<?= htmlspecialchars($selectedItem['contractDetails']['id_contract']) ?>">
                                            : <?= htmlspecialchars($selectedItem['contractDetails']['id_contract']) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">اسم صاحب العقد</label>
                                        <span class="detail-value">: <?= htmlspecialchars($selectedItem['contractDetails']['name_ar_contract']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">الاسم الوظيفي</label>
                                        <span class="detail-value">: <?= htmlspecialchars($selectedItem['contractDetails']['name_Job']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">تاريخ الإصدار</label>
                                        <span class="detail-value">: <?= date('Y-m-d', strtotime($selectedItem['contractDetails']['version_date'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">تاريخ بدء العقد</label>
                                        <span class="detail-value contract-date" data-date="<?= date('Y-m-d', strtotime($selectedItem['contractDetails']['start_date_contract'])) ?>">
                                            : <?= date('Y-m-d', strtotime($selectedItem['contractDetails']['start_date_contract'])) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">تاريخ انتهاء العقد</label>
                                        <span class="detail-value contract-date" data-date="<?= $selectedItem['contractDetails']['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['contractDetails']['end_date_contract'])) : '' ?>">
                                            : <?= $selectedItem['contractDetails']['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['contractDetails']['end_date_contract'])) : 'مفتوح' ?>
                                        </span>
                                    </div>
                                </div>
                                <?php
                                $todoListData = json_decode($selectedItem['contractDetails']['data_todo_list_contract'], true);
                                ?>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">نوع العقد</label>
                                        <span class="detail-value">: <?= isset($todoListData['evaluation']['percentageEvaluation']) && $todoListData['evaluation']['percentageEvaluation'] === 'yes' ? 'أجر شهري' : 'أجر يومي' ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">عدد التمديدات</label>
                                        <span class="detail-value">: <?= htmlspecialchars($selectedItem['contractDetails']['extension']) ?></span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">رقم العقد الأساسي</label>
                                        <span class="detail-value" data-contract-id="<?= htmlspecialchars($selectedItem['id_contract']) ?>">
                                            : <?= htmlspecialchars($selectedItem['id_contract']) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">رقم تمديد العقد</label>
                                        <span class="detail-value" data-extension-id="<?= htmlspecialchars($selectedItem['id_extension_contract']) ?>">
                                            : <?= htmlspecialchars($selectedItem['id_extension_contract']) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">اسم صاحب العقد</label>
                                        <span class="detail-value">: <?= htmlspecialchars($selectedItem['main_contract']['name_ar_contract']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">الاسم الوظيفي</label>
                                        <span class="detail-value">: <?= htmlspecialchars($selectedItem['main_contract']['name_Job']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">تاريخ الإصدار</label>
                                        <span class="detail-value">: <?= date('Y-m-d', strtotime($selectedItem['version_date'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">تاريخ بدء العقد</label>
                                        <span class="detail-value contract-date" data-date="<?= date('Y-m-d', strtotime($selectedItem['start_date_contract'])) ?>">
                                            : <?= date('Y-m-d', strtotime($selectedItem['start_date_contract'])) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">تاريخ انتهاء العقد</label>
                                        <span class="detail-value contract-date" data-date="<?= $selectedItem['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['end_date_contract'])) : '' ?>">
                                            : <?= $selectedItem['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['end_date_contract'])) : 'مفتوح' ?>
                                        </span>
                                    </div>
                                </div>
                                <?php
                                $todoListData = json_decode($selectedItem['main_contract']['data_todo_list_contract'], true);
                                ?>
                                <div class="col-md-4">
                                    <div class="detail-item">
                                        <label class="detail-label">نوع العقد</label>
                                        <span class="detail-value">: <?= isset($todoListData['evaluation']['percentageEvaluation']) && $todoListData['evaluation']['percentageEvaluation'] === 'yes' ? 'أجر شهري' : 'أجر يومي' ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">سجلات الحضور التي تم إنشاؤها مسبقًا</h5>
                        
                        <?php if (!empty($selectedItem['diapersRecords'])): ?>
                            <div class="extension-list">
                                <?php foreach ($selectedItem['diapersRecords'] as $record): ?>
                                    <div class="extension-item">
                                        <div class="extension-info">
                                            <?php if (isset($record['id_contract'])): ?>
                                            <div>
                                                <span class="detail-label">العقد الرئيسي:</span>
                                                <span class="detail-value"><?= htmlspecialchars($record['id_contract']) ?></span>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (isset($record['extended_contract']) && $record['extended_contract']): ?>
                                            <div>
                                                <span class="detail-label">تمديد العقد:</span>
                                                <span class="detail-value"><?= htmlspecialchars($record['extended_contract']) ?></span>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (isset($record['start_date'])): ?>
                                            <div>
                                                <span class="detail-label">تاريخ البداية:</span>
                                                <span class="detail-value"><?= htmlspecialchars(date('Y-m-d', strtotime($record['start_date']))) ?></span>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (isset($record['end_date'])): ?>
                                            <div>
                                                <span class="detail-label">تاريخ النهاية:</span>
                                                <span class="detail-value"><?= htmlspecialchars(date('Y-m-d', strtotime($record['end_date']))) ?></span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="no-extensions">
                                <i class="bi bi-info-circle" style="font-size: 2rem;"></i>
                                <p class="mt-2">لم يتم تسجيل أي سجلات حضور لهذا العقد حتى الآن.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">فترة الاحتفاظ بجدول الحضور</h5>
                        
                        <div class="retention-period-container d-flex">
                            <div class="calendar-section ms-4">
                                <div class="calendar-wrapper">
                                    <div class="calendar-navigation">
                                        <button type="button" class="btn btn-sm btn-outline-secondary prev-month">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                        <span class="current-month"></span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary next-month">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </div>
                                    <div class="calendar-header">
                                        <div class="calendar-day-header">Sun</div>
                                        <div class="calendar-day-header">Mon</div>
                                        <div class="calendar-day-header">Tue</div>
                                        <div class="calendar-day-header">Wed</div>
                                        <div class="calendar-day-header">Thu</div>
                                        <div class="calendar-day-header">Fri</div>
                                        <div class="calendar-day-header">Sat</div>
                                    </div>
                                    <div class="calendar-container"></div>
                                </div>
                            </div>

                            <div class="date-fields-section flex-grow-1">
                                <div class="mb-3">
                                    <label for="retention_start_date" class="form-label fw-bold">تاريخ البداية</label>
                                    <input type="text" 
                                           class="form-control retention-date-input" 
                                           id="retention_start_date" 
                                           readonly 
                                           placeholder="اختر من التقويم">
                                </div>
                                <div class="mb-3">
                                    <label for="retention_end_date" class="form-label fw-bold">تاريخ النهاية</label>
                                    <input type="text" 
                                           class="form-control retention-date-input" 
                                           id="retention_end_date" 
                                           readonly 
                                           placeholder="اختر من التقويم">
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-primary" id="generateAttendanceData">
                                        <i class="bi bi-calendar-check me-2"></i>إنشاء سجل الحضور
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Log Table Section -->
                <div class="card mb-4" id="attendanceTableSection" style="display: none;">
                    <div class="card-body">
                        <h5 class="card-title mb-4">سجل الحضور</h5>
                        
                        <!-- Add Default Values Section -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3">إدخال البيانات تلقائياً</h6>
                                <div class="row g-3">
                                    <!-- قائمة الأيام التي سيتم تطبيق الإدخال التلقائي عليها -->
                                    <div class="col-md-12 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-subtitle mb-3">الأيام المشمولة في الإدخال التلقائي</h6>
                                                <div class="alert alert-info mb-3">
                                                    <i class="bi bi-info-circle me-2"></i>
                                                    هذه هي قائمة الأيام التي سيتم تطبيق الإدخال التلقائي عليها عند النقر على زر "تطبيق على جميع الأيام"
                                                </div>
                                                <div class="days-actions d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllDays">
                                                            <i class="bi bi-check-all me-1"></i>تحديد الكل
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllDays">
                                                            <i class="bi bi-x-lg me-1"></i>إلغاء تحديد الكل
                                                        </button>
                                                    </div>
                                                    <span class="badge bg-primary" id="selectedDaysCount">0 أيام محددة</span>
                                                </div>
                                                <div class="days-list-container mt-2" style="max-height:300px; overflow-y:auto; border:1px solid var(--border-color); border-radius:0.5rem; position: relative;">
                                                    <div class="row" id="includedDaysList">
                                                        <div class="col-12 text-center py-3 text-muted">
                                                            <i class="bi bi-calendar3"></i> قم بإنشاء سجل الحضور أولاً لعرض الأيام المشمولة
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Regular Shift -->
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-subtitle mb-3">الدوام العادي</h6>
                                                <div class="row g-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label">الحضور</label>
                                                        <div class="time-input-group">
                                                            <select class="form-select form-select-sm" id="defaultRegularCheckInStatus">
                                                                <option value="-" selected>-</option>
                                                                <option value="حاضر">حاضر</option>
                                                                <option value="غائب">غائب</option>
                                                                <option value="إجازة">إجازة</option>
                                                                <option value="غير مدفوع">غير مدفوع</option>
                                                            </select>
                                                            <input type="time" class="form-control form-control-sm" id="defaultRegularCheckInTime">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">الانصراف</label>
                                                        <div class="time-input-group">
                                                            <select class="form-select form-select-sm" id="defaultRegularCheckOutStatus">
                                                                <option value="-" selected>-</option>
                                                                <option value="منصرف">منصرف</option>
                                                                <option value="غير منصرف">غير منصرف</option>
                                                            </select>
                                                            <input type="time" class="form-control form-control-sm" id="defaultRegularCheckOutTime">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Overtime Shift -->
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-subtitle mb-3">الدوام الإضافي</h6>
                                                <div class="row g-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label">الحضور</label>
                                                        <div class="time-input-group">
                                                            <select class="form-select form-select-sm" id="defaultOvertimeCheckInStatus">
                                                                <option value="-" selected>-</option>
                                                                <option value="حاضر">حاضر</option>
                                                                <option value="غائب">غائب</option>
                                                            </select>
                                                            <input type="time" class="form-control form-control-sm" id="defaultOvertimeCheckInTime">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">الانصراف</label>
                                                        <div class="time-input-group">
                                                            <select class="form-select form-select-sm" id="defaultOvertimeCheckOutStatus">
                                                                <option value="-" selected>-</option>
                                                                <option value="منصرف">منصرف</option>
                                                                <option value="غير منصرف">غير منصرف</option>
                                                            </select>
                                                            <input type="time" class="form-control form-control-sm" id="defaultOvertimeCheckOutTime">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Notes and Apply Button -->
                                    <div class="col-12">
                                        <div class="row g-3 align-items-end">
                                            <div class="col-md-9">
                                                <label class="form-label">ملاحظات افتراضية</label>
                                                <textarea class="form-control" id="defaultNotes" rows="1">-</textarea>
                                            </div>
                                            <div class="col-md-3 text-end">
                                                <button type="button" class="btn btn-primary w-100" id="applyDefaultValues">
                                                    <i class="bi bi-check2-all me-2"></i>تطبيق على جميع الأيام
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="attendanceTable">
                                <thead>
                                    <tr>
                                        <th>اليوم</th>
                                        <th>التاريخ</th>
                                        <th colspan="2">الدوام العادي</th>
                                        <th colspan="2">الدوام الإضافي</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                    <tr>
                                        <th></th>
                                        <th></th>
                                        <th>وقت الحضور</th>
                                        <th>وقت الانصراف</th>
                                        <th>وقت الحضور</th>
                                        <th>وقت الانصراف</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>

                        <!-- Data Summary Section -->
                        <div class="mt-4" id="capturedDataDisplay">
                            <h5 class="mb-3">ملخص البيانات</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>رقم المشروع</th>
                                        <th>رقم العقد</th>
                                        <th>رقم تمديد العقد</th>
                                        <th>تاريخ البداية</th>
                                        <th>تاريخ النهاية</th>
                                    </tr>
                                    <tr id="dataRow">
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="mt-3 text-center">
                                <form id="saveDataForm" method="POST">
                                    <input type="hidden" name="attendance_data" id="attendanceJsonData">
                                    <input type="hidden" name="basic_data" id="basicJsonData">
                                    <input type="hidden" name="save_permanent_diapers" value="1">
                                    <button type="submit" class="btn btn-primary" id="saveDataBtn" style="width: 80%; padding: 12px; font-size: 1.2rem; border-radius: 6px;">
                                        <i class="bi bi-save me-2"></i>حفظ حافظة الدوام
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- JSON Code Display Section -->
                <div class="card mb-4" id="jsonDisplaySection" style="display: none;">
                    <div class="card-body">
                        <h5 class="card-title mb-4 d-flex align-items-center justify-content-between">
                            عرض بيانات JSON
                            <button class="btn btn-link p-0 text-decoration-none" type="button" data-bs-toggle="collapse" data-bs-target="#jsonDisplayCollapse" aria-expanded="false" aria-controls="jsonDisplayCollapse">
                                <i class="bi bi-chevron-down"></i>
                            </button>
                        </h5>
                        <div class="collapse" id="jsonDisplayCollapse">
                            <pre class="json-display"><code id="jsonDisplay"></code></pre>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script>
        // Global variable to store the attendance data
        let globalAttendanceData = null;

        // Theme handling
        function setTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            // Update theme toggle button icon
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                const icon = themeToggle.querySelector('i');
                if (theme === 'dark') {
                    icon.classList.remove('bi-sun-fill');
                    icon.classList.add('bi-moon-fill');
                } else {
                    icon.classList.remove('bi-moon-fill');
                    icon.classList.add('bi-sun-fill');
                }
            }
        }

        // Initialize theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);

        // Theme toggle handler
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Initialize Select2
        $(document).ready(function() {
            // Initialize Select2 for project dropdown
            $('#project_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر المشروع',
                allowClear: true
            });

            // Initialize Select2 for contract dropdown with custom formatting
            $('#contract_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر العقد',
                allowClear: true,
                templateResult: formatContract,
                templateSelection: formatContractSelection
            });

            // Custom formatting for contract options in dropdown
            function formatContract(contract) {
                if (!contract.id) {
                    return contract.text;
                }

                var isExtension = contract.element.className.includes('extension-option');
                var text = isExtension ? contract.text : '• ' + contract.text;

                var $contract = $(
                    '<span class="contract-tree-item ' + 
                    (isExtension ? 'contract-extension' : 'contract-main') + 
                    '">' + text + '</span>'
                );

                return $contract;
            }

            // Custom formatting for selected contract
            function formatContractSelection(contract) {
                if (!contract.id) {
                    return contract.text;
                }
                var isExtension = contract.element.className.includes('extension-option');
                return isExtension ? contract.text : '• ' + contract.text;
            }

            // Project selection change handler
            $('#project_id').on('change', function() {
                const projectId = $(this).val();
                const contractSelect = $('#contract_id');
                
                // Reset and disable contract select if no project selected
                if (!projectId) {
                    contractSelect.empty().prop('disabled', true).trigger('change');
                    return;
                }

                // Enable contract select and load data
                contractSelect.prop('disabled', false);
                
                $.get(`?action=get_contracts&project_id=${projectId}`, function(data) {
                    contractSelect.empty().append('<option value="">اختر العقد</option>');
                    
                    data.forEach(contract => {
                        // Add main contract
                        contractSelect.append($('<option>', {
                            value: contract.id,
                            text: contract.text,
                            class: 'contract-main-option'
                        }));
                        
                        // Add extensions if any
                        if (contract.extensions && contract.extensions.length > 0) {
                            contract.extensions.forEach((ext, index) => {
                                contractSelect.append($('<option>', {
                                    value: ext.id,
                                    text: ext.text,
                                    class: 'extension-option' + 
                                          (index === 0 ? ' first-extension' : '') + 
                                          (index === contract.extensions.length - 1 ? ' last-extension' : '')
                                }));
                            });
                        }
                    });
                    
                    contractSelect.trigger('change');
                });
            });

            // Form submission handler
            $('#searchForm').on('submit', function(e) {
                if (!$(this).find(':submit').hasClass('clicked')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Submit button click handler
            $('#searchForm :submit').on('click', function() {
                $(this).addClass('clicked');
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.custom-alert').fadeOut('slow');
            }, 5000);

            // Calendar functionality
            let isSelecting = false;
            let selectedStartDate = null;
            let selectedEndDate = null;
            let tempEndDate = null;
            const today = new Date();
            let currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            let contractStartDate = null;
            let contractEndDate = null;
            let existingRecords = [];

            // Initialize contract dates based on selected item
            <?php if ($selectedItem): ?>
                <?php if ($selectedItem['type'] === 'contract'): ?>
                    contractStartDate = new Date('<?php echo $selectedItem['contractDetails']['start_date_contract']; ?>');
                    <?php if (!empty($selectedItem['contractDetails']['end_date_contract'])): ?>
                        contractEndDate = new Date('<?php echo $selectedItem['contractDetails']['end_date_contract']; ?>');
                    <?php endif; ?>
                <?php elseif ($selectedItem['type'] === 'extension'): ?>
                    contractStartDate = new Date('<?php echo $selectedItem['start_date_contract']; ?>');
                    <?php if (!empty($selectedItem['end_date_contract'])): ?>
                        contractEndDate = new Date('<?php echo $selectedItem['end_date_contract']; ?>');
                    <?php endif; ?>
                <?php endif; ?>

                // Initialize existing records
                <?php if (!empty($selectedItem['diapersRecords'])): ?>
                    existingRecords = [
                        <?php foreach ($selectedItem['diapersRecords'] as $record): ?>
                            {
                                start: new Date('<?php echo $record['start_date']; ?>'),
                                end: new Date('<?php echo $record['end_date']; ?>')
                            },
                        <?php endforeach; ?>
                    ];
                <?php endif; ?>
            <?php endif; ?>

            function isDateDisabled(date) {
                // Set time to midnight for proper comparison
                const compareDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                
                // Check if date is before contract start date
                if (contractStartDate && compareDate < contractStartDate) {
                    return true;
                }

                // Check if date is after contract end date (if exists)
                if (contractEndDate && compareDate > contractEndDate) {
                    return true;
                }

                // Check if date overlaps with existing records
                for (const record of existingRecords) {
                    if (compareDate >= record.start && compareDate <= record.end) {
                        return true;
                    }
                }

                return false;
            }

            function formatDate(date) {
                if (!date) return '';
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            function updateSelectedPeriod() {
                // Clear fields if no selection
                if (!selectedStartDate) {
                    $('#retention_start_date, #retention_end_date').val('');
                    return;
                }

                // If we have both dates, ensure they're in the correct order
                if (selectedEndDate && selectedStartDate > selectedEndDate) {
                    const temp = selectedStartDate;
                    selectedStartDate = selectedEndDate;
                    selectedEndDate = temp;
                }

                // Format dates for display
                const startDateStr = formatDate(selectedStartDate);
                const endDateStr = formatDate(selectedEndDate || selectedStartDate);

                // Update the input fields with formatted dates
                $('#retention_start_date').val(startDateStr);
                $('#retention_end_date').val(endDateStr);

                // Update data summary
                updateDataSummary();

                // Also update the data summary fields
                const dataRow = $('#dataRow');
                dataRow.find('td:eq(3)').text(startDateStr || '-');
                dataRow.find('td:eq(4)').text(endDateStr || '-');
                
                // تحديث قائمة الأيام المشمولة إذا تم بالفعل إنشاء جدول الحضور
                if (globalAttendanceData && globalAttendanceData.AttendanceData) {
                    updateIncludedDaysList();
                }
            }

            function renderCalendar() {
                const calendarContainer = $('.calendar-container');
                const year = currentMonth.getFullYear();
                const month = currentMonth.getMonth();
                
                // Update month display
                $('.current-month').text(currentMonth.toLocaleString('en-US', { 
                    month: 'long',
                    year: 'numeric'
                }));

                const firstDay = new Date(year, month, 1);
                const lastDay = new Date(year, month + 1, 0);
                const startingDay = firstDay.getDay();
                const monthLength = lastDay.getDate();

                const totalDays = startingDay + monthLength;
                const rows = Math.ceil(totalDays / 7);
                const totalCells = rows * 7;

                let calendarHtml = '';

                // Create calendar grid
                for (let i = 0; i < totalCells; i++) {
                    const day = i - startingDay + 1;
                    const date = new Date(year, month, day);
                    const isCurrentMonth = day > 0 && day <= monthLength;
                    const dateString = isCurrentMonth ? `${date.getFullYear()}-${String(date.getMonth()+1).padStart(2,'0')}-${String(date.getDate()).padStart(2,'0')}` : '';
                    
                    let classes = ['calendar-day'];
                    
                    if (!isCurrentMonth) {
                        classes.push('out-of-period');
                    } else {
                        // Check if date should be disabled
                        if (isDateDisabled(date)) {
                            classes.push('disabled');
                        }

                        // Add today class if it's today
                        if (date.toDateString() === new Date().toDateString()) {
                            classes.push('today');
                        }

                        // Show temporary selection during drag
                        if (isSelecting && selectedStartDate && !isDateDisabled(date)) {
                            const endDate = tempEndDate || selectedStartDate;
                            const minDate = new Date(Math.min(selectedStartDate.getTime(), endDate.getTime()));
                            const maxDate = new Date(Math.max(selectedStartDate.getTime(), endDate.getTime()));
                            
                            const compareDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                            if (compareDate >= minDate && compareDate <= maxDate) {
                                classes.push('in-range');
                            }
                        }
                        // Show permanent selection
                        else if (selectedStartDate && selectedEndDate && !isDateDisabled(date)) {
                            const compareDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                            if (compareDate >= selectedStartDate && compareDate <= selectedEndDate) {
                                classes.push('in-range');
                            }
                        }
                    }
                    
                    // Add selected class for start and end dates
                    if (isCurrentMonth && !isDateDisabled(date)) {
                        const compareDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                        if (selectedStartDate && compareDate.getTime() === new Date(selectedStartDate.getFullYear(), selectedStartDate.getMonth(), selectedStartDate.getDate()).getTime()) {
                            classes.push('selected start-date');
                        }
                        if (selectedEndDate && compareDate.getTime() === new Date(selectedEndDate.getFullYear(), selectedEndDate.getMonth(), selectedEndDate.getDate()).getTime()) {
                            classes.push('selected end-date');
                        }
                    }

                    calendarHtml += `
                        <div class="${classes.join(' ')}" 
                             data-date="${dateString}"
                             ${isCurrentMonth ? `title="${dateString}${isDateDisabled(date) ? ' (غير متاح)' : ''}"` : ''}>
                            ${isCurrentMonth ? day : ''}
                        </div>`;
                }

                calendarContainer.html(calendarHtml);

                // Attach mouse event handlers
                const calendarDays = $('.calendar-day:not(.out-of-period):not(.disabled)');
                
                // Mouse down event - start selection
                calendarDays.off('mousedown touchstart').on('mousedown touchstart', function(e) {
                    e.preventDefault(); // Prevent text selection
                    if ($(this).hasClass('out-of-period') || $(this).hasClass('disabled')) return;
                    
                    isSelecting = true;
                    const dateStr = $(this).data('date');
                    if (dateStr) {
                        selectedStartDate = new Date(dateStr);
                        selectedStartDate.setHours(0, 0, 0, 0);
                        selectedEndDate = null;
                        tempEndDate = null;
                        
                        // Update start date field immediately
                        $('#retention_start_date').val(formatDate(selectedStartDate));
                        $('#retention_end_date').val(''); // Clear end date when starting new selection
                        
                        renderCalendar();
                    }
                });

                // Mouse move event - update selection
                calendarDays.off('mousemove touchmove').on('mousemove touchmove', function(e) {
                    if (!isSelecting || $(this).hasClass('out-of-period') || $(this).hasClass('disabled')) return;
                    
                    const dateStr = $(this).data('date');
                    if (dateStr) {
                        e.preventDefault();
                        tempEndDate = new Date(dateStr);
                        tempEndDate.setHours(0, 0, 0, 0);
                        
                        // Update both date fields during drag
                        const startStr = formatDate(selectedStartDate);
                        const endStr = formatDate(tempEndDate);
                        
                        // Determine which date should be start and which should be end
                        if (tempEndDate < selectedStartDate) {
                            $('#retention_start_date').val(endStr);
                            $('#retention_end_date').val(startStr);
                        } else {
                            $('#retention_start_date').val(startStr);
                            $('#retention_end_date').val(endStr);
                        }
                        
                        renderCalendar();
                    }
                });

                // Mouse up event - end selection
                $(document).off('mouseup touchend').on('mouseup touchend', function() {
                    if (isSelecting) {
                        isSelecting = false;
                        if (tempEndDate) {
                            // If end date is before start date, swap them
                            if (tempEndDate < selectedStartDate) {
                                const temp = selectedStartDate;
                                selectedStartDate = tempEndDate;
                                selectedEndDate = temp;
                            } else {
                                selectedEndDate = tempEndDate;
                            }
                            tempEndDate = null;
                            updateSelectedPeriod();
                            renderCalendar();
                        } else if (selectedStartDate) {
                            // If only start date is selected (single click)
                            selectedEndDate = new Date(selectedStartDate);
                            updateSelectedPeriod();
                            renderCalendar();
                        }
                    }
                });

                // Handle mouse leaving calendar
                $('.calendar-wrapper').off('mouseleave').on('mouseleave', function() {
                    if (isSelecting && tempEndDate) {
                        renderCalendar();
                    }
                });

                // Prevent text selection during drag
                $('.calendar-wrapper').off('selectstart').on('selectstart', function(e) {
                    if (isSelecting) {
                        e.preventDefault();
                    }
                });
            }

            // Navigation handlers
            $('.prev-month').click(function() {
                currentMonth.setMonth(currentMonth.getMonth() - 1);
                renderCalendar();
            });

            $('.next-month').click(function() {
                currentMonth.setMonth(currentMonth.getMonth() + 1);
                renderCalendar();
            });

            // Initialize calendar
            renderCalendar();

            // Generate Attendance Data
            let attendanceData = null;

            function getDayName(date) {
                const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                return days[date.getDay()];
            }

            function generateAttendanceData() {
                if (!selectedStartDate || !selectedEndDate) {
                    alert('الرجاء تحديد تاريخ البداية وتاريخ النهاية');
                    return;
                }

                const startDate = new Date(selectedStartDate);
                const endDate = new Date(selectedEndDate);
                attendanceData = { AttendanceData: {} };

                let currentDate = new Date(startDate);

                while (currentDate <= endDate) {
                    const dayNumber = currentDate.getDate();
                    
                    attendanceData.AttendanceData[dayNumber] = {
                        BasicInfo: {
                            DayName: getDayName(currentDate),
                            DayNumber: dayNumber,
                            MonthNumber: currentDate.getMonth() + 1
                        },
                        RegularShift: {
                            CheckIn: {
                                Status: "-",
                                Time: "00:00"
                            },
                            CheckOut: {
                                Status: "-",
                                Time: "00:00"
                            }
                        },
                        OvertimeShift: {
                            CheckIn: {
                                Status: "-",
                                Time: "00:00"
                            },
                            CheckOut: {
                                Status: "-",
                                Time: "00:00"
                            }
                        },
                        AdditionalNotes: "-"
                    };
                    
                    currentDate.setDate(currentDate.getDate() + 1);
                }

                globalAttendanceData = attendanceData;
                updateJsonDisplay();
                updateAttendanceTable();
                updateDataSummary();
                
                // After generating the attendance table, apply the disabled attribute to time inputs where needed
                $('#attendanceTable select[data-field="Status"]').each(function() {
                    const $select = $(this);
                    const status = $select.val();
                    
                    if (status === 'غائب' || status === 'إجازة' || status === 'غير مدفوع') {
                        const $timeInput = $select.closest('.time-input-group').find('input[type="time"]');
                        $timeInput.prop('disabled', true);
                        $timeInput.addClass('time-placeholder');
                    }
                });
                
                // Show the sections
                $('#attendanceTableSection, #jsonDisplaySection').show();

                // Add smooth scroll to attendance table section
                $('#attendanceTableSection')[0].scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'start'
                });
                
                // بعد إنشاء بيانات الجدول، قم بتحديث قائمة الأيام المشمولة
                updateIncludedDaysList();
            }

            function updateJsonDisplay() {
                const jsonString = JSON.stringify(globalAttendanceData, null, 2);
                $('#jsonDisplay').text(jsonString);
            }

            function updateAttendanceTable() {
                const tbody = $('#attendanceTable tbody');
                tbody.empty();

                // Sort the days to ensure they appear in order
                const sortedDays = Object.keys(globalAttendanceData.AttendanceData)
                    .map(Number)
                    .sort((a, b) => a - b);

                sortedDays.forEach(day => {
                    const data = globalAttendanceData.AttendanceData[day];
                    const tr = $('<tr>');
                    
                    // Day name and date
                    tr.append(`<td>${data.BasicInfo.DayName}</td>`);
                    tr.append(`<td>${data.BasicInfo.DayNumber}/${data.BasicInfo.MonthNumber}</td>`);
                    
                    // Regular shift check-in
                    const regularCheckInDisabled = (data.RegularShift.CheckIn.Status === 'غائب' || 
                                                   data.RegularShift.CheckIn.Status === 'إجازة' || 
                                                   data.RegularShift.CheckIn.Status === 'غير مدفوع');
                    
                    const regularCheckInClass = regularCheckInDisabled && data.RegularShift.CheckIn.Time === '00:00' ? 
                                              'form-control form-control-sm attendance-input time-placeholder' : 
                                              'form-control form-control-sm attendance-input';
                    
                    tr.append(`
                        <td>
                            <div class="time-input-group">
                                <select class="form-select form-select-sm attendance-input" 
                                        data-shift="Regular" 
                                        data-type="CheckIn" 
                                        data-field="Status" 
                                        data-day="${day}">
                                    <option value="-" ${data.RegularShift.CheckIn.Status === '-' ? 'selected' : ''}>-</option>
                                    <option value="حاضر" ${data.RegularShift.CheckIn.Status === 'حاضر' ? 'selected' : ''}>حاضر</option>
                                    <option value="غائب" ${data.RegularShift.CheckIn.Status === 'غائب' ? 'selected' : ''}>غائب</option>
                                    <option value="إجازة" ${data.RegularShift.CheckIn.Status === 'إجازة' ? 'selected' : ''}>إجازة</option>
                                    <option value="غير مدفوع" ${data.RegularShift.CheckIn.Status === 'غير مدفوع' ? 'selected' : ''}>غير مدفوع</option>
                                </select>
                                <input type="time" 
                                       class="${regularCheckInClass}" 
                                       value="${data.RegularShift.CheckIn.Time}"
                                       data-shift="Regular" 
                                       data-type="CheckIn" 
                                       data-field="Time" 
                                       data-day="${day}"
                                       ${regularCheckInDisabled ? 'disabled' : ''}>
                            </div>
                        </td>
                    `);
                    
                    // Regular shift check-out
                    const regularCheckOutDisabled = data.RegularShift.CheckOut.Status === 'غير منصرف';
                    
                    const regularCheckOutClass = regularCheckOutDisabled && data.RegularShift.CheckOut.Time === '00:00' ? 
                                               'form-control form-control-sm attendance-input time-placeholder' : 
                                               'form-control form-control-sm attendance-input';
                    
                    tr.append(`
                        <td>
                            <div class="time-input-group">
                                <select class="form-select form-select-sm attendance-input" 
                                        data-shift="Regular" 
                                        data-type="CheckOut" 
                                        data-field="Status" 
                                        data-day="${day}">
                                    <option value="-" ${data.RegularShift.CheckOut.Status === '-' ? 'selected' : ''}>-</option>
                                    <option value="منصرف" ${data.RegularShift.CheckOut.Status === 'منصرف' ? 'selected' : ''}>منصرف</option>
                                    <option value="غير منصرف" ${data.RegularShift.CheckOut.Status === 'غير منصرف' ? 'selected' : ''}>غير منصرف</option>
                                </select>
                                <input type="time" 
                                       class="${regularCheckOutClass}" 
                                       value="${data.RegularShift.CheckOut.Time}"
                                       data-shift="Regular" 
                                       data-type="CheckOut" 
                                       data-field="Time" 
                                       data-day="${day}"
                                       ${regularCheckOutDisabled ? 'disabled' : ''}>
                            </div>
                        </td>
                    `);
                    
                    // Overtime shift check-in
                    const overtimeCheckInDisabled = (data.OvertimeShift.CheckIn.Status === 'غائب' || 
                                                    data.OvertimeShift.CheckIn.Status === 'إجازة' || 
                                                    data.OvertimeShift.CheckIn.Status === 'غير مدفوع');
                    
                    const overtimeCheckInClass = overtimeCheckInDisabled && data.OvertimeShift.CheckIn.Time === '00:00' ? 
                                               'form-control form-control-sm attendance-input time-placeholder' : 
                                               'form-control form-control-sm attendance-input';
                    
                    tr.append(`
                        <td>
                            <div class="time-input-group">
                                <select class="form-select form-select-sm attendance-input" 
                                        data-shift="Overtime" 
                                        data-type="CheckIn" 
                                        data-field="Status" 
                                        data-day="${day}">
                                    <option value="-" ${data.OvertimeShift.CheckIn.Status === '-' ? 'selected' : ''}>-</option>
                                    <option value="حاضر" ${data.OvertimeShift.CheckIn.Status === 'حاضر' ? 'selected' : ''}>حاضر</option>
                                    <option value="غائب" ${data.OvertimeShift.CheckIn.Status === 'غائب' ? 'selected' : ''}>غائب</option>
                                    <option value="غير مدفوع" ${data.OvertimeShift.CheckIn.Status === 'غير مدفوع' ? 'selected' : ''}>غير مدفوع</option>
                                </select>
                                <input type="time" 
                                       class="${overtimeCheckInClass}" 
                                       value="${data.OvertimeShift.CheckIn.Time}"
                                       data-shift="Overtime" 
                                       data-type="CheckIn" 
                                       data-field="Time" 
                                       data-day="${day}"
                                       ${overtimeCheckInDisabled ? 'disabled' : ''}>
                            </div>
                        </td>
                    `);
                    
                    // Overtime shift check-out
                    const overtimeCheckOutDisabled = data.OvertimeShift.CheckOut.Status === 'غير منصرف';
                    
                    const overtimeCheckOutClass = overtimeCheckOutDisabled && data.OvertimeShift.CheckOut.Time === '00:00' ? 
                                                'form-control form-control-sm attendance-input time-placeholder' : 
                                                'form-control form-control-sm attendance-input';
                    
                    tr.append(`
                        <td>
                            <div class="time-input-group">
                                <select class="form-select form-select-sm attendance-input" 
                                        data-shift="Overtime" 
                                        data-type="CheckOut" 
                                        data-field="Status" 
                                        data-day="${day}">
                                    <option value="-" ${data.OvertimeShift.CheckOut.Status === '-' ? 'selected' : ''}>-</option>
                                    <option value="منصرف" ${data.OvertimeShift.CheckOut.Status === 'منصرف' ? 'selected' : ''}>منصرف</option>
                                    <option value="غير منصرف" ${data.OvertimeShift.CheckOut.Status === 'غير منصرف' ? 'selected' : ''}>غير منصرف</option>
                                </select>
                                <input type="time" 
                                       class="${overtimeCheckOutClass}" 
                                       value="${data.OvertimeShift.CheckOut.Time}"
                                       data-shift="Overtime" 
                                       data-type="CheckOut" 
                                       data-field="Time" 
                                       data-day="${day}"
                                       ${overtimeCheckOutDisabled ? 'disabled' : ''}>
                            </div>
                        </td>
                    `);
                    
                    // Additional notes
                    tr.append(`
                        <td>
                            <textarea class="form-control form-control-sm attendance-input" 
                                      data-field="AdditionalNotes" 
                                      data-day="${day}">${data.AdditionalNotes}</textarea>
                        </td>
                    `);
                    
                    tbody.append(tr);
                });

                // Remove any existing event handlers
                $('#attendanceTable').off('change input', '.attendance-input');
                
                // Attach event handlers for real-time updates
                $('#attendanceTable').on('change input', '.attendance-input', function() {
                    const $this = $(this);
                    const day = $this.data('day');
                    const field = $this.data('field');
                    const shift = $this.data('shift');
                    const type = $this.data('type');
                    const value = $this.val();

                    if (field === 'AdditionalNotes') {
                        globalAttendanceData.AttendanceData[day][field] = value;
                    } else {
                        globalAttendanceData.AttendanceData[day][shift + 'Shift'][type][field] = value;
                    }

                    // Update JSON display immediately
                    updateJsonDisplay();
                    
                    // Update the hidden form input with the latest attendance data
                    $('#attendanceJsonData').val(JSON.stringify(globalAttendanceData));
                });

                // After the table is updated, apply the styling to time inputs
                updateTimePlaceholders();
            }

            // Copy JSON button handler
            $('#copyJsonBtn').on('click', function() {
                const jsonString = JSON.stringify(globalAttendanceData, null, 2);
                navigator.clipboard.writeText(jsonString).then(() => {
                    const $btn = $(this);
                    const originalText = $btn.html();
                    $btn.html('<i class="bi bi-check2 me-2"></i>تم النسخ');
                    setTimeout(() => {
                        $btn.html(originalText);
                    }, 2000);
                });
            });

            // Generate attendance data button handler
            $('#generateAttendanceData').on('click', generateAttendanceData);
            
            // Function to capture and store data
            function capturePageData() {
                const data = {
                    id_Project: $('#project_id').val(),
                    id_contract: null,
                    id_extension_contract: null,
                    start_date_permanent_diapers: null,
                    end_date_permanent_diapers: null
                };

                // Get contract details from the data attributes
                data.id_contract = $('.detail-value[data-contract-id]').attr('data-contract-id');
                data.id_extension_contract = $('.detail-value[data-extension-id]').attr('data-extension-id') || null;

                // Get retention period dates
                const selectedDates = $('.calendar-day.selected').map(function() {
                    return $(this).data('date');
                }).get();

                if (selectedDates.length > 0) {
                    data.start_date_permanent_diapers = selectedDates[0];
                    data.end_date_permanent_diapers = selectedDates[selectedDates.length - 1];
                }

                return data;
            }

            // Function to display data
            function updateDataSummary() {
                const data = capturePageData();
                const row = $('#dataRow');
                row.find('td:eq(0)').text(data.id_Project || '-');
                row.find('td:eq(1)').text(data.id_contract || '-');
                row.find('td:eq(2)').text(data.id_extension_contract || '-');
                row.find('td:eq(3)').text(data.start_date_permanent_diapers || '-');
                row.find('td:eq(4)').text(data.end_date_permanent_diapers || '-');
                
                // Update both JSON inputs with the latest data
                $('#basicJsonData').val(JSON.stringify(data));
                if (globalAttendanceData) {
                    $('#attendanceJsonData').val(JSON.stringify(globalAttendanceData));
                }
            }

            // Update data summary when relevant changes occur
            $('#project_id, #contract_id').on('change', updateDataSummary);
            
            // Update when calendar selection changes
            function updateSelectedPeriod() {
                // Existing code...
                updateDataSummary(); // Add this line to update summary
            }

            // Event handler for save button
            $('#saveDataForm').on('submit', function(e) {
                const basicData = JSON.parse($('#basicJsonData').val());
                const attendanceData = JSON.parse($('#attendanceJsonData').val());
                
                // Validate required fields
                if (!basicData.id_Project || !basicData.id_contract || 
                    !basicData.start_date_permanent_diapers || !basicData.end_date_permanent_diapers) {
                    e.preventDefault();
                    alert('يرجى التأكد من إدخال جميع البيانات المطلوبة');
                    return false;
                }

                // Validate attendance data
                if (!attendanceData || !attendanceData.AttendanceData) {
                    e.preventDefault();
                    alert('الرجاء إنشاء بيانات الحضور أولاً');
                    return false;
                }
            });

            // Calendar day click handler for single date selection
            $('.calendar-container').on('click', '.calendar-day:not(.out-of-period):not(.disabled)', function() {
                const dateStr = $(this).data('date');
                if (dateStr) {
                    if (!selectedStartDate) {
                        selectedStartDate = new Date(dateStr + 'T00:00:00');
                        selectedStartDate.setHours(0, 0, 0, 0);
                        selectedEndDate = null;
                    } else if (!selectedEndDate) {
                        selectedEndDate = new Date(dateStr + 'T00:00:00');
                        selectedEndDate.setHours(0, 0, 0, 0);
                        // Ensure start date is before end date
                        if (selectedStartDate > selectedEndDate) {
                            const temp = selectedStartDate;
                            selectedStartDate = selectedEndDate;
                            selectedEndDate = temp;
                        }
                    } else {
                        // Start new selection
                        selectedStartDate = new Date(dateStr + 'T00:00:00');
                        selectedStartDate.setHours(0, 0, 0, 0);
                        selectedEndDate = null;
                    }
                    updateSelectedPeriod();
                    renderCalendar();
                }
            });

            // Function to apply time-placeholder class based on values
            function updateTimePlaceholders() {
                $('input[type="time"]').each(function() {
                    const $input = $(this);
                    if ($input.val() === '00:00' && $input.prop('disabled')) {
                        $input.addClass('time-placeholder');
                    } else {
                        $input.removeClass('time-placeholder');
                    }
                });
            }
        });
    </script>
    <script>
        // Add event listener for collapse toggle button icon rotation
        $('#jsonDisplayCollapse').on('show.bs.collapse hide.bs.collapse', function (e) {
            const icon = $(this).siblings('.card-title').find('.bi');
            if (e.type === 'show') {
                icon.removeClass('bi-chevron-down').addClass('bi-chevron-up');
            } else {
                icon.removeClass('bi-chevron-up').addClass('bi-chevron-down');
            }
        });
    </script>
    <script>
        // Add the apply default values functionality
        $(document).ready(function() {
            // Add handler for default status changes
            $('#defaultRegularCheckInStatus, #defaultOvertimeCheckInStatus').on('change', function() {
                const status = $(this).val();
                const timeInputId = $(this).attr('id').replace('Status', 'Time');
                const $timeInput = $('#' + timeInputId);
                
                if (status === 'غائب' || status === 'إجازة' || status === 'غير مدفوع') {
                    $timeInput.val('00:00');
                    $timeInput.prop('disabled', true);
                    $timeInput.addClass('time-placeholder');
                } else {
                    $timeInput.prop('disabled', false);
                    $timeInput.removeClass('time-placeholder');
                }
            });
            
            // Add handler for default checkout status changes
            $('#defaultRegularCheckOutStatus, #defaultOvertimeCheckOutStatus').on('change', function() {
                const status = $(this).val();
                const timeInputId = $(this).attr('id').replace('Status', 'Time');
                const $timeInput = $('#' + timeInputId);
                
                if (status === 'غير منصرف') {
                    $timeInput.val('00:00');
                    $timeInput.prop('disabled', true);
                    $timeInput.addClass('time-placeholder');
                } else {
                    $timeInput.prop('disabled', false);
                    $timeInput.removeClass('time-placeholder');
                }
            });
            
            // Add handler for status changes to manage time inputs
            $('#attendanceTable').on('change', 'select[data-field="Status"]', function() {
                const $this = $(this);
                const status = $this.val();
                const day = $this.data('day');
                const shift = $this.data('shift');
                const type = $this.data('type');
                
                // Get the corresponding time input
                const $timeInput = $this.closest('.time-input-group').find('input[type="time"]');
                
                // Check if status is one of the special types that should disable time input
                if (status === 'غائب' || status === 'إجازة' || status === 'غير مدفوع' || 
                    (type === 'CheckOut' && status === 'غير منصرف')) {
                    // Set time to "00:00" which will be displayed as "-"
                    $timeInput.val('00:00');
                    $timeInput.prop('disabled', true);
                    $timeInput.addClass('time-placeholder');
                    
                    // Update the global data
                    globalAttendanceData.AttendanceData[day][shift + 'Shift'][type].Time = "00:00";
                    
                    // Trigger change to update JSON
                    $timeInput.trigger('change');
                } else {
                    // Enable time input for other status types
                    $timeInput.prop('disabled', false);
                    $timeInput.removeClass('time-placeholder');
                }
            });
            
            // إضافة معالج حدث لزر تحديد الكل
            $('#selectAllDays').on('click', function() {
                $('.day-selector').prop('checked', true).trigger('change');
                updateSelectedDaysCount();
            });
            
            // إضافة معالج حدث لزر إلغاء تحديد الكل
            $('#deselectAllDays').on('click', function() {
                $('.day-selector').prop('checked', false).trigger('change');
                updateSelectedDaysCount();
            });
            
            // Handle applying default values to selected days
            $('#applyDefaultValues').on('click', function() {
                if (!globalAttendanceData || !globalAttendanceData.AttendanceData) {
                    alert('الرجاء إنشاء جدول الحضور أولاً');
                    return;
                }
                
                // الحصول على قائمة الأيام المحددة
                const selectedDays = [];
                $('.day-selector:checked').each(function() {
                    selectedDays.push($(this).val());
                });
                
                if (selectedDays.length === 0) {
                    alert('الرجاء تحديد يوم واحد على الأقل لتطبيق القيم الافتراضية');
                    return;
                }

                // Get default values
                const defaultValues = {
                    RegularShift: {
                        CheckIn: {
                            Status: $('#defaultRegularCheckInStatus').val() || '-',
                            Time: $('#defaultRegularCheckInTime').val() || '00:00'
                        },
                        CheckOut: {
                            Status: $('#defaultRegularCheckOutStatus').val() || '-',
                            Time: $('#defaultRegularCheckOutTime').val() || '00:00'
                        }
                    },
                    OvertimeShift: {
                        CheckIn: {
                            Status: $('#defaultOvertimeCheckInStatus').val() || '-',
                            Time: $('#defaultOvertimeCheckInTime').val() || '00:00'
                        },
                        CheckOut: {
                            Status: $('#defaultOvertimeCheckOutStatus').val() || '-',
                            Time: $('#defaultOvertimeCheckOutTime').val() || '00:00'
                        }
                    },
                    AdditionalNotes: $('#defaultNotes').val() || '-'
                };
                
                // For special statuses, always set time to 00:00
                if (defaultValues.RegularShift.CheckIn.Status === 'غائب' || 
                    defaultValues.RegularShift.CheckIn.Status === 'إجازة' || 
                    defaultValues.RegularShift.CheckIn.Status === 'غير مدفوع') {
                    defaultValues.RegularShift.CheckIn.Time = '00:00';
                }
                
                if (defaultValues.RegularShift.CheckOut.Status === 'غير منصرف') {
                    defaultValues.RegularShift.CheckOut.Time = '00:00';
                }
                
                if (defaultValues.OvertimeShift.CheckIn.Status === 'غائب' || 
                    defaultValues.OvertimeShift.CheckIn.Status === 'إجازة' || 
                    defaultValues.OvertimeShift.CheckIn.Status === 'غير مدفوع') {
                    defaultValues.OvertimeShift.CheckIn.Time = '00:00';
                }
                
                if (defaultValues.OvertimeShift.CheckOut.Status === 'غير منصرف') {
                    defaultValues.OvertimeShift.CheckOut.Time = '00:00';
                }

                // Confirm before applying
                if (confirm(`هل أنت متأكد من تطبيق هذه القيم على الأيام المحددة (${selectedDays.length} يوم)؟`)) {
                    // Apply default values to selected days only
                    selectedDays.forEach(day => {
                        const currentData = globalAttendanceData.AttendanceData[day];
                        if (!currentData) return; // تخطي اليوم إذا لم يكن موجودًا
                        
                        const basicInfo = { ...currentData.BasicInfo }; // Create a copy of BasicInfo

                        // Update the global data structure
                        globalAttendanceData.AttendanceData[day] = {
                            BasicInfo: basicInfo,
                            RegularShift: JSON.parse(JSON.stringify(defaultValues.RegularShift)),
                            OvertimeShift: JSON.parse(JSON.stringify(defaultValues.OvertimeShift)),
                            AdditionalNotes: defaultValues.AdditionalNotes
                        };

                        // Function to update field and trigger change event
                        function updateFieldAndTrigger(selector, value) {
                            const field = $(selector);
                            field.val(value);
                            field.trigger('change');
                        }

                        // Update Regular Shift with change events
                        updateFieldAndTrigger(
                            `#attendanceTable [data-shift="Regular"][data-type="CheckIn"][data-field="Status"][data-day="${day}"]`,
                            defaultValues.RegularShift.CheckIn.Status
                        );
                        
                        // Update time fields, applying proper styling
                        const regularCheckInTimeInput = $(`#attendanceTable [data-shift="Regular"][data-type="CheckIn"][data-field="Time"][data-day="${day}"]`);
                        regularCheckInTimeInput.val(defaultValues.RegularShift.CheckIn.Time);
                        
                        // Apply disabled and styling if needed
                        if (defaultValues.RegularShift.CheckIn.Status === 'غائب' || 
                            defaultValues.RegularShift.CheckIn.Status === 'إجازة' || 
                            defaultValues.RegularShift.CheckIn.Status === 'غير مدفوع') {
                            regularCheckInTimeInput.prop('disabled', true);
                            regularCheckInTimeInput.addClass('time-placeholder');
                        } else {
                            regularCheckInTimeInput.prop('disabled', false);
                            regularCheckInTimeInput.removeClass('time-placeholder');
                        }
                        
                        regularCheckInTimeInput.trigger('change');
                        
                        // Continue with other updates
                        updateFieldAndTrigger(
                            `#attendanceTable [data-shift="Regular"][data-type="CheckOut"][data-field="Status"][data-day="${day}"]`,
                            defaultValues.RegularShift.CheckOut.Status
                        );
                        updateFieldAndTrigger(
                            `#attendanceTable [data-shift="Regular"][data-type="CheckOut"][data-field="Time"][data-day="${day}"]`,
                            defaultValues.RegularShift.CheckOut.Time
                        );

                        // Update Overtime Shift with change events
                        updateFieldAndTrigger(
                            `#attendanceTable [data-shift="Overtime"][data-type="CheckIn"][data-field="Status"][data-day="${day}"]`,
                            defaultValues.OvertimeShift.CheckIn.Status
                        );
                        
                        // Update time fields, applying proper styling
                        const overtimeCheckInTimeInput = $(`#attendanceTable [data-shift="Overtime"][data-type="CheckIn"][data-field="Time"][data-day="${day}"]`);
                        overtimeCheckInTimeInput.val(defaultValues.OvertimeShift.CheckIn.Time);
                        
                        // Apply disabled and styling if needed
                        if (defaultValues.OvertimeShift.CheckIn.Status === 'غائب' || 
                            defaultValues.OvertimeShift.CheckIn.Status === 'إجازة' || 
                            defaultValues.OvertimeShift.CheckIn.Status === 'غير مدفوع') {
                            overtimeCheckInTimeInput.prop('disabled', true);
                            overtimeCheckInTimeInput.addClass('time-placeholder');
                        } else {
                            overtimeCheckInTimeInput.prop('disabled', false);
                            overtimeCheckInTimeInput.removeClass('time-placeholder');
                        }
                        
                        overtimeCheckInTimeInput.trigger('change');
                        
                        updateFieldAndTrigger(
                            `#attendanceTable [data-shift="Overtime"][data-type="CheckOut"][data-field="Status"][data-day="${day}"]`,
                            defaultValues.OvertimeShift.CheckOut.Status
                        );
                        updateFieldAndTrigger(
                            `#attendanceTable [data-shift="Overtime"][data-type="CheckOut"][data-field="Time"][data-day="${day}"]`,
                            defaultValues.OvertimeShift.CheckOut.Time
                        );

                        // Update Additional Notes with change event
                        updateFieldAndTrigger(
                            `#attendanceTable [data-field="AdditionalNotes"][data-day="${day}"]`,
                            defaultValues.AdditionalNotes
                        );
                    });

                    // Update JSON display and form input
                    updateJsonDisplay();
                    $('#attendanceJsonData').val(JSON.stringify(globalAttendanceData));
                    
                    // تحديث قائمة الأيام المشمولة بعد تطبيق القيم الافتراضية
                    updateIncludedDaysList();

                    // Show success message
                    const successAlert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
                        .html(`
                            تم تطبيق القيم الافتراضية بنجاح على ${selectedDays.length} أيام محددة
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `)
                        .insertBefore('#attendanceTable');

                    // Auto-dismiss the alert after 3 seconds
                    setTimeout(() => {
                        successAlert.alert('close');
                    }, 3000);
                }
            });
        });
    </script>
    <script>
        // دالة لتحديث قائمة الأيام المشمولة في الإدخال التلقائي
        function updateIncludedDaysList() {
            if (!globalAttendanceData || !globalAttendanceData.AttendanceData) {
                return;
            }
            
            const daysList = $('#includedDaysList');
            daysList.empty();
            
            const days = Object.keys(globalAttendanceData.AttendanceData);
            
            if (days.length === 0) {
                daysList.html(`
                    <div class="col-12 text-center py-3 text-muted">
                        <i class="bi bi-calendar3"></i> لا توجد أيام مشمولة
                    </div>
                `);
                $('#selectedDaysCount').text('0 أيام محددة');
                return;
            }
            
            // رتب الأيام تصاعديًا حسب التاريخ
            days.sort((a, b) => {
                const dateA = new Date(a);
                const dateB = new Date(b);
                return dateA - dateB;
            });
            
            // إنشاء جدول متطور لعرض الأيام مع إمكانية التحديد
            const table = document.createElement('table');
            table.className = 'table table-striped table-hover table-sm';
            
            // إنشاء جسم الجدول
            const tbody = document.createElement('tbody');
            
            // إضافة صفوف الجدول
            for (let i = 0; i < days.length; i++) {
                const day = days[i];
                const dayInfo = globalAttendanceData.AttendanceData[day].BasicInfo;
                const date = new Date(day);
                const isWeekend = (date.getDay() === 5 || date.getDay() === 6); // الجمعة والسبت
                
                const row = document.createElement('tr');
                if (isWeekend) row.className = 'table-light';
                row.setAttribute('data-date', day);
                
                row.innerHTML = `
                    <td class="text-center fw-medium">${dayInfo.DayName}</td>
                    <td class="text-center">${day}</td>
                    <td class="text-center">
                        <div class="form-check d-flex justify-content-center">
                            <input class="form-check-input day-selector" type="checkbox" value="${day}" 
                                id="day_${day.replace(/-/g, '_')}" checked>
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            }
            
            table.appendChild(tbody);
            daysList.html('');
            daysList.append(table);
            
            // تحديث عدد الأيام المحددة
            updateSelectedDaysCount();
            
            // إضافة معالج الأحداث للاختيارات
            $('.day-selector').on('change', function() {
                // تحديث حالة الصف عند تغيير الاختيار
                const $row = $(this).closest('tr');
                if ($(this).is(':checked')) {
                    $row.addClass('selected');
                } else {
                    $row.removeClass('selected');
                }
                
                updateSelectedDaysCount();
            });
            
            // السماح بالنقر على الصف بأكمله لتحديد أو إلغاء تحديد اليوم
            $('#includedDaysList').on('click', 'tr', function(e) {
                // تجاهل النقر إذا كان مباشرة على مربع الاختيار
                if ($(e.target).is('input[type="checkbox"]')) {
                    return;
                }
                
                const $checkbox = $(this).find('input[type="checkbox"]');
                $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
            });
            
            // تعيين حالة الصفوف المحددة عند التحميل
            $('.day-selector:checked').closest('tr').addClass('selected');
            
            // Initialize the Apply button state
            const initialSelectedCount = $('.day-selector:checked').length;
            if (initialSelectedCount === 0) {
                $('#applyDefaultValues').prop('disabled', true);
            }
        }
        
        // دالة لتحديث عدد الأيام المحددة
        function updateSelectedDaysCount() {
            const selectedCount = $('.day-selector:checked').length;
            const totalDays = $('.day-selector').length;
            $('#selectedDaysCount').text(`${selectedCount} / ${totalDays} يوم محدد`);
            
            // تحديث حالة أزرار التحديد
            if (selectedCount === 0) {
                $('#applyDefaultValues').prop('disabled', true);
            } else {
                $('#applyDefaultValues').prop('disabled', false);
            }
        }
    </script>
</body>
</html>
