	Troubleshooting GNU FreeFont

So your text looks lousy, although you installed FreeFont and you seem to be
using it.  What do you do?

Before you blame the problem on FreeFont, take the time to double-check that
the text you are looking at is really rendered with FreeFont.

Be aware that not all Unicode characters are supported by FreeFont, and
even characters supported by one face, such as <PERSON><PERSON>, might not be
supported by other faces such as <PERSON><PERSON>.

Also, some systems have settings that strongly affect the rendering
of fonts.  It may be worth tweaking these.

glyph substitution
==================

When given the task of displaying characters in text, modern font rendering
software usually tries to display *something*, even if the font it is
*supposed* to be using does not contain glyphs for all the characters in the
text.  The software will snoop through all the fonts on the system to find
one that has a glyph for the one missing in the desired font.  So although
you have specified FreeSans-bold, you may be looking at a letter from quite
a different font.

First double-check that the font in question really contains the character
in question.  If you don't have font development software, this can be
tricky.  In the case of FreeFont, you can check if a given character 
range is supported: <http://www.gnu.org/software/freefont/coverage.html>

Next double-check that your application (web browser, text editor, etc)
has indeed been properly instructed to use the font.

Then double-check that the font is really installed in the system.
(This depends on the operating system, of course.)

Linux and Unix
==============

Modern Linux systems use a system called fontconfig, which maintains a font
cache, for efficiency.

The font cache can really complicate font installation and troubleshooting
however.  It can happen that when a font is newly installed, what is 
displayed is coming out of an old cache entry rather than the new font.

Just what to do depends on how and where the font was installed.

Fonts installed system-wide are usually put in a directory such as
	/usr/share/fonts/
the font cache for these might be in
	/var/cache/fontconfig/
Fonts installed just for one user account will typically be in
	~/.fonts/
and the cache will be
	~/.fontconfig/

You can clean your local cache merely by emptying the directory 
	~/.fontconfig/
In any case, to clean the cache, you can use the fontconfig command
	fc-cache -vf
If run as root, it will clean the system cache, if run as a normal user,
it cleans only the normal user's cache.

The procedure for local fonts is:
	1) shut off any program using the fonts in question
	2) clean the cache
	3) re-start the program
The procedure for system-wide fonts is:
	1) log out of the X Windows session
	2) in a console, clean the cache
	3) log in to an X Windows session

LibreOffice / OpenOffice
========================
These products have their own font rendering libraries, which have 
idiosyncratic behavior.

It has recently been reported that as of LibreOffice 3.5.1, font features
are disabled for OpenType fonts.  If you use FreeFont with these products,
you may want to install the TrueType versions of the fonts.

Windows
=======

The most common complaint has to do with "blurry text".  There are two
causes.

The first is that ClearType smoothing is turned off.  The best way to check
is to use the native Windows Web browser. Do a search for "ClearType Tuner".
The Microsoft pages install a tuner for ClearType. A security block notice
will appear at the top of the window--you have to allow the installation.
Then check the box "Turn on ClearType".  The change happens immediately.

The secont cause is that the FreeFont version with cubic spline outlines is
installed.  As of the 2012 GNU FreeFont release, the TrueType builds have
quadratic splines, which work best with Windows' rendering software.
	TTF (TrueType)  quadratic splines Windows 7, Vista, Windows XP.
	OTF (OpenType)  cubic splines     Linux, Mac

Note also: Firefox has a setting for ClearType:
	gfx.font_rendering.cleartype_params.rendering_mode
A value of 2 sets it to old-style GDI rendering, while -1 is the default.

reporting problems
==================

If you really think you're seeing a bug in FreeFont, or if you have
a suggestion, consider opening a problem report at
	https://savannah.gnu.org/bugs/?group=freefont
It is best that you make a Savannah account and log in with that, so 
you can be e-mailed whenever changes are made to your report.

$Id: troubleshooting.txt,v 1.10 2011-07-16 08:38:06 Stevan_White Exp $
