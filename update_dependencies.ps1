# Define the mapping of CDN URLs to local paths
$replacements = @{
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css' = '../assets/lib/bootstrap/bootstrap.min.css'
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js' = '../assets/lib/bootstrap/bootstrap.bundle.min.js'
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css' = '../assets/lib/bootstrap/bootstrap-icons.css'
    'https://code.jquery.com/jquery-3.7.1.min.js' = '../assets/lib/jquery/jquery.min.js'
    'https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css' = '../assets/lib/select2/select2.min.css'
    'https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js' = '../assets/lib/select2/select2.min.js'
    'https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css' = '../assets/lib/datatables/dataTables.bootstrap5.min.css'
    'https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js' = '../assets/lib/datatables/jquery.dataTables.min.js'
    'https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js' = '../assets/lib/datatables/dataTables.bootstrap5.min.js'
    'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js' = '../assets/lib/moment/moment.min.js'
    'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.js' = '../assets/lib/moment/moment-ar.js'
}

# Get all PHP files in the Pages directory and its subdirectories
$phpFiles = Get-ChildItem -Path "www\Pages" -Filter "*.php" -Recurse

foreach ($file in $phpFiles) {
    Write-Host "Processing $($file.FullName)"
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8

    # Replace each CDN URL with its local path
    foreach ($replacement in $replacements.GetEnumerator()) {
        if ($content -match [regex]::Escape($replacement.Key)) {
            Write-Host "  Replacing $($replacement.Key)"
            $content = $content.Replace($replacement.Key, $replacement.Value)
        }
    }

    # Save the file with UTF-8 encoding
    $content | Out-File -FilePath $file.FullName -Encoding UTF8
}

Write-Host "All files have been updated to use local dependencies!"