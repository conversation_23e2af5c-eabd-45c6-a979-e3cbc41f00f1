<?php
// Start output buffering
ob_start();

// Include the TCPDF library
require_once('../../TCPDF-6.6.2/tcpdf.php');

// Get parameters from the request
$data = json_decode($_POST['data'], true);

if (!$data) {
    die('No data provided');
}

// Fetch institution data
$institutionData = null;
try {
    // Connect to the database
    $file = fopen(__DIR__ . "/../../Pages/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch institution data
    $stmt = $db->query("SELECT * FROM documents LIMIT 1");
    $institutionData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Fetch task description from the Project_name column in the tasks table
    if (isset($data['id_TASKS'])) {
        // Changed query to fetch all columns from tasks to identify correct column name
        $stmt = $db->prepare("SELECT * FROM tasks WHERE id_TASKS = ?");
        $stmt->execute([$data['id_TASKS']]);
        $taskData = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($taskData) {
            // Check if 'description_TASKS' exists, which is more likely to be the task description column
            if (isset($taskData['description_TASKS'])) {
                $data['task_description'] = $taskData['description_TASKS'];
            } elseif (isset($taskData['note'])) {
                // Fallback to 'note' column if it exists
                $data['task_description'] = $taskData['note'];
            } else {
                // If neither exists, don't show the task description section
                $data['task_description'] = null;
            }
        }
    }
} catch (Exception $e) {
    die('Error fetching data: ' . $e->getMessage());
}

// Process institution data
if ($institutionData) {
    $agency_name_ar = explode("\n", trim($institutionData['name_ar']));
    $address_ar = explode("\n", trim($institutionData['address_ar']));
    $agency_name_en = explode("\n", trim($institutionData['name_en']));
    $address_en = explode("\n", trim($institutionData['address_en']));
    $numbers = explode("\n", trim($institutionData['numbers']));
    $logoData = $institutionData['logo'];
}

// Clean any output before generating PDF
ob_end_clean();

// Extend TCPDF class to customize header and footer
class MYPDF extends TCPDF {
    protected $institutionData;
    
    public function setInstitutionData($data) {
        $this->institutionData = $data;
    }

    public function Header() {
        if (!$this->institutionData) return;

        // Save current position
        $x = $this->GetX();
        $y = $this->GetY();

        // Calculate widths with equal margins
        $pageWidth = $this->getPageWidth();
        $margin = 2; // Equal margin on both sides
        $logoWidth = 35; // Increased from 25 to 35
        
        // New header layout - Logo at the top center
        if ($this->institutionData['logo']) {
            $this->Image('@'.$this->institutionData['logo'], 
                ($pageWidth - $logoWidth) / 2,
                $y + 2,
                $logoWidth, 
                $logoWidth,
                '', '', '', false, 300, '', false, false, 0);
        }
        
        // Add space after logo
        $y_after_logo = $y + $logoWidth + 5;
        
        // Draw purple lines with text - similar to offer.php style
        // Top purple lines (3 lines)
        $lineY = $y_after_logo;
        $lineHeight = 0.5;
        $lineGap = 1;
        $purpleColor = array(128, 0, 128); // Change back to purple RGB for lines only
        
        $this->SetLineWidth(0.2);
        $this->SetDrawColor($purpleColor[0], $purpleColor[1], $purpleColor[2]);
        
        // Draw top 3 purple lines
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Add text in the middle - only English text between lines
        $this->SetY($y_after_logo + 1.5);
        $this->SetFont('times', 'B', 10);
        $this->SetTextColor(0, 0, 0); // Keep text color black
        $this->Cell(0, 6, 'Assistance for Response and Development', 0, 1, 'L');
        $this->SetTextColor(0, 0, 0); // Reset text color to black for the rest of the document
        
        // Draw bottom 3 purple lines
        $lineY = $y_after_logo + 8;
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Set ending position
        $this->SetY($lineY + 5);
    }

    public function Footer() {
        $this->SetY(-25);
        $this->SetFont('aealarabiya', '', 9);
        $this->Cell(0, 10, 'توقيع الموظف                                  الموارد البشرية                                  رئيس المؤسسة', 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
}

// Create new PDF document
$pdf = new MYPDF('P', 'mm', 'A4', true, 'UTF-8', false);

// Set institution data
$pdf->setInstitutionData([
    'agency_name_ar' => $agency_name_ar,
    'address_ar' => $address_ar,
    'agency_name_en' => $agency_name_en,
    'address_en' => $address_en,
    'numbers' => $numbers,
    'logo' => $logoData
]);

// Set document information
$pdf->SetCreator('HR System');
$pdf->SetAuthor('HR System');
$pdf->SetTitle('تفاصيل المهمة');

// Set margins
$pdf->SetMargins(2, 55, 2);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 25);

// Add a page
$pdf->AddPage();

// Set font for header information
$pdf->SetFont('aealarabiya', 'B', 14);

// Add title
$pdf->Cell(0, 10, "تفاصيل المهمة", 0, 1, 'C');
$pdf->Ln(2);

// Calculate page width and adjust column widths
$pageWidth = 210 - 4; // Total page width minus total margins (2mm + 2mm)
$spacing = 2;
$leftStart = $pdf->GetMargins()['left'];

// Task Name Section with full width (first row)
$pdf->SetX($leftStart);

// Task name label and field with full width
$taskLabelWidth = 30; // Width for label
$taskNameWidth = $pageWidth - $taskLabelWidth; // Remaining width for content

// Store starting position for task name
$startX = $pdf->GetX();
$startY = $pdf->GetY();

// Set smaller font size for task name
$pdf->SetFont('aealarabiya', 'B', 9); // Reduced font size from default to 9

// Task name with MultiCell - allowing vertical expansion
$pdf->MultiCell($taskNameWidth, 6, $data['name_TASKS'], 1, 'R', false);

// Get ending Y position after task name content
$endY = $pdf->GetY();

// Add task label at the right height
$pdf->SetXY($startX + $taskNameWidth, $startY);
$pdf->Cell($taskLabelWidth, $endY - $startY, 'اسم المهمة', 1, 1, 'R', false);

// Add Project Name directly below the task name
// Get the project number if available
if (isset($data['id_Project'])) {
    // Fetch project name from database using the id_Project
    $projectName = '';
    try {
        $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $db->prepare("SELECT Project_name FROM project WHERE id_Project = ?");
        $stmt->execute([$data['id_Project']]);
        $projectResult = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($projectResult && isset($projectResult['Project_name'])) {
            $projectName = $projectResult['Project_name'];
        } else {
            $projectName = 'غير متوفر';
        }
    } catch (Exception $e) {
        $projectName = 'غير متوفر';
    }
    
    // Store starting position for project name
    $startX = $pdf->GetX();
    $startY = $pdf->GetY();
    
    // Keep same smaller font size for project name (already set to 9)
    
    // Project name with MultiCell - allowing vertical expansion
    $pdf->MultiCell($taskNameWidth, 6, $projectName, 1, 'R', false);
    
    // Get ending Y position after project name content
    $endY = $pdf->GetY();
    
    // Add project label at the right height
    $pdf->SetXY($startX + $taskNameWidth, $startY);
    $pdf->Cell($taskLabelWidth, $endY - $startY, 'اسم المشروع', 1, 1, 'R', false);
}

// Task Description Section if available
if (!empty($data['task_description'])) {
    // Reset font to normal size for description
    $pdf->SetFont('aealarabiya', '', 10);
    
    $startX = $pdf->GetX();
    $startY = $pdf->GetY();
    
    // Task description with MultiCell - allowing vertical expansion
    $pdf->MultiCell($taskNameWidth, 6, $data['task_description'], 1, 'R', false);
    
    // Get ending Y position after task description content
    $endY = $pdf->GetY();
    
    // Add task description label at the right height
    $pdf->SetXY($startX + $taskNameWidth, $startY);
    $pdf->Cell($taskLabelWidth, $endY - $startY, 'وصف المهمة', 1, 1, 'R', false);
}

// Add spacing after the main task details
$pdf->Ln(5);

// Set up main info section with enhanced styling
$pdf->SetFont('aealarabiya', 'B', 14);
$pdf->SetTextColor(0, 0, 0);
$pdf->Cell(0, 10, "معلومات المهمة", 0, 1, 'C');
$pdf->SetTextColor(0, 0, 0);
$pdf->Ln(2);

// Define colors for different sections
$headerFillColor = array(240, 240, 240); // Light gray for headers
$borderColor = array(0, 0, 0);           // Black border

// Set border color to black
$pdf->SetDrawColor($borderColor[0], $borderColor[1], $borderColor[2]);
$pdf->SetLineWidth(0.3); // Slightly thicker borders

// Removing redundant headers and keeping only one "Task Information" header
// Set font for content
$pdf->SetFont('aealarabiya', 'B', 10);

// Calculate column widths
$leftSectionWidth = $pageWidth / 2 - $spacing / 2;
$rightSectionWidth = $pageWidth / 2 - $spacing / 2;
$labelWidth = 40;
$valueWidth = $leftSectionWidth - $labelWidth;

// Executor information with improved styling
$colWidth1 = ($pageWidth) / 2;
$leftSectionX = $leftStart;

// Executor information
$pdf->SetX($leftSectionX);
$executorType = $data['executor_type'] == 'employee' ? 'موظف' : 'مندوب';
$executorName = $data['executor_name_ar'];
$pdf->Cell($colWidth1, 8, 'منفذ المهمة: ' . $executorName . ' (' . $executorType . ')', 1, 0, 'R', false);

// Task ID
$pdf->Cell($colWidth1, 8, 'رقم المهمة: ' . $data['id_TASKS'], 1, 1, 'R', false);

// Dates
$startDate = date('Y/m/d', strtotime($data['start_date_TASKS']));
$endDate = date('Y/m/d', strtotime($data['end_date_TASKS']));
$pdf->SetX($leftSectionX);
$pdf->Cell($colWidth1, 8, 'تاريخ البدء: ' . $startDate, 1, 0, 'R', false);
$pdf->Cell($colWidth1, 8, 'تاريخ الانتهاء: ' . $endDate, 1, 1, 'R', false);

// Payment and Project Information
$pdf->SetX($leftSectionX);
$pdf->Cell($colWidth1, 8, 'المبلغ كتابةً: ' . $data['amount_written_ar'], 1, 0, 'R', false);
$pdf->Cell($colWidth1, 8, 'رقم المشروع: ' . $data['id_Project'], 1, 1, 'R', false);

// Workplace
$pdf->SetX($leftSectionX);
$pdf->Cell(0, 8, 'مكان العمل: ' . $data['workplace'], 1, 1, 'R', false);

// Notes if available
if (!empty($data['note'])) {
    $pdf->SetX($leftSectionX);
    $startY = $pdf->GetY();
    $pdf->MultiCell(0, 8, 'ملاحظات: ' . $data['note'], 1, 'R', false);
}

// Reset border color and line width to default
$pdf->SetDrawColor(0, 0, 0);
$pdf->SetLineWidth(0.1);

$pdf->Ln(5);

// Add Task List Section if available
if (isset($data['data_todo_list_TASKS']) && isset($data['data_todo_list_TASKS']['taskDetails']) && isset($data['data_todo_list_TASKS']['taskDetails']['subtasks'])) {
    $pdf->SetFont('aealarabiya', 'B', 12);
    $pdf->Cell(0, 8, "قائمة المهام التفصيلية", 0, 1, 'C');
    $pdf->Ln(2);

    // Tasks table headers
    $pdf->SetFont('aealarabiya', 'B', 10);
    $pdf->SetFillColor(220, 220, 220);
    
    // Column widths
    $taskWidth = $pageWidth - 15;
    $numWidth = 15;
    
    // Headers - switched order so task number (ID) appears first in RTL
    $pdf->Cell($taskWidth, 7, 'المهمة', 1, 0, 'C', true);
    $pdf->Cell($numWidth, 7, 'الرقم', 1, 1, 'C', true);

    // Tasks data
    $pdf->SetFont('aealarabiya', '', 10);
    $fill = false;
    
    $subtasks = $data['data_todo_list_TASKS']['taskDetails']['subtasks'];
    foreach ($subtasks as $index => $subtask) {
        // Task name with potential line wrapping
        $taskName = $subtask['subtaskName'];
        
        // For long text, use a two-step approach with startTransaction
        $longText = (strlen($taskName) > 40);
        
        if ($longText) {
            // Start a transaction to measure cell height
            $pdf->startTransaction();
            $start_y = $pdf->GetY();
            $start_page = $pdf->getPage();
            
            // Write task name to calculate height
            $pdf->MultiCell($taskWidth, 0, $taskName, 1, 'R', $fill, 1);
            
            // Get end position
            $end_y = $pdf->GetY();
            $end_page = $pdf->getPage();
            
            // Calculate height
            if ($end_page == $start_page) {
                $height = $end_y - $start_y;
            } else {
                $height = 15; // Default for multi-page cell
            }
            
            // Rollback to start position
            $pdf->rollbackTransaction(true);
            
            // Set minimum height
            $height = max($height, 7);
            
            // Draw cells with calculated height - switched order
            $startX = $pdf->GetX();
            $startY = $pdf->GetY();
            
            // Task name first (switched order)
            $pdf->SetXY($startX, $startY);
            $pdf->MultiCell($taskWidth, $height, $taskName, 1, 'R', $fill, 0);
            
            // Number cell (switched order)
            $pdf->SetXY($startX + $taskWidth, $startY);
            $pdf->MultiCell($numWidth, $height, $index + 1, 1, 'C', $fill, 1);
        } else {
            // For short text, use a standard approach - switched order
            $pdf->Cell($taskWidth, 7, $taskName, 1, 0, 'R', $fill);
            $pdf->Cell($numWidth, 7, $index + 1, 1, 1, 'C', $fill);
        }
        
        $fill = !$fill;
    }
}

// Output the PDF
$pdf->Output('task_details.pdf', 'I'); 