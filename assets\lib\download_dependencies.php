<?php
// List of URLs to download from
$dependencies = [
    // Bootstrap
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' => 'lib/bootstrap/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js' => 'lib/bootstrap/bootstrap.bundle.min.js',
    
    // Bootstrap Icons
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css' => 'lib/bootstrap/bootstrap-icons.css',
    
    // jQuery
    'https://code.jquery.com/jquery-3.6.0.min.js' => 'lib/jquery/jquery.min.js',
    
    // Select2
    'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css' => 'lib/select2/select2.min.css',
    'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js' => 'lib/select2/select2.min.js',
    
    // DataTables
    'https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css' => 'lib/datatables/dataTables.bootstrap5.min.css',
    'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js' => 'lib/datatables/jquery.dataTables.min.js',
    'https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js' => 'lib/datatables/dataTables.bootstrap5.min.js',
    
    // Cairo Font Files
    'https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2' => 'fonts/cairo/cairo-regular.woff2',
    'https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcSCyS4J0.woff2' => 'fonts/cairo/cairo-bold.woff2',
];

// Create directories if they don't exist
foreach ($dependencies as $url => $path) {
    $dir = dirname(__DIR__ . '/' . $path);
    if (!is_dir($dir)) {
        mkdir($dir, 0777, true);
    }
}

// Download files
foreach ($dependencies as $url => $path) {
    $content = file_get_contents($url);
    if ($content === false) {
        echo "Failed to download: $url\n";
        continue;
    }
    
    $fullPath = __DIR__ . '/../' . $path;
    if (file_put_contents($fullPath, $content) === false) {
        echo "Failed to save: $path\n";
        continue;
    }
    
    echo "Successfully downloaded: $path\n";
} 