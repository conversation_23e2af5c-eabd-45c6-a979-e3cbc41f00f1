/* Cairo Font with System Font Fallbacks */
@font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 400;
    src: local('Cairo Regular'), local('Cairo-Regular'),
         url('../fonts/cairo-regular.woff2') format('woff2'),
         url('../fonts/cairo-regular.woff') format('woff');
}

@font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 600;
    src: local('Cairo SemiBold'), local('Cairo-SemiBold'),
         url('../fonts/cairo-semibold.woff2') format('woff2'),
         url('../fonts/cairo-semibold.woff') format('woff');
}

@font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 700;
    src: local('Cairo Bold'), local('Cairo-Bold'),
         url('../fonts/cairo-bold.woff2') format('woff2'),
         url('../fonts/cairo-bold.woff') format('woff');
}

/* Fallback font stack */
body {
    font-family: 'Cairo', 'Segoe UI', 'Arial', '<PERSON>homa', sans-serif;
} 