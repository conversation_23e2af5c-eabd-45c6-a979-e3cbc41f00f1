<?php
// Start output buffering
ob_start();

// Include the TCPDF library
require_once('../../TCPDF-6.6.2/tcpdf.php');

// Get parameters from the request
$data = json_decode($_POST['data'], true);

if (!$data) {
    die('No data provided');
}

// Fetch institution data
$institutionData = null;
try {
    // Connect to the database
    $file = fopen(__DIR__ . "/../../Pages/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch institution data
    $stmt = $db->query("SELECT * FROM documents LIMIT 1");
    $institutionData = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die('Error fetching institution data: ' . $e->getMessage());
}

// Process institution data
if ($institutionData) {
    $agency_name_ar = explode("\n", trim($institutionData['name_ar']));
    $address_ar = explode("\n", trim($institutionData['address_ar']));
    $agency_name_en = explode("\n", trim($institutionData['name_en']));
    $address_en = explode("\n", trim($institutionData['address_en']));
    $numbers = explode("\n", trim($institutionData['numbers']));
    $logoData = $institutionData['logo'];
}

// Clean any output before generating PDF
ob_end_clean();

// Extend TCPDF class to customize header and footer
class MYPDF extends TCPDF {
    protected $institutionData;
    
    public function setInstitutionData($data) {
        $this->institutionData = $data;
    }

    public function Header() {
        if (!$this->institutionData) return;

        // Save current position
        $x = $this->GetX();
        $y = $this->GetY();

        // Calculate widths with equal margins
        $pageWidth = $this->getPageWidth();
        $margin = 2; // Equal margin on both sides
        $logoWidth = 35; // Increased from 25 to 35
        
        // New header layout - Logo at the top center
        if ($this->institutionData['logo']) {
            $this->Image('@'.$this->institutionData['logo'], 
                ($pageWidth - $logoWidth) / 2,
                $y + 2,
                $logoWidth, 
                $logoWidth,
                '', '', '', false, 300, '', false, false, 0);
        }
        
        // Add space after logo
        $y_after_logo = $y + $logoWidth + 5;
        
        // Draw purple lines with text - similar to offer.php style
        // Top purple lines (3 lines)
        $lineY = $y_after_logo;
        $lineHeight = 0.5;
        $lineGap = 1;
        $purpleColor = array(128, 0, 128); // Purple RGB
        
        $this->SetLineWidth(0.2);
        $this->SetDrawColor($purpleColor[0], $purpleColor[1], $purpleColor[2]);
        
        // Draw top 3 purple lines
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Add text in the middle - only English text between lines
        $this->SetY($y_after_logo + 1.5);
        $this->SetFont('times', 'B', 10);
        $this->SetTextColor($purpleColor[0], $purpleColor[1], $purpleColor[2]); // Set text color to purple
        $this->Cell(0, 6, 'Assistance for Response and Development', 0, 1, 'L'); // Changed from 'C' to 'L' for left alignment
        $this->SetTextColor(0, 0, 0); // Reset text color to black for the rest of the document
        
        // Draw bottom 3 purple lines
        $lineY = $y_after_logo + 8;
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Set ending position
        $this->SetY($lineY + 5);
    }

    public function Footer() {
        $this->SetY(-25);
        $this->SetFont('aealarabiya', '', 9);
        $this->Cell(0, 10, 'توقيع الموظف                                  الموارد البشرية                                  رئيس المؤسسة', 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
}

// Create new PDF document
$pdf = new MYPDF('P', 'mm', 'A4', true, 'UTF-8', false);

// Set institution data
$pdf->setInstitutionData([
    'agency_name_ar' => $agency_name_ar,
    'address_ar' => $address_ar,
    'agency_name_en' => $agency_name_en,
    'address_en' => $address_en,
    'numbers' => $numbers,
    'logo' => $logoData
]);

// Set document information
$pdf->SetCreator('HR System');
$pdf->SetAuthor('HR System');
$pdf->SetTitle('Achievement Report');

// Set margins
$pdf->SetMargins(2, 55, 2);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 25);

// Add a page
$pdf->AddPage();

// Set font for header information
$pdf->SetFont('aealarabiya', 'B', 10);

// Calculate page width and adjust column widths
$pageWidth = 210 - 4; // Total page width minus total margins (2mm + 2mm)
$labelWidth = 22;
$valueWidth = 98; // Increased to use the extra space
$smallValueWidth = 55;
$spacing = 2;

// Set fill color for labels
$pdf->SetFillColor(220, 220, 220);

// Store starting Y position
$startY = $pdf->GetY();

// Left side of first row (Project ID)
$leftStart = $pdf->GetMargins()['left'];
$pdf->SetX($leftStart);
$pdf->Cell($smallValueWidth, 6, $data['id_Project'], 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'رقم المشروع', 1, 0, 'R', false);
$pdf->Cell($spacing, 6, '', 0, 0, 'C', false);

// Right side - Project name with MultiCell
$rightX = $pdf->GetX();
$rightY = $pdf->GetY();
$pdf->MultiCell($valueWidth, 6, $data['Project_name'], 1, 'R', false);

// Get ending Y position after project name
$endY = $pdf->GetY();

// Add project label at the right height
$pdf->SetXY($rightX + $valueWidth, $rightY);
$pdf->Cell($labelWidth, $endY - $rightY, 'اسم المشروع', 1, 1, 'R', false);

// Ensure we start the second row at the correct position
$pdf->SetY($endY);

// Add minimal spacing between rows
$pdf->Ln(0.5);

// Second row (aligned with the first row)
$pdf->SetX($leftStart);
$pdf->Cell($smallValueWidth, 6, $data['name_ar_contract'], 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'اسم الموظف', 1, 0, 'R', false);
$pdf->Cell($spacing, 6, '', 0, 0, 'C', false);
$pdf->Cell($valueWidth, 6, $data['name_Job'], 1, 0, 'R', false);
$pdf->Cell($labelWidth, 6, 'المسمى الوظيفي', 1, 1, 'R', false);

// Add minimal spacing after the information section
$pdf->Ln(5);

// Add title with date range
$startDate = date('Y/m/d', strtotime($data['start_date_achievement_reports']));
$endDate = date('Y/m/d', strtotime($data['end_date_achievement_reports']));

// Add period details in a styled box
$pdf->SetFillColor(245, 245, 245); // Light gray background
$pdf->Cell(0, 8, "الفترة: $startDate - $endDate", 1, 1, 'C', true);

$pdf->Ln(15); // Changed from 10 to 15 for more space before title

// Add the Achievement Report title with larger font
$pdf->SetFont('aealarabiya', 'B', 14); // Changed from 11 to 14 for larger text
$pdf->Cell(0, 8, "تقرير الإنجاز للفترة المحددة", 0, 1, 'C'); // Changed height from 6 to 8

$pdf->Ln(8); // Changed from 5 to 8 for more space before table

// Achievement Time Report Table
if (isset($data['data_todo_list_achievement']) && 
    isset($data['data_todo_list_achievement']['jobDetails']) && 
    isset($data['data_todo_list_achievement']['jobDetails']['tasks'])) {

    $isPercentageEvaluation = $data['data_todo_list_achievement']['evaluation']['percentageEvaluation'] === "yes";
    
    // Table headers
    $pdf->SetFont('aealarabiya', 'B', 9);
    $pdf->SetFillColor(220, 220, 220);
    
    // Column widths - Adjusted to balance between Task Name and Progress columns
    $w = array(25, 25, 25, 25, 104); // Adjusted column widths for better balance
    
    // Headers - Reverse the order
    $pdf->Cell($w[0], 7, 'ملاحظات', 1, 0, 'C', true);
    if ($isPercentageEvaluation) {
        $pdf->Cell($w[1], 7, 'إجمالي نسبة الإنجاز', 1, 0, 'C', true);
        $pdf->Cell($w[2], 7, 'نسبة الإنجاز للفترة', 1, 0, 'C', true);
        $pdf->Cell($w[3], 7, 'نسبة الإنجاز حتى الآن', 1, 0, 'C', true);
    } else {
        $pdf->Cell($w[1], 7, 'إجمالي أيام العمل', 1, 0, 'C', true);
        $pdf->Cell($w[2], 7, 'أيام العمل للفترة', 1, 0, 'C', true);
        $pdf->Cell($w[3], 7, 'أيام العمل المنجزة', 1, 0, 'C', true);
    }
    $pdf->Cell($w[4], 7, 'اسم المهمة', 1, 1, 'C', true);

    // Prepare for table content
    $pdf->SetFont('aealarabiya', '', 9);
    $fill = false;
    
    foreach ($data['data_todo_list_achievement']['jobDetails']['tasks'] as $task) {
        // Remove dash prefix from task name if it exists
        $cleanTaskName = preg_replace('/^[\s\-–—]+/', '', $task['taskName']);
        $notesText = $task['notes'] ?: '-';
        
        // Check if text is long - if so, use a two-step approach with startTransaction
        $longText = (strlen($cleanTaskName) > 40 || strlen($notesText) > 20);
        
        if ($longText) {
            // Start a transaction to measure cell height
            $pdf->startTransaction();
            $start_y = $pdf->GetY();
            $start_page = $pdf->getPage();
            
            // Write task name to calculate height
            $pdf->MultiCell($w[4], 0, $cleanTaskName, 1, 'R', $fill, 1);
            
            // Get end position
            $end_y = $pdf->GetY();
            $end_page = $pdf->getPage();
            
            // Calculate height
            if ($end_page == $start_page) {
                $height = $end_y - $start_y;
            } else {
                $height = 15; // Default for multi-page cell
            }
            
            // Rollback to start position
            $pdf->rollbackTransaction(true);
            
            // Set minimum height
            $height = max($height, 7);
            
            // Draw cells with calculated height
            $startX = $pdf->GetX();
            $startY = $pdf->GetY();
            
            // Notes column
            $pdf->SetXY($startX, $startY);
            $pdf->MultiCell($w[0], $height, $notesText, 1, 'R', $fill, 0);
            
            // Values columns
            if ($isPercentageEvaluation) {
                $pdf->SetXY($startX + $w[0], $startY);
                $pdf->MultiCell($w[1], $height, $task['total'] . '%', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1], $startY);
                $pdf->MultiCell($w[2], $height, $task['completionRate'] . '%', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1] + $w[2], $startY);
                $pdf->MultiCell($w[3], $height, $task['total'] . '%', 1, 'C', $fill, 0);
            } else {
                $pdf->SetXY($startX + $w[0], $startY);
                $pdf->MultiCell($w[1], $height, $task['total'] . ' يوم', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1], $startY);
                $pdf->MultiCell($w[2], $height, $task['completionRate'] . ' يوم', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1] + $w[2], $startY);
                $pdf->MultiCell($w[3], $height, $task['total'] . ' يوم', 1, 'C', $fill, 0);
            }
            
            // Task name
            $pdf->SetXY($startX + $w[0] + $w[1] + $w[2] + $w[3], $startY);
            $pdf->MultiCell($w[4], $height, $cleanTaskName, 1, 'R', $fill, 1);
        } else {
            // For short text, use a standard height
            $height = 7;
            
            // Start position
            $startX = $pdf->GetX();
            $startY = $pdf->GetY();
            
            // Notes column
            $pdf->SetXY($startX, $startY);
            $pdf->MultiCell($w[0], $height, $notesText, 1, 'R', $fill, 0);
            
            // Values columns
            if ($isPercentageEvaluation) {
                $pdf->SetXY($startX + $w[0], $startY);
                $pdf->MultiCell($w[1], $height, $task['total'] . '%', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1], $startY);
                $pdf->MultiCell($w[2], $height, $task['completionRate'] . '%', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1] + $w[2], $startY);
                $pdf->MultiCell($w[3], $height, $task['total'] . '%', 1, 'C', $fill, 0);
            } else {
                $pdf->SetXY($startX + $w[0], $startY);
                $pdf->MultiCell($w[1], $height, $task['total'] . ' يوم', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1], $startY);
                $pdf->MultiCell($w[2], $height, $task['completionRate'] . ' يوم', 1, 'C', $fill, 0);
                
                $pdf->SetXY($startX + $w[0] + $w[1] + $w[2], $startY);
                $pdf->MultiCell($w[3], $height, $task['total'] . ' يوم', 1, 'C', $fill, 0);
            }
            
            // Task name
            $pdf->SetXY($startX + $w[0] + $w[1] + $w[2] + $w[3], $startY);
            $pdf->MultiCell($w[4], $height, $cleanTaskName, 1, 'R', $fill, 1);
        }
        
        $fill = !$fill;
    }
}

// Output the PDF
$pdf->Output('achievement_report.pdf', 'I'); 