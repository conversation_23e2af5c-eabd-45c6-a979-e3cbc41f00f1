<?php
// Turn off error output to prevent HTML in JSON response
error_reporting(0);
ini_set('display_errors', 0);
session_start();

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if request is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    // Check if all required parameters are provided
    if (!isset($_POST['delegate_id']) || !isset($_POST['document_type']) || !isset($_FILES['document_file'])) {
        throw new Exception('Missing required parameters');
    }
    
    $delegate_id = (int)$_POST['delegate_id'];
    $document_type = $_POST['document_type'];
    $document_file = $_FILES['document_file'];
    
    // Validate delegate_id
    if ($delegate_id <= 0) {
        throw new Exception('Invalid delegate ID: ' . $_POST['delegate_id']);
    }
    
    // Validate document type
    $allowed_document_types = [
        'Identity_document',
        'code_conduct'
    ];
    
    if (!in_array($document_type, $allowed_document_types)) {
        throw new Exception('Invalid document type');
    }
    
    // Validate file
    if ($document_file['error'] !== UPLOAD_ERR_OK) {
        $upload_errors = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive in php.ini',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive in the HTML form',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
        ];
        $error_message = isset($upload_errors[$document_file['error']]) 
            ? $upload_errors[$document_file['error']] 
            : 'Unknown upload error';
        throw new Exception('File upload error: ' . $error_message);
    }
    
    // Check file size (limit to 5MB)
    if ($document_file['size'] > 5 * 1024 * 1024) {
        throw new Exception('File size exceeds the limit (5MB)');
    }
    
    // Check file type (allow PDF only)
    if (!function_exists('finfo_open')) {
        // Fallback if fileinfo extension is not available
        $file_extension = strtolower(pathinfo($document_file['name'], PATHINFO_EXTENSION));
        if ($file_extension !== 'pdf') {
            throw new Exception('Only PDF files are allowed');
        }
    } else {
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mime_type = $finfo->file($document_file['tmp_name']);
        
        if ($mime_type !== 'application/pdf') {
            throw new Exception('Only PDF files are allowed. Detected MIME type: ' . $mime_type);
        }
    }
    
    // Read file content
    $document_data = file_get_contents($document_file['tmp_name']);
    
    if ($document_data === false) {
        throw new Exception('Failed to read file content');
    }
    
    // Read database connection details
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);
    
    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");
    
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    
    // First, check if the delegate exists
    $check_sql = "SELECT COUNT(*) as count FROM assigned WHERE id_assigned = ?";
    $check_stmt = $conn->prepare($check_sql);
    
    if (!$check_stmt) {
        throw new Exception("Check query preparation failed: " . $conn->error);
    }
    
    $check_stmt->bind_param("i", $delegate_id);
    
    if (!$check_stmt->execute()) {
        throw new Exception("Check query execution failed: " . $check_stmt->error);
    }
    
    $check_result = $check_stmt->get_result();
    $count_row = $check_result->fetch_assoc();
    $check_stmt->close();
    
    if (!$count_row || $count_row['count'] == 0) {
        throw new Exception("Delegate with ID {$delegate_id} does not exist in the database");
    }
    
    // Update the database
    $sql = "UPDATE assigned SET {$document_type} = ? WHERE id_assigned = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Query preparation failed: " . $conn->error);
    }
    
    $null = NULL;
    $stmt->bind_param("bi", $null, $delegate_id);
    $stmt->send_long_data(0, $document_data);
    
    if (!$stmt->execute()) {
        throw new Exception("Query execution failed: " . $stmt->error);
    }
    
    $affected_rows = $stmt->affected_rows;
    
    if ($affected_rows === 0) {
        throw new Exception("No record was updated. Delegate ID {$delegate_id} exists but update failed.");
    }
    
    $stmt->close();
    $conn->close();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Document uploaded successfully',
        'delegate_id' => $delegate_id
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'delegate_id' => isset($delegate_id) ? $delegate_id : null,
        'post_data' => $_POST
    ]);
} catch (Error $e) {
    // Catch PHP 7+ errors
    echo json_encode([
        'success' => false,
        'message' => 'System error: ' . $e->getMessage(),
        'delegate_id' => isset($delegate_id) ? $delegate_id : null
    ]);
} 