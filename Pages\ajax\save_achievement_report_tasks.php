<?php
session_start();
header('Content-Type: application/json');

// Error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$response = ['success' => false, 'message' => ''];

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get database connection
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) throw new Exception('Error reading configuration file');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Validate required fields
    $requiredFields = ['id_Project', 'id_TASKS', 'start_date_achievement_reports', 
                      'end_date_achievement_reports', 'data_todo_list_achievement'];
    
    foreach ($requiredFields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }

    // Prepare the SQL statement
    $sql = "INSERT INTO achievement_reports_tasks (
                id_Project, 
                id_TASKS, 
                id_assigned,
                id_employees, 
                start_date_achievement_reports, 
                end_date_achievement_reports, 
                data_todo_list_achievement,
                add_achievement_reports
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    // Handle NULL values for id_assigned and id_employees
    $id_assigned = !empty($_POST['id_assigned']) ? $_POST['id_assigned'] : null;
    $id_employees = !empty($_POST['id_employees']) ? $_POST['id_employees'] : null;

    // Bind parameters
    $stmt->bind_param(
        "iiissss",
        $_POST['id_Project'],
        $_POST['id_TASKS'],
        $id_assigned,
        $id_employees,
        $_POST['start_date_achievement_reports'],
        $_POST['end_date_achievement_reports'],
        $_POST['data_todo_list_achievement']
    );

    // Execute the statement
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    $response['success'] = true;
    $response['message'] = 'Task achievement report saved successfully';

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
} finally {
    if (isset($stmt)) $stmt->close();
    if (isset($conn)) $conn->close();
}

echo json_encode($response); 