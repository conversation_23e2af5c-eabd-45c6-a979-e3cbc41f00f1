<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $table = $_POST['table'] ?? '';
    $action = $_POST['action'] ?? '';
    $id = $_POST['id'] ?? '';
    
    try {
        if ($action === 'add' || $action === 'edit') {
            $name_ar = $_POST['name_ar'] ?? '';
            $name_en = $_POST['name_en'] ?? '';
            $role = $_POST['role'] ?? '';
            
            if ($action === 'add') {
                switch ($table) {
                    case 'administrators':
                        $stmt = $conn->prepare("INSERT INTO administrators (full_name_ar, full_name_en, role, role_en) VALUES (?, ?, ?, ?)");
                        $stmt->bind_param("ssss", $name_ar, $name_en, $role, $_POST['role_en']);
                        break;
                    default:
                        $stmt = $conn->prepare("INSERT INTO $table (name_ar, name_en) VALUES (?, ?)");
                        $stmt->bind_param("ss", $name_ar, $name_en);
                }
            } else {
                switch ($table) {
                    case 'administrators':
                        $stmt = $conn->prepare("UPDATE administrators SET full_name_ar = ?, full_name_en = ?, role = ?, role_en = ? WHERE id_administrators = ?");
                        $stmt->bind_param("ssssi", $name_ar, $name_en, $role, $_POST['role_en'], $id);
                        break;
                    default:
                        $stmt = $conn->prepare("UPDATE $table SET name_ar = ?, name_en = ? WHERE id_$table = ?");
                        $stmt->bind_param("ssi", $name_ar, $name_en, $id);
                }
            }
            
            if (!$stmt->execute()) {
                throw new Exception($stmt->error);
            }
            $success_message = $action === 'add' ? 'تمت الإضافة بنجاح' : 'تم التحديث بنجاح';
            $stmt->close();
        }
        
        elseif ($action === 'toggle_status') {
            $id_column = "id_" . $table;
            $current_status = $_POST['current_status'] ?? 1;
            $new_status = $current_status == 1 ? 2 : 1;
            
            $stmt = $conn->prepare("UPDATE $table SET status = ? WHERE $id_column = ?");
            $stmt->bind_param("ii", $new_status, $id);
            
            if (!$stmt->execute()) {
                throw new Exception($stmt->error);
            }
            $success_message = $new_status == 1 ? 'تم تنشيط السجل بنجاح' : 'تم تعليق السجل بنجاح';
            $stmt->close();
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Fetch records for the selected table
$selected_table = $_GET['table'] ?? 'administrators';
$valid_tables = ['administrators', 'implementing_organizations', 'target_areas', 'funders', 'contracts'];

if (!in_array($selected_table, $valid_tables)) {
    $selected_table = 'administrators';
}

// Fetch projects for contract filter
$projects = [];
if ($selected_table === 'contracts') {
    $query = "SELECT id_Project, Project_name FROM Project WHERE Project_status = 1";
    $result = $conn->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }
}

// Handle AJAX request for contracts
if (isset($_GET['action']) && $_GET['action'] === 'toggle_contract_status') {
    header('Content-Type: application/json');
    try {
        $contract_id = isset($_POST['contract_id']) ? intval($_POST['contract_id']) : 0;
        $current_status = isset($_POST['current_status']) ? intval($_POST['current_status']) : 0;
        $new_status = $current_status == 1 ? 0 : 1;
        
        $stmt = $conn->prepare("UPDATE contract SET status_contract = ? WHERE id_contract = ?");
        $stmt->bind_param("ii", $new_status, $contract_id);
        
        if (!$stmt->execute()) {
            throw new Exception($stmt->error);
        }
        
        echo json_encode(['success' => true, 'new_status' => $new_status]);
        exit;
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

if (isset($_GET['action']) && $_GET['action'] === 'get_contracts') {
    header('Content-Type: application/json');
    try {
        // تحديد المعايير للبحث والتصفية
        $search = isset($_GET['search']) ? $_GET['search'] : '';
        $projectFilter = isset($_GET['project_id']) ? intval($_GET['project_id']) : 0;
        
        // بناء استعلام SQL مع معايير البحث والتصفية
                        $sql = "SELECT 
                c.id_contract, 
                e.name_ar_contract, 
                p.Project_name,
                c.id_Project,
                c.contract_type,
                c.end_date_contract,
                CASE 
                    WHEN c.contract_type = 1 AND c.end_date_contract IS NULL THEN 'شهري مفتوح'
                    WHEN c.contract_type = 1 AND c.end_date_contract IS NOT NULL THEN 'شهري مغلق'
                    WHEN c.contract_type = 2 AND c.end_date_contract IS NULL THEN 'يومي مفتوح'
                    WHEN c.contract_type = 2 AND c.end_date_contract IS NOT NULL THEN 'يومي مغلق'
                    ELSE 'غير محدد'
                END as contract_type_name,
                c.add_contract,
                c.status_contract
            FROM contract c
            LEFT JOIN employees e ON c.id_employees = e.id_employees
            LEFT JOIN project p ON c.id_Project = p.id_Project
            WHERE 1=1";
        
        // إضافة شرط البحث
        if (!empty($search)) {
            $search = $conn->real_escape_string($search);
            $sql .= " AND (e.name_ar_contract LIKE '%$search%' OR 
                         c.id_contract LIKE '%$search%' OR
                         p.Project_name LIKE '%$search%')";
        }
        
        // إضافة شرط تصفية المشروع
        if ($projectFilter > 0) {
            $sql .= " AND c.id_Project = $projectFilter";
        }
        
        // ترتيب النتائج
        $sql .= " ORDER BY c.id_contract DESC";
        
        $result = $conn->query($sql);
        if (!$result) {
            throw new Exception("Query execution failed: " . $conn->error);
        }
        
        $contracts = [];
        while ($row = $result->fetch_assoc()) {
            // تنسيق التاريخ
            $date = new DateTime($row['add_contract']);
            $row['add_contract'] = $date->format('Y-m-d');
            
            $contracts[] = $row;
        }
        
        echo json_encode(['data' => $contracts]);
        exit;
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

if (isset($_GET['action']) && $_GET['action'] === 'toggle_contract_status') {
    header('Content-Type: application/json');
    try {
        $contract_id = isset($_POST['contract_id']) ? intval($_POST['contract_id']) : 0;
        $current_status = isset($_POST['current_status']) ? intval($_POST['current_status']) : 0;
        $new_status = $current_status == 1 ? 0 : 1;
        
        $stmt = $conn->prepare("UPDATE contract SET status_contract = ? WHERE id_contract = ?");
        $stmt->bind_param("ii", $new_status, $contract_id);
        
        if (!$stmt->execute()) {
            throw new Exception($stmt->error);
        }
        
        echo json_encode(['success' => true, 'new_status' => $new_status]);
        exit;
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

try {
    if ($selected_table !== 'contracts') {
        $query = "SELECT *, CASE WHEN status = 1 THEN 'نشط' ELSE 'معلق' END as status_text FROM $selected_table";
        $result = $conn->query($query);
        if (!$result) {
            throw new Exception($conn->error);
        }
    }
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العداد الأساسي - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/lib/datatables/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        :root {
            --font-family: 'Cairo', sans-serif;
        }
        
        body {
            font-family: var(--font-family);
        }
        
        .table-container {
            background-color: var(--bg-card);
            border-radius: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            margin-top: 1rem;
            padding: 1.5rem;
        }
        
        /* Table Styles */
        .table {
            margin-bottom: 0;
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table th,
        .table td {
            border-bottom: 1px solid var(--border-color);
        }
        
        .table th {
            background-color: var(--bg-main);
            color: var(--text-color);
            font-weight: 600;
            padding: 1rem;
        }
        
        .table td {
            background-color: var(--bg-card);
            color: var(--text-color);
            padding: 0.75rem 1rem;
            vertical-align: middle;
        }
        
        .table tbody tr:hover td {
            background-color: var(--hover-color);
        }
        
        /* DataTables Controls Container */
        .dataTables_wrapper .row {
            margin: 0;
            align-items: center;
        }
        
        .dataTables_wrapper .row:not(:last-child) {
            margin-bottom: 1rem;
        }
        
        /* Search Control */
        .dataTables_filter {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }
        
        .dataTables_filter label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            color: var(--text-color);
            font-weight: 500;
            width: 100%;
        }
        
        .dataTables_filter input {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 8px;
            padding: 0.6rem 1rem;
            font-size: 0.95rem;
            width: 300px;
            transition: all 0.3s ease;
        }
        
        /* Length Control */
        .dataTables_length {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        .dataTables_length label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            color: var(--text-color);
            font-weight: 500;
        }
        
        .dataTables_length select {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 8px;
            padding: 0.6rem 2rem 0.6rem 1rem;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }
        
        /* Focus states */
        .dataTables_length select:focus,
        .dataTables_filter input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
        }
        
        /* Pagination */
        .dataTables_paginate {
            display: inline-flex;
            justify-content: flex-end;
            align-items: center;
            gap: 0.25rem;
            margin-top: 1.5rem;
            padding: 0.5rem 0;
            background-color: transparent;
            border-radius: 10px;
            border: none;
            box-shadow: none;
        }
        
        .dataTables_paginate .paginate_button {
            min-width: 32px;
            height: 32px;
            padding: 0;
            margin: 0 1px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card) !important;
            color: var(--text-color) !important;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }
        
        .dataTables_paginate .paginate_button.current {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: white !important;
            font-weight: 600;
        }
        
        .dataTables_paginate .paginate_button:hover:not(.current):not(.disabled) {
            background-color: var(--hover-color) !important;
            border-color: var(--primary-color) !important;
            color: var(--text-color) !important;
        }
        
        .dataTables_paginate .paginate_button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
            background-color: var(--bg-card) !important;
        }
        
        /* Info text */
        .dataTables_info {
            color: var(--text-color);
            font-size: 0.9rem;
            padding-top: 0.85rem;
            font-weight: 500;
            text-align: right;
        }
        
        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }
        
        .action-buttons .btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .action-buttons .btn i {
            font-size: 1rem;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dataTables_length,
            .dataTables_filter {
                justify-content: flex-start;
                margin-bottom: 1rem;
            }
            
            .dataTables_filter input {
                width: 100%;
            }
            
            .dataTables_paginate {
                justify-content: flex-end;
                flex-wrap: wrap;
                gap: 0.25rem;
                padding: 0.5rem 0;
                margin-top: 1rem;
            }
            
            .dataTables_info {
                text-align: right;
                margin-bottom: 0.5rem;
            }
        }
        
        .nav-pills .nav-link {
            color: var(--text-color);
            border-radius: 10px;
            padding: 0.8rem 1.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link:hover {
            background-color: var(--hover-color);
        }
        
        .nav-pills .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .modal-content {
            background-color: var(--bg-card);
            color: var(--text-color);
        }
        
        .modal-header {
            border-bottom-color: var(--border-color);
        }
        
        .modal-footer {
            border-top-color: var(--border-color);
        }
        
        /* Contract Management Table Styling */
        #contracts-table {
            margin-bottom: 0;
            border-collapse: collapse;
        }

        #contracts-table thead th {
            position: sticky;
            top: 0;
            background-color: var(--bg-main);
            z-index: 10;
            border: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--text-color);
            text-align: center;
            vertical-align: middle;
            padding: 1rem;
        }

        #contracts-table tbody td {
            border: 1px solid var(--border-color);
            vertical-align: middle;
            padding: 0.75rem 1rem;
            text-align: center;
        }

        #contracts-table tbody tr {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        #contracts-table tbody tr:hover td {
            background-color: var(--hover-color);
        }
        
        /* Select2 Styling */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
            height: 38px;
            line-height: 38px;
            border-radius: 8px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            color: var(--text-color);
            padding-left: 12px;
            padding-right: 30px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
            right: 6px;
        }

        .select2-dropdown {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
            border-radius: 4px;
            padding: 6px 10px;
        }

        .select2-container--default .select2-results__option {
            color: var(--text-color);
            padding: 8px 12px;
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color);
            color: white;
        }

        /* Dark theme support for Select2 */
        [data-theme="dark"] .select2-container--default .select2-selection--single {
            background-color: var(--dark-bg);
            border-color: var(--dark-border);
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__rendered {
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-dropdown {
            background-color: #1a1d20 !important;
            border-color: #2c3034 !important;
        }

        [data-theme="dark"] .select2-container--default .select2-search--dropdown .select2-search__field {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
            border-color: #2c3034 !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }
        
        /* Limit dropdown height and add scrolling */
        .select2-results__options {
            max-height: 250px !important;
            overflow-y: auto !important;
        }
        
        /* Add custom scrollbar to dropdown */
        .select2-results__options::-webkit-scrollbar {
            width: 6px;
        }
        
        .select2-results__options::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 3px;
        }
        
        .select2-results__options::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }
        
        [data-theme="dark"] .select2-results__options::-webkit-scrollbar-track {
            background: var(--dark-bg);
        }
        
        [data-theme="dark"] .select2-results__options::-webkit-scrollbar-thumb {
            background: var(--dark-primary);
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <!-- Main Content -->
    <main id="content">
        <div class="container-fluid py-4">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="card-title mb-0">إدارة البيانات الأساسية</h5>
                                <?php if ($selected_table !== 'contracts'): ?>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                                    <i class="bi bi-plus-lg"></i> إضافة جديد
                                </button>
                                <?php else: ?>
                                <a href="create_contract_con.php" class="btn btn-success">
                                    <i class="bi bi-file-earmark-plus"></i> إنشاء عقد جديد
                                </a>
                                <?php endif; ?>
                            </div>

                            <ul class="nav nav-pills mb-4">
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $selected_table === 'administrators' ? 'active' : ''; ?>" 
                                       href="?table=administrators">المسؤولين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $selected_table === 'implementing_organizations' ? 'active' : ''; ?>" 
                                       href="?table=implementing_organizations">المنظمات المنفذة</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $selected_table === 'target_areas' ? 'active' : ''; ?>" 
                                       href="?table=target_areas">المناطق المستهدفة</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $selected_table === 'funders' ? 'active' : ''; ?>" 
                                       href="?table=funders">الممولين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $selected_table === 'contracts' ? 'active' : ''; ?>" 
                                       href="?table=contracts">العقود</a>
                                </li>
                            </ul>

                            <div class="table-container">
                                <?php if ($selected_table === 'contracts'): ?>
                                    <!-- Contracts Table Section -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                                <input type="text" id="contract-search" class="form-control" placeholder="البحث في العقود...">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <select id="project-filter" class="form-select select2-search">
                                                <option value="0">جميع المشاريع</option>
                                                <?php foreach ($projects as $project): ?>
                                                    <option value="<?php echo htmlspecialchars($project['id_Project']); ?>">
                                                        <?php echo htmlspecialchars($project['Project_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table id="contracts-table" class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th style="text-align: center; vertical-align: middle; border: 1px solid var(--border-color);">رقم العقد</th>
                                                    <th style="text-align: center; vertical-align: middle; border: 1px solid var(--border-color);">اسم الموظف</th>
                                                    <th style="text-align: center; vertical-align: middle; border: 1px solid var(--border-color);">رقم المشروع</th>
                                                    <th style="text-align: center; vertical-align: middle; border: 1px solid var(--border-color);">نوع العقد</th>
                                                    <th style="text-align: center; vertical-align: middle; border: 1px solid var(--border-color);">تاريخ الإنشاء</th>
                                                    <th style="text-align: center; vertical-align: middle; border: 1px solid var(--border-color);">الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- سيتم ملء البيانات عبر AJAX -->
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                <table class="table" id="recordsTable">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>الاسم (عربي)</th>
                                            <th>Name (English)</th>
                                            <?php if ($selected_table === 'administrators'): ?>
                                                <th>الدور</th>
                                                <th>Role</th>
                                            <?php endif; ?>
                                            <th>تاريخ الإضافة</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (isset($result) && $result->num_rows > 0): ?>
                                            <?php while ($row = $result->fetch_assoc()): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($row["id_" . $selected_table]); ?></td>
                                                    <td><?php echo htmlspecialchars($row[$selected_table === 'administrators' ? 'full_name_ar' : 'name_ar']); ?></td>
                                                    <td><?php echo htmlspecialchars($row[$selected_table === 'administrators' ? 'full_name_en' : 'name_en']); ?></td>
                                                    <?php if ($selected_table === 'administrators'): ?>
                                                        <td><?php echo htmlspecialchars($row['role']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['role_en']); ?></td>
                                                    <?php endif; ?>
                                                    <td><?php echo date('Y-m-d H:i', strtotime($row['add'])); ?></td>
                                                    <td>
                                                        <span class="badge <?php echo $row['status'] == 1 ? 'bg-success' : 'bg-warning'; ?>">
                                                            <?php echo htmlspecialchars($row['status_text']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <button type="button" class="btn btn-sm btn-primary" 
                                                                    onclick="editRecord(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                                                <i class="bi bi-pencil"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm <?php echo $row['status'] == 1 ? 'btn-warning' : 'btn-success'; ?>" 
                                                                    onclick="toggleStatus(<?php echo $row["id_" . $selected_table]; ?>, <?php echo $row['status']; ?>)">
                                                                <i class="bi <?php echo $row['status'] == 1 ? 'bi-pause-fill' : 'bi-play-fill'; ?>"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <?php if ($selected_table === 'administrators'): ?>
                                                    <td></td>
                                                    <td></td>
                                                <?php endif; ?>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="addModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="recordForm" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="formAction" value="add">
                        <input type="hidden" name="table" value="<?php echo $selected_table; ?>">
                        <input type="hidden" name="id" id="recordId">
                        
                        <div class="mb-3">
                            <label class="form-label">الاسم (عربي)</label>
                            <input type="text" class="form-control" name="name_ar" id="nameAr" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Name (English)</label>
                            <input type="text" class="form-control" name="name_en" id="nameEn" required>
                        </div>
                        
                        <?php if ($selected_table === 'administrators'): ?>
                            <div class="mb-3">
                                <label class="form-label">الدور (عربي)</label>
                                <input type="text" class="form-control" name="role" id="role" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Role (English)</label>
                                <input type="text" class="form-control" name="role_en" id="roleEn" required>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Status Toggle Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statusModalTitle">تأكيد تغيير الحالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="statusModalBody">
                    هل أنت متأكد من تغيير حالة هذا السجل؟
                </div>
                <div class="modal-footer">
                    <form method="POST">
                        <input type="hidden" name="action" value="toggle_status">
                        <input type="hidden" name="table" value="<?php echo $selected_table; ?>">
                        <input type="hidden" name="id" id="statusRecordId">
                        <input type="hidden" name="current_status" id="currentStatus">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" id="confirmStatusBtn">تأكيد</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/lib/datatables/jquery.dataTables.min.js"></script>
    <script src="../assets/lib/datatables/dataTables.bootstrap5.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        const addModal = new bootstrap.Modal(document.getElementById('addModal'));
        const statusModal = new bootstrap.Modal(document.getElementById('statusModal'));
        const selected_table = '<?php echo $selected_table; ?>';
        
        // Initialize DataTables
        $(document).ready(function() {
            // Suppress DataTables warning alerts
            $.fn.dataTable.ext.errMode = 'none';
            
            // Initialize contracts table if we're on the contracts tab
            if (selected_table === 'contracts') {
                // Destroy existing DataTable instance if it exists
                if ($.fn.DataTable.isDataTable('#contracts-table')) {
                    $('#contracts-table').DataTable().destroy();
                }
                
                $('#contracts-table').DataTable({
                    processing: true,
                    serverSide: false,
                    searching: false, // Disable built-in search as we'll implement our own
                    paging: true,
                    pageLength: 5,
                    lengthChange: false,
                    info: true,
                    autoWidth: false,
                    responsive: true,
                    language: {
                        "emptyTable": "لا توجد عقود متاحة",
                        "info": "عرض _START_ إلى _END_ من _TOTAL_ عقد",
                        "infoEmpty": "عرض 0 إلى 0 من 0 عقد",
                        "infoFiltered": "(تمت تصفيته من _MAX_ عقد)",
                        "lengthMenu": "عرض _MENU_ عقد",
                        "loadingRecords": "جاري التحميل...",
                        "processing": "جاري المعالجة...",
                        "search": "بحث:",
                        "zeroRecords": "لم يتم العثور على عقود مطابقة",
                        "paginate": {
                            "first": "الأول",
                            "last": "الأخير",
                            "next": "التالي",
                            "previous": "السابق"
                        }
                    },
                    ajax: {
                        url: 'counter_essential_set.php?action=get_contracts',
                        dataSrc: 'data',
                        data: function(d) {
                            d.search = $('#contract-search').val();
                            d.project_id = $('#project-filter').val();
                            return d;
                        }
                    },
                    columns: [
                        { data: 'id_contract' },
                        { data: 'name_ar_contract' },
                        { data: 'id_Project' },
                        { 
                            data: 'contract_type_name',
                            render: function(data, type, row) {
                                if (type === 'display') {
                                    let badgeClass = '';
                                    if (data === 'شهري مفتوح') {
                                        badgeClass = 'bg-primary';
                                    } else if (data === 'شهري مغلق') {
                                        badgeClass = 'bg-success';
                                    } else if (data === 'يومي مفتوح') {
                                        badgeClass = 'bg-warning';
                                    } else if (data === 'يومي مغلق') {
                                        badgeClass = 'bg-info';
                                    } else {
                                        badgeClass = 'bg-secondary';
                                    }
                                    return '<span class="badge ' + badgeClass + '">' + data + '</span>';
                                }
                                return data;
                            }
                        },
                        { data: 'add_contract' },
                        { 
                            data: 'status_contract',
                            render: function(data, type, row) {
                                if (type === 'display') {
                                    const buttonClass = data == 1 ? 'btn-warning' : 'btn-success';
                                    const iconClass = data == 1 ? 'bi-pause-fill' : 'bi-play-fill';
                                    const buttonTitle = data == 1 ? 'تعليق العقد' : 'تنشيط العقد';
                                    return '<button type="button" class="btn btn-sm ' + buttonClass + '" onclick="toggleContractStatus(' + row.id_contract + ', ' + data + ')" title="' + buttonTitle + '"><i class="bi ' + iconClass + '"></i></button>';
                                }
                                return data;
                            }
                        }
                    ],
                    order: [[0, 'desc']], // Sort by contract ID descending
                    drawCallback: function() {
                        // Apply theme to table after drawing
                        const theme = $('body').attr('data-theme');
                        if (theme === 'dark') {
                            $('#contracts-table').addClass('table-dark');
                        } else {
                            $('#contracts-table').removeClass('table-dark');
                        }
                    }
                });
                
                // Handle search input
                $('#contract-search').on('keyup', function() {
                    try {
                        $('#contracts-table').DataTable().ajax.reload();
                    } catch (e) {
                        console.log('Error reloading table:', e);
                    }
                });
                
                // Handle project filter change
                $('#project-filter').on('change', function() {
                    try {
                        $('#contracts-table').DataTable().ajax.reload();
                    } catch (e) {
                        console.log('Error reloading table:', e);
                    }
                });
                
                // Initialize Select2 for project filter
                $('#project-filter').select2({
                    placeholder: 'تصفية حسب المشروع',
                    allowClear: true,
                    language: { noResults: () => "لا توجد نتائج" },
                    dir: "rtl",
                    width: '100%'
                });
            }
            
            // Initialize regular records table
            $('#recordsTable').DataTable({
                language: {
                    search: "بحث:",
                    lengthMenu: "عرض _MENU_ سجلات",
                    info: "عرض _START_ إلى _END_ من _TOTAL_ سجل",
                    infoEmpty: "لا توجد سجلات متاحة",
                    infoFiltered: "(تمت التصفية من _MAX_ سجل)",
                    zeroRecords: "لا توجد بيانات متوفرة في هذا القسم",
                    emptyTable: "لا توجد بيانات متوفرة في هذا القسم",
                    paginate: {
                        first: '<i class="bi bi-chevron-double-right"></i>',
                        previous: '<i class="bi bi-chevron-right"></i>',
                        next: '<i class="bi bi-chevron-left"></i>',
                        last: '<i class="bi bi-chevron-double-left"></i>'
                    }
                },
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: -1 },
                    { defaultContent: "", targets: "_all" }
                ],
                dom: '<"row"<"col-sm-6"f><"col-sm-6"l>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-6"i><"col-sm-6 d-flex justify-content-end"p>>',
                drawCallback: function() {
                    // Update table row colors and ensure proper theme colors
                    $('.table tbody tr').each(function() {
                        $(this).find('td').css('background-color', 'var(--bg-card)');
                    });
                    
                    // Fix pagination button backgrounds
                    $('.paginate_button').each(function() {
                        if (!$(this).hasClass('current')) {
                            $(this).css('background-color', 'var(--bg-card)');
                        }
                    });
                },
                initComplete: function(settings, json) {
                    // If table is empty, add a custom message
                    if (this.api().data().length === 0) {
                        $(this.api().table().body()).html(
                            '<tr class="odd">' +
                            '<td valign="top" colspan="' + this.api().columns().nodes().length + '" class="text-center">' +
                            'لا توجد بيانات متوفرة في هذا القسم' +
                            '</td>' +
                            '</tr>'
                        );
                    }
                }
            });
        });
        
        // Theme change handler
        document.addEventListener('themeChanged', function() {
            // Update table row colors when theme changes
            $('.table tbody tr').each(function() {
                $(this).find('td').css('background-color', 'var(--bg-card)');
            });
            
            // Update pagination button backgrounds
            $('.paginate_button').each(function() {
                if (!$(this).hasClass('current')) {
                    $(this).css('background-color', 'var(--bg-card)');
                }
            });
        });
        
        function editRecord(record) {
            document.getElementById('modalTitle').textContent = 'تعديل البيانات';
            document.getElementById('formAction').value = 'edit';
            document.getElementById('recordId').value = record[`id_${selected_table}`];
            
            if (selected_table === 'administrators') {
                document.getElementById('nameAr').value = record.full_name_ar;
                document.getElementById('nameEn').value = record.full_name_en;
                document.getElementById('role').value = record.role;
                document.getElementById('roleEn').value = record.role_en;
            } else {
                document.getElementById('nameAr').value = record.name_ar;
                document.getElementById('nameEn').value = record.name_en;
            }
            
            addModal.show();
        }
        
        function toggleStatus(id, currentStatus) {
            document.getElementById('statusModalTitle').textContent = currentStatus === 1 ? 'تأكيد تعليق السجل' : 'تأكيد تنشيط السجل';
            document.getElementById('statusModalBody').textContent = currentStatus === 1 ? 
                'هل أنت متأكد من تعليق هذا السجل؟' : 'هل أنت متأكد من تنشيط هذا السجل؟';
            document.getElementById('statusRecordId').value = id;
            document.getElementById('currentStatus').value = currentStatus;
            document.getElementById('confirmStatusBtn').className = `btn ${currentStatus === 1 ? 'btn-warning' : 'btn-success'}`;
            statusModal.show();
        }
        
        // Reset form when modal is closed
        document.getElementById('addModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('modalTitle').textContent = 'إضافة جديد';
            document.getElementById('recordForm').reset();
            document.getElementById('formAction').value = 'add';
            document.getElementById('recordId').value = '';
        });

        function toggleContractStatus(contractId, currentStatus) {
            if (!confirm(currentStatus == 1 ? 'هل أنت متأكد من تعليق هذا العقد؟' : 'هل أنت متأكد من تنشيط هذا العقد؟')) {
                return;
            }
            
            $.ajax({
                url: 'counter_essential_set.php?action=toggle_contract_status',
                method: 'POST',
                data: {
                    contract_id: contractId,
                    current_status: currentStatus
                },
                success: function(response) {
                    if (response.success) {
                        $('#contracts-table').DataTable().ajax.reload(null, false);
                    }
                },
                error: function(xhr) {
                    alert('حدث خطأ أثناء تحديث حالة العقد');
                }
            });
        }
    </script>
</body>
</html> 

