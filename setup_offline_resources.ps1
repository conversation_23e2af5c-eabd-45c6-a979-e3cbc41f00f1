# Create necessary directories
New-Item -ItemType Directory -Force -Path "assets/fonts"
New-Item -ItemType Directory -Force -Path "assets/css"

# Download Cairo font files
$fontUrls = @{
    "cairo-regular.woff2" = "https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hGA-W1Q.woff2"
    "cairo-regular.woff" = "https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hGA-W1Q.woff"
    "cairo-semibold.woff2" = "https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hL4-W1Q.woff2"
    "cairo-semibold.woff" = "https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hL4-W1Q.woff"
    "cairo-bold.woff2" = "https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hOY-W1Q.woff2"
    "cairo-bold.woff" = "https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hOY-W1Q.woff"
}

foreach ($font in $fontUrls.GetEnumerator()) {
    $outputPath = "assets/fonts/$($font.Key)"
    Write-Host "Downloading $($font.Key)..."
    Invoke-WebRequest -Uri $font.Value -OutFile $outputPath
}

Write-Host "Font files have been downloaded and placed in the assets/fonts directory."
Write-Host "Setup complete! Your application should now work offline." 