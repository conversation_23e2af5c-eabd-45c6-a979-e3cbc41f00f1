<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Fetch all projects for the search dropdown
    $projects = [];
    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }

    // Fetch all job titles for the search dropdown
    $job_titles = [];
    $result = $conn->query("SELECT DISTINCT c.name_Job FROM contract c ORDER BY c.name_Job");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $job_titles[] = $row['name_Job'];
        }
    }

    $contract_data = null;
    $error_message = '';
    $success_message = '';

    // Add this function near the top of the file after the database connection
    function handleFileUpload($employeeId, $fieldName) {
        global $conn;
        
        if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'لم يتم اختيار ملف أو حدث خطأ أثناء الرفع'];
        }

        // Check file type
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        $fileType = $_FILES[$fieldName]['type'];
        
        if (!in_array($fileType, $allowedTypes)) {
            return ['success' => false, 'message' => 'نوع الملف غير مسموح به. يرجى اختيار ملف PDF أو صورة'];
        }

        // Check file size (5MB max)
        if ($_FILES[$fieldName]['size'] > 5 * 1024 * 1024) {
            return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى هو 5 ميجابايت'];
        }

        try {
            $fileContent = file_get_contents($_FILES[$fieldName]['tmp_name']);
            
            // Update the query to use proper SQL syntax
            $sql = "UPDATE employees SET `$fieldName` = ? WHERE id_employees = ?";
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Error preparing statement: " . $conn->error);
            }
            
            $null = null;
            $stmt->bind_param("bi", $fileContent, $employeeId);
            $result = $stmt->execute();
            
            if (!$result) {
                throw new Exception("Error executing statement: " . $stmt->error);
            }
            
            $stmt->close();
            
            // Refresh the contract data after upload
            $refresh_stmt = $conn->prepare("SELECT * FROM employees WHERE id_employees = ?");
            $refresh_stmt->bind_param("i", $employeeId);
            $refresh_stmt->execute();
            $result = $refresh_stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                global $contract_data;
                $contract_data = $row;
            }
            $refresh_stmt->close();
            
            return ['success' => true, 'message' => 'تم رفع الملف بنجاح'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'حدث خطأ أثناء معالجة لملف: ' . $e->getMessage()];
        }
    }

    // Update file upload handling
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_file'])) {
        $employeeId = $_POST['employee_id'];
        $fieldName = $_POST['field_name'];
        
        $result = handleFileUpload($employeeId, $fieldName);
        if ($result['success']) {
            $success_message = $result['message'];
            
            // Refresh the contract data
            $refresh_stmt = $conn->prepare("SELECT * FROM employees WHERE id_employees = ?");
            $refresh_stmt->bind_param("i", $employeeId);
            $refresh_stmt->execute();
            $result = $refresh_stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $contract_data = $row;
            }
            $refresh_stmt->close();
        } else {
            $error_message = $result['message'];
        }
    }

    // Add file download handling
    if (isset($_GET['download']) && isset($_GET['employee_id']) && isset($_GET['field'])) {
        $employeeId = $_GET['employee_id'];
        $fieldName = $_GET['field'];
        
        // Wrap the column name in backticks
        $stmt = $conn->prepare("SELECT `$fieldName` FROM employees WHERE id_employees = ?");
        $stmt->bind_param("i", $employeeId);
        $stmt->execute();
        $stmt->store_result();
        $stmt->bind_result($fileContent);
        
        if ($stmt->fetch() && $fileContent) {
            // Detect file type from the first few bytes
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->buffer($fileContent);
            
            switch ($mimeType) {
                case 'application/pdf':
                    $extension = '.pdf';
                    break;
                case 'image/jpeg':
                    $extension = '.jpg';
                    break;
                case 'image/png':
                    $extension = '.png';
                    break;
                default:
                    $extension = '';
            }

            // Set appropriate headers
            header('Content-Type: ' . $mimeType);
            header('Content-Disposition: attachment; filename="' . $fieldName . '_' . $employeeId . $extension . '"');
            header('Content-Length: ' . strlen($fileContent));
            header('Cache-Control: private, must-revalidate, max-age=0');
            header('Pragma: public');
            
            echo $fileContent;
            exit;
        }
        
        $stmt->close();
        $error_message = "الملف غير موجود";
    }

    // Add this near the download handler
    if (isset($_GET['preview']) && isset($_GET['employee_id']) && isset($_GET['field'])) {
        $employeeId = $_GET['employee_id'];
        $fieldName = $_GET['field'];
        
        // Wrap the column name in backticks
        $stmt = $conn->prepare("SELECT `$fieldName` FROM employees WHERE id_employees = ?");
        $stmt->bind_param("i", $employeeId);
        $stmt->execute();
        $stmt->store_result();
        $stmt->bind_result($fileContent);
        
        if ($stmt->fetch() && $fileContent) {
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->buffer($fileContent);
            
            header('Content-Type: ' . $mimeType);
            echo $fileContent;
            exit;
        }
        
        $stmt->close();
        echo "الملف غير موجود";
        exit;
    }

    // Handle search request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search'])) {
        // Verify that project is selected (required)
        if (!isset($_POST['project_id']) || empty($_POST['project_id'])) {
            $error_message = 'يرجى اختيار المشروع قبل البحث';
        } else {
            $conditions = [];
            $params = [];
            $types = '';

            // Base query with joins to get executor details
            $sql = "SELECT t.*, p.Project_name, 
                    e.name_ar_contract as employee_name_ar, e.name_en_contract as employee_name_en,
                    e.phone_number as employee_phone, e.Identity_number_contract as employee_identity,
                    a.name_ar_assigned as delegate_name_ar, a.name_en_assigned as delegate_name_en,
                    a.phone_number as delegate_phone, a.Identity_number_assigned as delegate_identity
                    FROM tasks t 
                    LEFT JOIN project p ON t.id_Project = p.id_Project 
                    LEFT JOIN employees e ON t.id_employees = e.id_employees
                    LEFT JOIN assigned a ON t.id_assigned = a.id_assigned 
                    WHERE 1=1";

            // Add executor type condition - MODIFIED to allow both types
            if (isset($_POST['executor_type']) && $_POST['executor_type'] !== '') {
                if ($_POST['executor_type'] === 'employee') {
                    $conditions[] = "t.id_employees IS NOT NULL AND t.id_assigned IS NULL";
                } elseif ($_POST['executor_type'] === 'delegate') {
                    $conditions[] = "t.id_assigned IS NOT NULL AND t.id_employees IS NULL";
                }
                // If executor_type is empty, we don't add any condition - show both
            }

            // Add project filter condition (required)
            if (isset($_POST['project_id']) && !empty($_POST['project_id'])) {
                $conditions[] = "t.id_Project = ?";
                $params[] = intval($_POST['project_id']);
                $types .= 'i';
            }

            // Search by task name or task number
            if (!empty($_POST['name_ar_contract'])) {
                $searchTerm = '%' . $_POST['name_ar_contract'] . '%';
                // Check if the search term is numeric (potential task number)
                if (is_numeric($_POST['name_ar_contract'])) {
                    $conditions[] = "(t.name_TASKS LIKE ? OR t.name_TASKS_en LIKE ? OR t.id_TASKS = ?)";
                    $params = array_merge($params, [$searchTerm, $searchTerm, intval($_POST['name_ar_contract'])]);
                    $types .= 'ssi';
                } else {
                    $conditions[] = "(t.name_TASKS LIKE ? OR t.name_TASKS_en LIKE ?)";
                    $params = array_merge($params, [$searchTerm, $searchTerm]);
                    $types .= 'ss';
                }
            }

            // Add conditions to query
            if (!empty($conditions)) {
                $sql .= " AND " . implode(" AND ", $conditions);
            }

            // Add ORDER BY clause
            $sql .= " ORDER BY t.id_TASKS DESC";

            // Prepare and execute the query
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Query preparation failed: " . $conn->error);
            }

            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }

            if (!$stmt->execute()) {
                throw new Exception("Query execution failed: " . $stmt->error);
            }

            $result = $stmt->get_result();
            $search_results = [];
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    // Decode JSON data_todo_list_TASKS
                    $row['data_todo_list_TASKS'] = json_decode($row['data_todo_list_TASKS'], true);
                    $search_results[] = $row;
                }
                $success_message = 'تم العثور على ' . count($search_results) . ' مهمة';
            } else {
                $error_message = 'لم يتم العثور على أي مهام تطابق معايير البحث';
            }

            $stmt->close();

            // If a specific task is selected
            if (isset($_POST['selected_task'])) {
                $task_id = (int) $_POST['selected_task'];
                
                $sql = "SELECT t.*, p.Project_name,
                        e.name_ar_contract, e.name_en_contract, e.phone_number as employee_phone, e.Identity_number_contract,
                        a.name_ar_assigned, a.name_en_assigned, a.phone_number as delegate_phone, a.Identity_number_assigned
                        FROM tasks t 
                        LEFT JOIN project p ON t.id_Project = p.id_Project 
                        LEFT JOIN employees e ON t.id_employees = e.id_employees
                        LEFT JOIN assigned a ON t.id_assigned = a.id_assigned
                        WHERE t.id_TASKS = ?";
                
                // Add project filter if provided
                $taskParams = [$task_id];
                $taskTypes = "i";
                
                if (isset($_POST['project_id']) && !empty($_POST['project_id'])) {
                    $sql .= " AND t.id_Project = ?";
                    $taskParams[] = intval($_POST['project_id']);
                    $taskTypes .= "i";
                }
                
                $stmt = $conn->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Query preparation failed: " . $conn->error);
                }
                
                $stmt->bind_param($taskTypes, ...$taskParams);
                
                if (!$stmt->execute()) {
                    throw new Exception("Query execution failed: " . $stmt->error);
                }

                $result = $stmt->get_result();
                if ($result && $result->num_rows > 0) {
                    $task_data = $result->fetch_assoc();
                    // Store both the decoded and original JSON
                    $task_data['data_todo_list_TASKS_decoded'] = json_decode($task_data['data_todo_list_TASKS'], true);
                    $success_message = 'تم العثور على بيانات المهمة';
                } else {
                    $error_message = 'لم يتم العثور على بيانات المهمة';
                }
                $stmt->close();
                
                // Fetch previous achievement reports for this task
                if (isset($task_data)) {
                    $prev_reports_sql = "SELECT id_achievement_reports_TASKS, 
                                                start_date_achievement_reports, 
                                                end_date_achievement_reports,
                                                data_todo_list_achievement,
                                                add_achievement_reports
                                         FROM achievement_reports_tasks 
                                         WHERE id_TASKS = ? 
                                         ORDER BY add_achievement_reports DESC";
                    
                    $stmt = $conn->prepare($prev_reports_sql);
                    $stmt->bind_param("i", $task_id);
                    $stmt->execute();
                    $prev_reports_result = $stmt->get_result();
                    
                    $previous_achievement_reports = [];
                    while ($report = $prev_reports_result->fetch_assoc()) {
                        $previous_achievement_reports[] = $report;
                    }
                    $stmt->close();
                    
                    // Check if there are previous achievement reports
                    if (count($previous_achievement_reports) > 0) {
                        // Get the most recent achievement report
                        $latest_report = $previous_achievement_reports[0];
                        
                        // Use the task code from the most recent achievement report instead of from the task table
                        if (!empty($latest_report['data_todo_list_achievement'])) {
                            $task_data['data_todo_list_TASKS'] = $latest_report['data_todo_list_achievement'];
                            $task_data['data_todo_list_TASKS_decoded'] = json_decode($latest_report['data_todo_list_achievement'], true);
                        }
                    }
                    
                    // Prepare existing report dates for JavaScript
                    $existing_report_dates = [];
                    foreach ($previous_achievement_reports as $report) {
                        $start_date = new DateTime($report['start_date_achievement_reports']);
                        $end_date = new DateTime($report['end_date_achievement_reports']);
                        $interval = new DateInterval('P1D'); // 1 day interval
                        $date_range = new DatePeriod($start_date, $interval, $end_date->modify('+1 day'));
                        
                        foreach ($date_range as $date) {
                            $existing_report_dates[] = $date->format('Y-m-d');
                        }
                    }
                    // Convert to JSON for JavaScript
                    $existing_report_dates_json = json_encode($existing_report_dates);
                }
            }
        }
    }

    // Fetch attendance records (permanent diapers)
    $attendance_records = [];
    $attendance_sql = "SELECT id_permanent_diapers, 
                             start_date_permanent_diapers, 
                             end_date_permanent_diapers,
                             add_permanent_diapers 
                      FROM permanent_diapers 
                      WHERE id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $attendance_sql .= " AND id_extension_contract = ?";
        $stmt = $conn->prepare($attendance_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $attendance_sql .= " AND (id_extension_contract IS NULL OR id_extension_contract = 0)";
        $stmt = $conn->prepare($attendance_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $attendance_records[] = $row;
    }

    // Fetch achievement reports
    $achievement_reports = [];
    $achievement_sql = "SELECT ar.id_achievement_reports,
                              ar.start_date_achievement_reports,
                              ar.end_date_achievement_reports,
                              ar.actual_working_days,
                              ar.add_achievement_reports,
                              pd.id_permanent_diapers
                       FROM achievement_reports ar
                       JOIN permanent_diapers pd ON ar.id_permanent_diapers = pd.id_permanent_diapers
                       WHERE ar.id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $achievement_sql .= " AND ar.id_extension_contract = ?";
        $stmt = $conn->prepare($achievement_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $achievement_sql .= " AND (ar.id_extension_contract IS NULL OR ar.id_extension_contract = 0)";
        $stmt = $conn->prepare($achievement_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        // Calculate working days if actual_working_days is 0
        if ($row['actual_working_days'] == 0) {
            $start = new DateTime($row['start_date_achievement_reports']);
            $end = new DateTime($row['end_date_achievement_reports']);
            $interval = $start->diff($end);
            $row['actual_working_days'] = $interval->days + 1;
        }
        $achievement_reports[] = $row;
    }

    // Fetch merit reports
    $merit_reports = [];
    $merit_sql = "SELECT mr.id_merit_reports,
                         mr.actual_working_days,
                         mr.today_wage,
                         mr.total,
                         ar.id_achievement_reports,
                         ar.start_date_achievement_reports,
                         ar.end_date_achievement_reports
                  FROM merit_reports mr
                  JOIN achievement_reports ar ON mr.id_achievement_reports = ar.id_achievement_reports
                  WHERE mr.id_contract = ?";

    // Add extension contract condition if viewing an extension
    if (isset($contract_data['id_extension_contract'])) {
        $merit_sql .= " AND mr.id_extension_contract = ?";
        $stmt = $conn->prepare($merit_sql);
        $stmt->bind_param("ii", $contract_data['id_contract'], $contract_data['id_extension_contract']);
    } else {
        $merit_sql .= " AND (mr.id_extension_contract IS NULL OR mr.id_extension_contract = 0)";
        $stmt = $conn->prepare($merit_sql);
        $stmt->bind_param("i", $contract_data['id_contract']);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $merit_reports[] = $row;
    }

    // Fetch extended contracts if this is a main contract
    $extended_contracts = [];
    if (isset($_POST['selected_contract']) && strpos($_POST['selected_contract'], 'contract_') === 0) {
        $contract_id = (int) str_replace('contract_', '', $_POST['selected_contract']);
        
        $ext_sql = "SELECT ec.*, 
                           DATE_FORMAT(ec.version_date, '%d-%m-%Y') as formatted_version_date,
                           DATE_FORMAT(ec.start_date_contract, '%d-%m-%Y') as formatted_start_date,
                           COALESCE(DATE_FORMAT(ec.end_date_contract, '%d-%m-%Y'), 'مفتوح') as formatted_end_date
                    FROM extension_contract ec 
                    WHERE ec.id_contract = ? 
                    ORDER BY ec.version_date DESC";
        
        $stmt = $conn->prepare($ext_sql);
        $stmt->bind_param("i", $contract_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $extended_contracts[] = $row;
        }
        $stmt->close();
    }

} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

function formatDate($date) {
    if (empty($date)) return "غير محدد";
    return date("Y/m/d", strtotime($date));
}

function getContractTypeName($type) {
    switch ($type) {
        case 1: return "راتب شهري";
        case 2: return "أجر يومي";
        default: return "غير محدد";
    }
}

// First, add this helper function near the other helper functions at the top of the file
function getCurrencyTypeName($type) {
    switch ($type) {
        case 1: return "دولار";
        case 2: return "ريال";
        default: return "غير محدد";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير عقد العمل - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Updated styles for better dark theme compatibility */
        .search-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 2px 15px var(--shadow-color);
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filters {
            padding: 1rem;
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .quick-filter-item {
            min-width: 150px;
            position: relative;
        }

        .quick-filter-item .form-select,
        .quick-filter-item .select2-container .select2-selection--single {
            border-radius: 20px;
            padding: 0.4rem 2rem 0.4rem 1rem;
            border: 1px solid var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .quick-filter-item .form-select:hover,
        .quick-filter-item .select2-container .select2-selection--single:hover {
            border-color: var(--primary-color);
        }

        .quick-filter-item .form-select:focus,
        .quick-filter-item .select2-container .select2-selection--single:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .advanced-filters-container {
            padding: 1.5rem;
            background: var(--bg-main);
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .filter-group {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            box-shadow: 0 1px 3px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .filter-group-title {
            color: var(--text-color);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-group-title i {
            color: var(--primary-color);
        }

        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .form-control {
            background-color: var(--bg-input);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .form-control:focus {
            background-color: var(--bg-input);
            border-color: var(--primary-color);
            color: var(--text-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
            border-radius: 20px;
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Results styles with dark theme support */
        .results-container {
            margin-top: 1.5rem;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .results-header {
            position: sticky;
            top: 0;
            background: var(--bg-main);
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            z-index: 1;
            transition: all 0.3s ease;
        }

        .results-search input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-input);
            color: var(--text-color);
            transition: all 0.2s ease;
        }

        .results-search input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .result-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-card);
        }

        .result-item:hover {
            background: var(--hover-color);
        }

        .result-item-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .result-item-info {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .select-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Select2 Dark Theme Adjustments */
        .select2-container--bootstrap-5 .select2-selection {
            background-color: var(--bg-input) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--bg-card) !important;
            border-color: var(--border-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            color: var(--text-color) !important;
            background-color: var(--bg-card) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--hover-color) !important;
            color: var(--text-color) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: var(--primary-color) !important;
            color: white !important;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .search-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .quick-filters {
            background: var(--bg-card);
        }

        [data-theme="dark"] .advanced-filters-container {
            background: var(--bg-main);
        }

        [data-theme="dark"] .filter-group {
            background: var(--bg-card);
        }

        [data-theme="dark"] input[type="date"] {
            color-scheme: dark;
        }

        /* Scrollbar styling */
        .results-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .results-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .results-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .quick-filter-item {
                min-width: 120px;
            }
            
            .filter-group {
                margin-bottom: 1rem;
            }
            
            .advanced-filters-container {
                padding: 1rem;
            }
            
            .filter-actions {
                justify-content: center;
            }
        }

        /* Add these styles for the Contract Details section */
        .report-container {
            background: var(--bg-card);
            border-radius: 15px;
            box-shadow: 0 2px 15px var(--shadow-color);
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            color: var(--text-color);
            margin: 0;
            font-weight: 600;
        }

        .report-header .btn-print {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .report-header .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .report-section {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .report-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1.25rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            background: var(--bg-card);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        .info-value.text-primary {
            color: var(--primary-color) !important;
        }

        .info-value.text-success {
            color: var(--success-color) !important;
        }

        .badge {
            padding: 0.5em 0.75em;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .badge-primary {
            background: var(--primary-color);
            color: white;
        }

        .badge-success {
            background: var(--success-color);
            color: white;
        }

        .task-list {
            display: grid;
            gap: 1rem;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .task-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .task-name-en {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .task-progress {
            width: 100px;
            background: var(--bg-main);
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-value {
            height: 6px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .progress-label {
            color: var(--text-muted);
            font-size: 0.75rem;
            text-align: center;
            margin-top: 0.25rem;
        }

        /* Dark theme adjustments */
        [data-theme="dark"] .report-container {
            background: var(--bg-card);
        }

        [data-theme="dark"] .report-section {
            background: var(--bg-main);
        }

        [data-theme="dark"] .info-item {
            background: var(--bg-card);
        }

        [data-theme="dark"] .task-item {
            background: var(--bg-card);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .report-container {
                margin: 0;
                padding: 0;
                box-shadow: none;
            }

            .report-section {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }

        .extension-item {
            padding-right: 2rem !important;
            position: relative;
            font-size: 0.95em;
            background-color: var(--bg-secondary) !important;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: 1rem;
            top: 50%;
            width: 0.5rem;
            height: 1px;
            background-color: var(--text-muted);
        }

        .result-item-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .result-item:not(.extension-item) {
            margin-top: 0.5rem;
        }

        .result-item:not(.extension-item) + .extension-item {
            margin-top: 0.25rem;
        }

        .tasks-container {
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.5rem;
        }

        .task-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-color);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-size: 0.95rem;
            line-height: 1.4;
        }

        /* Scrollbar styling */
        .tasks-container::-webkit-scrollbar {
            width: 6px;
        }

        .tasks-container::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .tasks-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Add these styles to your existing style section */
        .scrollable-section {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--bg-main);
        }

        .scrollable-section::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-section::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }

        .scrollable-section::-webkit-scrollbar-thumb {
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .search-box {
            margin-left: 1rem;
        }

        .list-group-item {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            background-color: var(--hover-color);
        }

        .list-group-item h6 {
            color: var(--text-color);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .list-group-item small {
            font-size: 0.8rem;
        }

        /* Add these new styles */
        .inner-section {
            background: var(--bg-main);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .inner-section .section-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.35em 0.65em;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        /* Add these new styles */
        .stats-pills {
            display: flex;
            gap: 1rem;
        }

        .stat-pill {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.35rem 0.75rem;
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 0.875rem;
        }

        .stat-pill i {
            color: var(--primary-color);
        }

        .stat-value {
            font-weight: 600;
            color: var(--text-color);
        }

        .stat-label {
            color: var(--text-muted);
        }

        .nav-tabs-custom {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background: var(--bg-main);
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .tab-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: var(--text-muted);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .tab-btn:hover {
            color: var(--text-color);
            background: var(--hover-color);
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .tab-content-wrapper {
            background: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .tab-content {
            display: none;
            height: 400px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .search-box {
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .search-input {
            padding-left: 2.5rem;
            background: var(--bg-main);
        }

        .scrollable-section {
            height: calc(100% - 65px);
            overflow-y: auto;
            padding: 1rem;
        }

        .list-item {
            padding: 1rem;
            border-radius: 8px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
        }

        .item-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .item-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 8px;
            flex-shrink: 0;
        }

        .item-details {
            flex: 1;
        }

        .item-title {
            color: var(--text-color);
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .item-meta {
            color: var(--text-muted);
            font-size: 0.8rem;
            display: flex;
            gap: 1rem;
        }

        .item-meta i {
            margin-right: 0.25rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
        }

        .btn-icon:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        /* Add to the existing style section */
        .extension-list-item {
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .extension-list-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .extension-list-item .item-icon {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .extension-list-item .item-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .extension-list-item .btn-icon:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Add to your existing styles */
        .result-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .result-item:hover {
            background: var(--hover-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-left: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        .extension-item {
            margin-left: 2rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            left: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .result-item-details {
            flex: 1;
        }

        .select-contract-btn {
            padding: 0.375rem 1rem;
            border-radius: 4px;
            background: var(--primary-light);
            color: var(--primary-color);
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .select-contract-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.25em 0.5em;
            border-radius: 4px;
        }

        /* Update these styles in your existing style section */

        .extension-item {
            margin-right: 2rem;
            margin-bottom: 0.5rem;
            border-right: 4px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }

        .extension-item::before {
            content: '';
            position: absolute;
            right: -2rem;
            top: 50%;
            width: 1.5rem;
            height: 2px;
            background-color: var(--border-color);
        }

        .main-contract {
            background: var(--bg-main);
            border-right: 4px solid var(--primary-color);
            margin-top: 1rem;
        }

        /* Add new style for the arrow icon */
        .extension-item .bi-arrow-return-right {
            transform: scaleX(-1);  /* Flip the arrow icon horizontally */
        }

        /* Optional: Add a connecting line from bottom extension to top extension */
        .extension-item::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -2rem;
            width: 2px;
            height: calc(-100% - 0.5rem);
            background-color: var(--border-color);
            z-index: 0;
        }

        /* Remove the vertical line from the first extension */
        .extension-item:first-child::after {
            display: none;
        }

        /* Add hover effect for the connection lines */
        .extension-item:hover::before,
        .extension-item:hover::after {
            background-color: var(--primary-color);
        }

        /* Add these styles to make the modal content more readable */
        .modal-xl {
            max-width: 90%; /* Makes modal wider */
        }
        
        .modal-body {
            padding: 2rem;
        }
        
        .report-details .section {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .info-item {
            background: var(--bg-main);
            padding: 1rem;
            border-radius: 6px;
            height: 100%;
        }
        
        .info-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            color: var(--text-color);
            font-weight: 500;
        }

        /* Modal Styling */
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Modal Header Styling */
        .modal-header {
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header .modal-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            color: var(--text-color);
            margin: 0;
            order: 1; /* Changed from 2 to 1 */
        }

        .modal-header .modal-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .modal-header .btn-close {
            order: 2; /* Changed from 1 to 2 */
            margin: 0;
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-main);
            border-radius: 8px;
            opacity: 1;
            transition: all 0.2s ease;
            position: relative;
        }

        /* Custom close button design */
        .modal-header .btn-close::before,
        .modal-header .btn-close::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 2px;
            background-color: var(--text-color);
            border-radius: 1px;
            transition: all 0.2s ease;
        }

        .modal-header .btn-close::before {
            transform: rotate(45deg);
        }

        .modal-header .btn-close::after {
            transform: rotate(-45deg);
        }

        .modal-header .btn-close:hover {
            background-color: var(--hover-color);
            transform: rotate(90deg);
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .modal-header .btn-close {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] .modal-header .btn-close::before,
        [data-theme="dark"] .modal-header .btn-close::after {
            background-color: var(--text-muted);
        }

        [data-theme="dark"] .modal-header .btn-close:hover {
            background-color: var(--primary-color);
        }

        [data-theme="dark"] .modal-header .btn-close:hover::before,
        [data-theme="dark"] .modal-header .btn-close:hover::after {
            background-color: white;
        }

        .modal-body {
            padding: 2rem;
            background: var(--bg-main);
        }

        /* Report Details Styling */
        .report-details {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .report-section {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .report-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-header i {
            color: var(--primary-color);
            font-size: 1.25rem;
            background: var(--primary-light);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .section-header h6 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--text-color);
        }

        /* Info Grid Styling */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            background: var(--hover-color);
            border-color: var(--primary-color);
        }

        .info-card .label {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .info-card .value {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 500;
        }

        /* Task List Styling */
        .task-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .task-item {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            background: var(--hover-color);
        }

        .task-number {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .task-content {
            flex: 1;
        }

        .task-name {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.75rem;
        }

        .progress-wrapper {
            background: var(--bg-card);
            border-radius: 6px;
            padding: 0.75rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background: var(--bg-main);
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-muted);
            font-size: 0.75rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: var(--bg-main);
            border-radius: 10px;
            padding: 1.25rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            background: var(--primary-light);
            color: var(--primary-color);
            width: 48px;
            height: 48px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Achievement Timeline Report Table Styles */
        .achievement-schedule-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 0;
            border: 1px solid var(--border-color);
        }

        .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            white-space: nowrap;
            position: relative;
        }

        .achievement-schedule-table tbody td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
            transition: all 0.2s ease;
        }

        .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Task name column specific styling */
        .achievement-schedule-table td:first-child {
            text-align: right;
            font-weight: 500;
            color: var(--primary-color);
        }

        /* Percentage/Days values styling */
        .achievement-schedule-table td:not(:first-child):not(:last-child) {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Notes column styling */
        .achievement-schedule-table td:last-child {
            color: var(--text-muted);
            font-style: italic;
        }

        /* Dark theme specific adjustments */
        [data-theme="dark"] .achievement-schedule-table {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .achievement-schedule-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border-bottom-color: var(--primary-color);
        }

        [data-theme="dark"] .achievement-schedule-table tbody td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .achievement-schedule-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Responsive table container */
        .achievement-schedule-container {
            width: 100%;
            overflow-x: auto;
            border-radius: 8px;
            position: relative;
        }

        /* Custom scrollbar styling */
        .achievement-schedule-container::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .achievement-schedule-container::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .achievement-schedule-container::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* Value highlighting */
        .value-percentage {
            color: var(--success-color);
            background: var(--success-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        .value-days {
            color: var(--info-color);
            background: var(--info-bg);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .achievement-schedule-table thead th,
            .achievement-schedule-table tbody td {
                padding: 0.75rem;
                font-size: 0.9rem;
            }
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
            margin-bottom: 0.25rem;
        }

        .status-badge:empty::after {
            content: "-";
            color: var(--text-muted);
        }

        .status-badge.present {
            background-color: var(--success-bg);
            color: var(--success-color);
        }

        .time-display {
            font-size: 0.875rem;
            color: var(--text-primary);
            text-align: center;
        }

        .time-display:empty::after {
            content: "00:00";
            color: var(--text-muted);
        }

        .table th {
            text-align: center;
            vertical-align: middle;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            white-space: nowrap;
        }

        .table td {
            text-align: center;
            vertical-align: middle;
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        /* Attendance Tables Styling */
        .attendance-summary-table,
        .attendance-details-table {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
        }

        .attendance-summary-table th,
        .attendance-details-table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            border-color: var(--border-color);
            text-align: center;
            white-space: nowrap;
        }

        .attendance-summary-table td,
        .attendance-details-table td {
            background-color: var(--bg-card);
            color: var(--text-primary);
            border-color: var(--border-color);
            padding: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .attendance-details-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Status Badge Styling */
        .status-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.35rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            min-width: 80px;
        }

        .status-badge.present {
            background-color: var(--success-light);
            color: var(--success-color);
        }

        .status-badge.leave {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }

        /* Time Display Styling */
        .time-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .time-value {
            font-family: monospace;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        /* Dark Theme Specific Adjustments */
        [data-theme="dark"] .attendance-summary-table,
        [data-theme="dark"] .attendance-details-table {
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] .attendance-summary-table th,
        [data-theme="dark"] .attendance-details-table th {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .attendance-summary-table td,
        [data-theme="dark"] .attendance-details-table td {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .status-badge.present {
            background-color: rgba(var(--success-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.absent {
            background-color: rgba(var(--danger-rgb), 0.2);
        }

        [data-theme="dark"] .status-badge.leave {
            background-color: rgba(var(--warning-rgb), 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .attendance-summary-table,
            .attendance-details-table {
                font-size: 0.875rem;
            }

            .attendance-summary-table th,
            .attendance-details-table th,
            .attendance-summary-table td,
            .attendance-details-table td {
                padding: 0.625rem;
            }

            .status-badge {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                min-width: 60px;
            }
        }

        /* Add these styles to the existing style section */
        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .report-header .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .report-header .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .report-header .btn i {
            font-size: 1rem;
        }

        /* Add to the existing style section */
        .document-card {
            background: var(--bg-main);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
        }

        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .document-card.has-file {
            border-color: var(--success-color);
        }

        .document-card.no-file {
            border-style: dashed;
        }

        .document-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .document-header i {
            font-size: 1.25rem;
            color: var(--primary-color);
            background: var(--primary-light);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .document-header h6 {
            margin: 0;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .document-status {
            margin-bottom: 1rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.35rem 0.75rem;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .status-badge.success {
            background: var(--success-light);
            color: var(--success-color);
        }

        .status-badge.warning {
            background: var(--warning-light);
            color: var(--warning-color);
        }

        .document-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .file-input-wrapper {
            position: relative;
            margin-bottom: 0.75rem;
        }

        .file-input-wrapper input[type="file"] {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .file-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--bg-card);
            border: 1px dashed var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            color: var(--text-muted);
            transition: all 0.2s ease;
        }

        .file-label:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .upload-btn, .download-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.5rem;
            font-size: 0.875rem;
        }

        .upload-btn {
            background: var(--primary-color);
            border: none;
        }

        .download-btn {
            background: var(--secondary-color);
            border: none;
        }

        .upload-btn:hover, .download-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn-group {
            display: flex;
            gap: 0.5rem;
        }

        .preview-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .download-btn {
            flex: 1;
        }

        /* Custom Styles */
        /* Select2 RTL Fixes with Theme Support */
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px !important;
            display: flex !important;
            align-items: center !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            transition: all 0.3s ease !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            width: 100% !important;
            padding-right: 8px !important;
            padding-left: 20px !important;
            display: block !important;
            position: static !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            color: var(--select-text) !important;
        }

        /* Placeholder color */
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
            color: var(--select-placeholder) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            position: absolute !important;
            left: 3px !important;
            right: auto !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
            border-color: var(--select-text) transparent transparent transparent !important;
        }

        .select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow b {
            border-color: transparent transparent var(--select-text) transparent !important;
        }

        /* Dropdown styles */
        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--select-bg) !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            text-align: right !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            padding: 6px 12px !important;
            text-align: right !important;
            color: var(--select-text) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
            background-color: rgba(var(--primary-rgb), 0.1);
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
        
        /* Enhanced select field styling */
        .form-select.select2-search {
            height: 38px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .form-select.select2-search:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.15);
            outline: none;
        }
        
        .form-select.select2-search:hover {
            border-color: #bdbdbd;
        }

        /* Set CSS variables for light/dark themes */
        :root {
            --select-bg: #fff;
            --select-text: #495057;
            --select-border: #ced4da;
            --select-placeholder: #999;
            --select-hover-bg: #f8f9fa;
            --select-focus-border: #86b7fe;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
            --primary-color: #0d6efd;
            --primary-rgb: 13, 110, 253;
            --text-light: #fff;
        }
        
        /* Existing styles */
        .form-label.fw-bold {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            border-radius: 8px;
            height: calc(2.5rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding-right: 0;
            color: var(--text-color);
            line-height: 1.5;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: 38px;
        }
        
        .select2-dropdown-rtl {
            text-align: right;
        }
        
        .select2-dropdown {
            border-color: var(--border-color);
            background-color: var(--bg-card);
        }
        
        .select2-results__option {
            padding: 0.5rem 0.75rem;
            transition: background-color 0.15s ease-in-out;
        }
        
        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--primary-color);
            color: white;
        }
        
        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: 1rem;
        }

        /* Detail item styles */
        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            white-space: nowrap;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* Row spacing */
        .row.g-3 {
            --bs-gutter-y: 1rem;
        }

        /* Flex container for details */
        .d-flex.align-items-center {
            min-height: 40px;
            padding: 0.5rem 0;
        }

        /* Remove borders and backgrounds */
        .card-body .row.g-3 > div {
            background: transparent;
            border: none;
        }

        /* Ensure proper spacing between label and value */
        .detail-value.ms-2 {
            margin-right: 0.25rem;
        }
        
        /* Retention Period Calendar */
        .retention-period-container {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .calendar-section {
            flex: 0 0 auto;
        }

        .calendar-wrapper {
            --calendar-day-size: 40px; /* Increased size */
            width: calc(var(--calendar-day-size) * 7 + 16px);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem;
            background-color: var(--bg-light);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .calendar-day-header {
            width: var(--calendar-day-size);
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .calendar-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1px;
        }

        .calendar-day {
            width: var(--calendar-day-size);
            height: var(--calendar-day-size);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.25rem;
            font-weight: 500;
            cursor: pointer;
            position: relative;
            user-select: none;
            transition: background-color 0.2s ease, color 0.2s ease;
        }

        .calendar-day:not(.out-of-period):hover {
            background-color: var(--primary-light);
            color: var(--primary);
            z-index: 1;
        }

        .calendar-day.selected {
            background-color: var(--primary);
            color: white;
            z-index: 2;
        }

        .calendar-day.in-range {
            background-color: var(--primary-light);
            color: var(--text-primary);
            position: relative;
        }

        .calendar-day.in-range::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--primary-light);
            z-index: -1;
        }

        .calendar-day.in-range:hover::before {
            background-color: var(--primary-light);
        }

        .calendar-day.out-of-period {
            color: var(--text-muted);
            cursor: not-allowed;
            opacity: 0.5;
        }

        .calendar-day.disabled {
            cursor: not-allowed;
            color: var(--text-muted);
            background-color: var(--bg-light);
            position: relative;
        }

        .calendar-day.disabled::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            border-top: 1px solid rgba(var(--bs-danger-rgb), 0.5);
            transform: rotate(-45deg);
        }

        [data-theme="dark"] .calendar-day.disabled::after {
            border-top: 1px solid rgba(255, 100, 100, 0.5);
        }

        .calendar-day.disabled:hover {
            background-color: var(--bg-light);
            color: var(--text-muted);
        }

        .calendar-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0 0.25rem;
        }

        .date-fields-section {
            flex: 1;
            min-width: 250px;
        }

        .retention-date-input {
            display: block;
            width: 100%;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            background-color: var(--bg-light);
            background-clip: padding-box;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .retention-date-input[readonly] {
            background-color: var(--bg-light);
            cursor: default;
        }

        .retention-date-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
        }

        [data-theme="dark"] .calendar-wrapper {
            border-color: var(--border-dark);
            background-color: var(--bg-dark);
        }
        
        /* Achievement button styles */
        .achievement-btn-container {
            display: flex;
            justify-content: flex-start;
            margin-top: 1rem;
        }
        
        .achievement-create-btn {
            padding: 0.5rem 1.5rem;
            font-weight: 600;
        }
        
        /* Achievement Table Styles */
        #achievement-tasks-table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }
        
        #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            text-align: center;
            padding: 0.75rem;
            vertical-align: middle;
            border-bottom: 2px solid var(--border-color);
            white-space: nowrap;
        }
        
        #achievement-tasks-table tbody td {
            padding: 0.75rem;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }
        
        #achievement-tasks-table tbody tr:last-child td {
            border-bottom: none;
        }
        
        #achievement-tasks-table tbody tr:nth-child(even) td {
            background-color: var(--bg-light);
        }
        
        #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }
        
        #achievement-tasks-table .form-control {
            padding: 0.375rem 0.75rem;
            font-size: 0.95rem;
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
            transition: all 0.2s ease-in-out;
            text-align: center;
        }
        
        #achievement-tasks-table .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
        }
        
        #achievement-tasks-table .current-progress {
            min-width: 80px;
            margin: 0 auto;
        }
        
        #achievement-tasks-table .task-notes {
            min-width: 150px;
        }
        
        #achievement-tasks-table thead th:not(:last-child) {
            border-right: 1px solid var(--border-color);
        }
        
        #achievement-tasks-table td:first-child {
            text-align: right;
        }
        
        #achievement-tasks-table td:nth-child(2),
        #achievement-tasks-table td:nth-child(3),
        #achievement-tasks-table td:nth-child(4) {
            width: 150px;
        }
        
        #achievement-tasks-table td:last-child {
            width: 200px;
        }
        
        .actual-workdays-container {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: var(--bg-light);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }
        
        .input-help-text {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }
        
        .required-asterisk {
            color: var(--bs-danger);
            margin-right: 0.25rem;
        }
        
        /* JSON Display Styles */
        #json_display {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            background-color: var(--bg-light);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 0.75rem;
            resize: vertical;
        }
        
        /* Dark mode styles */
        [data-theme="dark"] #achievement-tasks-table {
            border-color: var(--border-dark);
        }
        
        [data-theme="dark"] #achievement-tasks-table thead th {
            background-color: var(--bg-dark-secondary);
            border-color: var(--border-dark);
        }
        
        [data-theme="dark"] #achievement-tasks-table tbody td {
            border-color: var(--border-dark);
        }
        
        [data-theme="dark"] #achievement-tasks-table tbody tr:nth-child(even) td {
            background-color: var(--bg-dark-hover);
        }
        
        [data-theme="dark"] #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-dark-hover);
        }
        
        [data-theme="dark"] #achievement-tasks-table .form-control {
            background-color: var(--bg-dark);
            border-color: var(--border-dark);
            color: var(--text-light);
        }
        
        [data-theme="dark"] #achievement-tasks-table .form-control:focus {
            border-color: var(--primary);
        }
        
        [data-theme="dark"] .actual-workdays-container {
            background-color: var(--bg-dark-secondary);
            border-color: var(--border-dark);
        }
        
        [data-theme="dark"] #json_display {
            background-color: var(--bg-dark);
            color: var(--text-light);
            border-color: var(--border-dark);
        }
        
        /* Data Summary Table */
        .data-summary-table {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .data-summary-table th,
        .data-summary-table td {
            padding: 0.625rem;
            text-align: center;
            border: 1px solid var(--border-color);
        }
        
        .data-summary-table th {
            background-color: var(--bg-secondary);
            font-weight: 600;
        }
        
        [data-theme="dark"] .data-summary-table {
            border-color: var(--border-dark);
        }
        
        [data-theme="dark"] .data-summary-table th,
        [data-theme="dark"] .data-summary-table td {
            border-color: var(--border-dark);
        }
        
        [data-theme="dark"] .data-summary-table th {
            background-color: var(--bg-dark-secondary);
        }
        
        /* File upload styles */
        .file-upload-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .file-upload-input {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            border: 0;
        }
        
        .file-upload-label {
            display: inline-block;
            padding: 0.375rem 0.75rem;
            border-radius: 0.25rem;
            background-color: var(--bs-light);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }
        
        .file-upload-label:hover {
            background-color: var(--bs-light-hover);
        }
        
        .file-name-display {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .calendar-day.out-of-period {
            opacity: 0.4;
            cursor: default;
            color: var(--bs-gray-600);
        }
        
        /* Style for dates with existing achievement reports */
        .calendar-day.has-report {
            background-color: rgba(220, 53, 69, 0.2); /* Light red background */
            color: var(--bs-danger);
            position: relative;
            cursor: not-allowed;
        }
        
        /* Add a cross symbol to indicate the date is not available */
        .calendar-day.has-report::after {
            content: "✕";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: var(--bs-danger);
            opacity: 0.7;
        }
        
        /* Dark theme support */
        [data-theme="dark"] .calendar-day.has-report {
            background-color: rgba(220, 53, 69, 0.3);
            color: #ff6b6b;
        }
        
        [data-theme="dark"] .calendar-day.has-report::after {
            color: #ff6b6b;
        }
        
        .calendar-day.disabled {
        </style>

    <!-- CSS Styles to add for the detail-item UI pattern -->
    <style>
        .detail-item {
            padding: 0.75rem;
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
            display: block;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
            font-size: 1rem;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Main Container -->
                    <div class="search-container">
                        <!-- Quick Filters -->
                        <div class="quick-filters">
                            <form method="post" id="searchForm" class="mb-0">
                                <div class="d-flex flex-column gap-3">
                                    <!-- Task Executor Type Selection -->
                                    <div class="filter-group mb-3">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-person-badge"></i> نوع منفذ المهمة
                                        </h6>
                                        <div class="d-flex gap-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="executor_type" 
                                                       id="both_type" value="" checked>
                                                <label class="form-check-label" for="both_type">
                                                    الكل
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="executor_type" 
                                                       id="employee_type" value="employee">
                                                <label class="form-check-label" for="employee_type">
                                                    موظف
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="executor_type" 
                                                       id="delegate_type" value="delegate">
                                                <label class="form-check-label" for="delegate_type">
                                                    مندوب
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Add Project Selection Field -->
                                    <div class="filter-group mb-3">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-building"></i> المشروع
                                        </h6>
                                        <div class="form-group">
                                            <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                            <select class="form-select select2-search" id="project_id" name="project_id" required>
                                                <option value="">اختر المشروع</option>
                                                <?php foreach ($projects as $project): ?>
                                                    <option value="<?= $project['id_Project'] ?>" <?= isset($_POST['project_id']) && $_POST['project_id'] == $project['id_Project'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($project['Project_name']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Search Input -->
                                    <div class="filter-group mb-0">
                                        <h6 class="filter-group-title">
                                            <i class="bi bi-search"></i> بحث عن مهمة
                                        </h6>
                                        <div class="d-flex gap-2 flex-wrap">
                                            <div class="flex-grow-1">
                                                <input type="text" class="form-control form-control-sm" 
                                                       id="name_ar_contract" 
                                                       name="name_ar_contract" 
                                                       placeholder="البحث (اسم المهمة أو رقم المهمة)">
                                            </div>
                                            <div>
                                                <button type="submit" name="search" class="btn btn-primary btn-sm">
                                                    <i class="bi bi-search"></i> بحث
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Results Section -->
                        <?php if (isset($search_results) && !empty($search_results)): ?>
                        <div class="results-container">
                            <div class="results-header">
                                <div class="results-search">
                                    <input type="text" id="resultSearch" placeholder="البحث في النتائج..." class="form-control">
                                </div>
                            </div>
                            <div class="results-list">
                                <?php foreach ($search_results as $task): ?>
                                    <div class="result-item" data-search-text="<?= htmlspecialchars($task['name_TASKS']) ?> <?= htmlspecialchars($task['id_TASKS']) ?>">
                                        <div class="result-item-details">
                                            <div class="result-item-name">
                                                <i class="bi bi-list-task text-primary me-2"></i>
                                                <span class="badge bg-secondary me-1">مهمة #<?= htmlspecialchars($task['id_TASKS']) ?></span>
                                                <?= htmlspecialchars($task['name_TASKS']) ?>
                                            </div>
                                            <div class="result-item-info small text-muted mt-1">
                                                <span class="me-3">
                                                    <i class="bi bi-person"></i>
                                                    <?php
                                                    if (!empty($task['id_employees'])) {
                                                        echo htmlspecialchars($task['employee_name_ar']);
                                                    } elseif (!empty($task['id_assigned'])) {
                                                        echo htmlspecialchars($task['delegate_name_ar']);
                                                    } else {
                                                        echo "غير محدد";
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?= !empty($task['id_employees']) ? 'primary' : 'info' ?> ms-1">
                                                        <?= !empty($task['id_employees']) ? 'موظف' : 'مندوب' ?>
                                                    </span>
                                                </span>
                                                <span class="me-3">
                                                    <i class="bi bi-calendar-event"></i>
                                                    <?= date('Y-m-d', strtotime($task['start_date_TASKS'])) ?>
                                                </span>
                                                <span>
                                                    <i class="bi bi-calendar-check"></i>
                                                    <?= date('Y-m-d', strtotime($task['end_date_TASKS'])) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <form method="post" style="display: inline;">
                                            <input type="hidden" name="search" value="1">
                                            <input type="hidden" name="executor_type" value="<?= $_POST['executor_type'] ?>">
                                            <input type="hidden" name="selected_task" value="<?= $task['id_TASKS'] ?>">
                                            <input type="hidden" name="project_id" value="<?= $_POST['project_id'] ?>">
                                            <button type="submit" class="select-contract-btn">
                                                <i class="bi bi-eye me-1"></i>عرض
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if (isset($_POST['selected_task']) && $task_data): ?>
                    <div class="row">
                        <!-- Task Details - Full Width -->
                        <div class="col-12">
                            <!-- Report Container -->
                            <div class="report-container">
                                <div class="report-header">
                                    <h5 class="card-title mb-4">تفاصيل المهمة</h5>
                                </div>

                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">رقم المهمة</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['id_TASKS']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">اسم المهمة بالعربية</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['name_TASKS']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">تاريخ البدء</label>
                                            <span class="detail-value">
                                                <?php echo date('Y-m-d', strtotime($task_data['start_date_TASKS'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">تاريخ الانتهاء</label>
                                            <span class="detail-value">
                                                <?php echo date('Y-m-d', strtotime($task_data['end_date_TASKS'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">الأجر</label>
                                            <span class="detail-value">
                                                <?php echo number_format($task_data['wage_TASKS'], 2) . ' ' . 
                                                    ($task_data['Type_currency'] == 1 ? 'دولار' : 'ريال'); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">مكان العمل</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['workplace']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">المشروع المرتبط</label>
                                            <span class="detail-value">
                                                <?php 
                                                    if (!empty($task_data['id_Project'])) {
                                                        echo htmlspecialchars($task_data['id_Project']);
                                                    } else {
                                                        echo 'غير محدد';
                                                    }
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <!-- Added Person Assigned Information -->
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">اسم منفذ المهمة</label>
                                            <span class="detail-value">
                                                <?php 
                                                    if (!empty($task_data['id_employees'])) {
                                                        echo htmlspecialchars($task_data['name_ar_contract']);
                                                    } elseif (!empty($task_data['id_assigned'])) {
                                                        echo htmlspecialchars($task_data['name_ar_assigned']);
                                                    } else {
                                                        echo 'غير محدد';
                                                    }
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">النوع</label>
                                            <span class="detail-value">
                                                <?php 
                                                    if (!empty($task_data['id_employees'])) {
                                                        echo 'موظف';
                                                    } elseif (!empty($task_data['id_assigned'])) {
                                                        echo 'مندوب';
                                                    } else {
                                                        echo 'غير محدد';
                                                    }
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="detail-item">
                                            <label class="detail-label">رقم منفذ المهمة</label>
                                            <span class="detail-value">
                                                <?php 
                                                    if (!empty($task_data['id_employees'])) {
                                                        echo htmlspecialchars($task_data['id_employees']);
                                                    } elseif (!empty($task_data['id_assigned'])) {
                                                        echo htmlspecialchars($task_data['id_assigned']);
                                                    } else {
                                                        echo 'غير محدد';
                                                    }
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <?php if (!empty($task_data['note'])): ?>
                                    <div class="col-md-12">
                                        <div class="detail-item">
                                            <label class="detail-label">ملاحظات</label>
                                            <span class="detail-value">
                                                <?php echo htmlspecialchars($task_data['note']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Previous Achievement Reports Section -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title mb-4">تقارير الإنجاز السابقة للمهمة المحددة</h5>
                                    
                                    <?php if (isset($previous_achievement_reports) && !empty($previous_achievement_reports)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>رقم التقرير</th>
                                                    <th>تاريخ البداية</th>
                                                    <th>تاريخ النهاية</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($previous_achievement_reports as $report): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($report['id_achievement_reports_TASKS']) ?></td>
                                                    <td><?= date('Y-m-d', strtotime($report['start_date_achievement_reports'])) ?></td>
                                                    <td><?= date('Y-m-d', strtotime($report['end_date_achievement_reports'])) ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <?php else: ?>
                                    <div class="alert alert-info">
                                        لا توجد تقارير إنجاز سابقة لهذه المهمة
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Define Achievement Period Section -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card mb-4" id="achievement-period-section">
                                <div class="card-body">
                                    <h5 class="card-title mb-4">تحديد فترة الإنجاز</h5>
                                    
                                    <div class="retention-period-container d-flex">
                                        <div class="calendar-section ms-4">
                                            <div class="calendar-wrapper">
                                                <div class="calendar-navigation">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary prev-month">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                    <span class="current-month"></span>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary next-month">
                                                        <i class="fas fa-chevron-left"></i>
                                                    </button>
                                                </div>
                                                <div class="calendar-header">
                                                    <div class="calendar-day-header">Sun</div>
                                                    <div class="calendar-day-header">Mon</div>
                                                    <div class="calendar-day-header">Tue</div>
                                                    <div class="calendar-day-header">Wed</div>
                                                    <div class="calendar-day-header">Thu</div>
                                                    <div class="calendar-day-header">Fri</div>
                                                    <div class="calendar-day-header">Sat</div>
                                                </div>
                                                <div class="calendar-container"></div>
                                            </div>
                                        </div>

                                        <div class="date-fields-section flex-grow-1">
                                            <div class="mb-3">
                                                <label for="retention_start_date" class="form-label">تاريخ البداية</label>
                                                <input type="text" 
                                                       class="retention-date-input" 
                                                       id="retention_start_date" 
                                                       readonly 
                                                       placeholder="Select from calendar">
                                            </div>
                                            <div class="mb-3">
                                                <label for="retention_end_date" class="form-label">تاريخ النهاية</label>
                                                <input type="text" 
                                                       class="retention-date-input" 
                                                       id="retention_end_date" 
                                                       readonly 
                                                       placeholder="Select from calendar">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">عدد أيام الفترة</label>
                                                <input type="text" 
                                                       class="retention-date-input" 
                                                       id="period_days" 
                                                       readonly 
                                                       value="-">
                                            </div>
                                            <div class="achievement-btn-container">
                                                <button type="button" 
                                                        class="btn btn-primary achievement-create-btn" 
                                                        id="create-report-btn" 
                                                        disabled>
                                                    إنشاء تقرير الإنجاز
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Create Achievement Report Section -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card mb-4" id="create-achievement-section" style="display: none;">
                                <div class="card-body">
                                    <h5 class="card-title mb-4">إنشاء تقرير الإنجاز</h5>
                                    <div id="achievement-form-container">
                                        <div class="table-responsive">
                                            <table class="table" id="achievement-tasks-table">
                                                <thead>
                                                    <!-- Will be populated dynamically -->
                                                </thead>
                                                <tbody>
                                                    <!-- Will be populated with tasks -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <div class="mt-4">
                                            <h6 class="mb-3 pb-2 border-bottom border-primary">ملخص البيانات</h6>
                                            <div class="table-responsive">
                                                <table class="table data-summary-table">
                                                    <thead>
                                                        <tr>
                                                            <th>رقم المهمة</th>
                                                            <th>رقم منفذ المهمة</th>
                                                            <th>المشروع</th>
                                                            <th>تاريخ البداية</th>
                                                            <th>تاريخ النهاية</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr id="saved-data-row">
                                                            <td id="task-number">-</td>
                                                            <td id="assignee-number">-</td>
                                                            <td id="project-number">-</td>
                                                            <td id="start-date">-</td>
                                                            <td id="end-date">-</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        
                                        <!-- Add Save Achievement Report Button -->
                                        <div class="mt-4 text-center">
                                            <button type="button" id="save-achievement-btn" class="btn btn-primary btn-lg">
                                                <i class="bi bi-save"></i>
                                                حفظ تقرير الإنجاز
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Add the JSON Display Section -->
                    <div class="card mb-4" id="json-display-section" style="display: none;">
                        <div class="card-body">
                            <h5 class="card-title mb-4 d-flex align-items-center justify-content-between">
                                عرض كود JSON
                                <button class="btn btn-link p-0 text-decoration-none" type="button" data-bs-toggle="collapse" data-bs-target="#jsonDisplayCollapse" aria-expanded="false" aria-controls="jsonDisplayCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </h5>
                            <div class="collapse" id="jsonDisplayCollapse">
                                <textarea id="json_display" class="form-control" rows="10" readonly 
                                    style="direction: ltr; text-align: left; font-family: monospace;"></textarea>
                            </div>
                        </div>
                    </div>
                    <?php elseif (isset($search_results) && !empty($search_results)): ?>
                    <div class="text-center mt-4 text-muted">
                        <i class="bi bi-arrow-up-circle fs-1"></i>
                        <p class="mt-2">الرجاء اختيار مهمة من القائمة أعلاه لعرض التفاصيل</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for all select elements
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                language: {
                    noResults: function() {
                        return "لا توجد نتائج";
                    }
                }
            });

            // Search within results functionality
            $('#resultSearch').on('input', function() {
                const searchText = $(this).val().toLowerCase();
                $('.result-item').each(function() {
                    const itemText = $(this).data('search-text').toLowerCase();
                    $(this).toggle(itemText.includes(searchText));
                });
            });

            // Auto-scroll to contract details when a contract is selected
            <?php if (isset($_POST['selected_task']) && $contract_data): ?>
                $('html, body').animate({
                    scrollTop: $('.report-container').offset().top - 20
                }, 300);
            <?php endif; ?>

            // Initialize Select2 for project dropdown
            $('#project_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر المشروع',
                allowClear: true
            });

            // Form submission validation
            $('#searchForm').on('submit', function(e) {
                if (!$('#project_id').val()) {
                    e.preventDefault();
                    alert('يرجى اختيار المشروع قبل البحث');
                    return false;
                }
            });
        });
    </script>
    
    <script>
        // Add Font Awesome for calendar icons
        if (!document.getElementById('font-awesome')) {
            var fontAwesome = document.createElement('link');
            fontAwesome.id = 'font-awesome';
            fontAwesome.rel = 'stylesheet';
            fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
            document.head.appendChild(fontAwesome);
        }
        
        $(document).ready(function() {
            // Variables for date selection
            let selectedStartDate = null;
            let selectedEndDate = null;
            let selectionInProgress = false;
            
            // Task dates from PHP variables
            const taskStartDate = new Date('<?php echo isset($task_data) ? $task_data['start_date_TASKS'] : date("Y-m-d"); ?>');
            const taskEndDate = new Date('<?php echo isset($task_data) ? $task_data['end_date_TASKS'] : date("Y-m-d"); ?>');
            
            // Dates that already have achievement reports
            const existingReportDates = <?php echo isset($existing_report_dates_json) ? $existing_report_dates_json : '[]'; ?>;
            
            // Format date for display (YYYY-MM-DD)
            function formatDate(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }
            
            // Calendar functionality
            let currentMonth = new Date();
            currentMonth.setDate(1); // Set to first day of current month
            
            // Init to task start date month if task is selected
            if (<?php echo isset($task_data) ? 'true' : 'false'; ?>) {
                currentMonth = new Date(taskStartDate);
                currentMonth.setDate(1);
            }
            
            // Next month button
            $('.next-month').on('click', function() {
                currentMonth.setMonth(currentMonth.getMonth() + 1);
                renderCalendar();
            });
            
            // Previous month button
            $('.prev-month').on('click', function() {
                currentMonth.setMonth(currentMonth.getMonth() - 1);
                renderCalendar();
            });
            
            // Render calendar function
            function renderCalendar() {
                const calendarContainer = $('.calendar-container');
                
                // Update current month display
                const monthNames = ["January", "February", "March", "April", "May", "June",
                                   "July", "August", "September", "October", "November", "December"];
                $('.current-month').text(monthNames[currentMonth.getMonth()] + ' ' + currentMonth.getFullYear());
                
                // Calculate first day of month and last day
                const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
                const lastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
                
                // Get day of week for first day (0 = Sunday, 6 = Saturday)
                const firstDayIndex = firstDay.getDay();
                
                // Create calendar grid
                let calendarHtml = '';
                
                // Add empty cells for days before first day of month
                for (let i = 0; i < firstDayIndex; i++) {
                    calendarHtml += `<div class="calendar-day out-of-period"></div>`;
                }
                
                // Add days of the month
                for (let day = 1; day <= lastDay.getDate(); day++) {
                    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
                    date.setHours(0, 0, 0, 0);
                    
                    // Check if date is within the allowed range (between task start and end dates)
                    const isOutOfPeriod = date < taskStartDate || date > taskEndDate;
                    
                    // Determine CSS classes
                    let classes = ['calendar-day'];
                    
                    if (isOutOfPeriod) {
                        classes.push('out-of-period');
                    }
                    
                    // Check if date has an existing achievement report
                    const dateStr = formatDate(date);
                    const hasExistingReport = existingReportDates.includes(dateStr);
                    if (hasExistingReport) {
                        classes.push('has-report');
                    }
                    
                    // Check if date is selected or in selected range
                    if (selectedStartDate && selectedEndDate) {
                        if (date.getTime() === selectedStartDate.getTime() || 
                            date.getTime() === selectedEndDate.getTime()) {
                            classes.push('selected');
                        } else if (date > selectedStartDate && date < selectedEndDate) {
                            classes.push('in-range');
                        }
                    } else if (selectedStartDate && date.getTime() === selectedStartDate.getTime()) {
                        classes.push('selected');
                    }
                    
                    calendarHtml += `
                        <div class="${classes.join(' ')}" 
                             data-date="${formatDate(date)}">
                            ${day}
                        </div>`;
                }
                
                calendarContainer.html(calendarHtml);
                
                // Add event handlers to calendar days
                const calendarDays = $('.calendar-day:not(.out-of-period):not(.has-report)');
                
                // Handle mousedown (start selection)
                calendarDays.off('mousedown').on('mousedown', function(e) {
                    e.preventDefault(); // Prevent text selection
                    
                    // Get the date from the clicked day
                    const dateStr = $(this).data('date');
                    
                    // Check if this date already has a report
                    if (existingReportDates.includes(dateStr)) {
                        return; // Skip if the date already has a report
                    }
                    
                    selectedStartDate = new Date(dateStr);
                    selectedStartDate.setHours(0, 0, 0, 0);
                    
                    // Reset end date
                    selectedEndDate = null;
                    selectionInProgress = true;
                    
                    // Update calendar display
                    renderCalendar();
                });
                
                // Handle mouseover during selection
                calendarDays.off('mousemove').on('mousemove', function(e) {
                    if (!selectionInProgress || !selectedStartDate) return;
                    
                    // Get the date from the hovered day
                    const dateStr = $(this).data('date');
                    const currentDate = new Date(dateStr);
                    currentDate.setHours(0, 0, 0, 0);
                    
                    // Check if the current date to the selected start date range includes any dates with reports
                    let hasConflict = false;
                    const rangeStart = new Date(Math.min(selectedStartDate.getTime(), currentDate.getTime()));
                    const rangeEnd = new Date(Math.max(selectedStartDate.getTime(), currentDate.getTime()));
                    
                    for (let d = new Date(rangeStart); d <= rangeEnd; d.setDate(d.getDate() + 1)) {
                        const checkDateStr = formatDate(d);
                        if (existingReportDates.includes(checkDateStr)) {
                            hasConflict = true;
                            break;
                        }
                    }
                    
                    if (hasConflict) {
                        return; // Skip if there's a conflict
                    }
                    
                    // Set end date (ensure it's after start date)
                    if (currentDate >= selectedStartDate) {
                        selectedEndDate = currentDate;
                    } else {
                        // If dragging backwards, swap start and end
                        selectedEndDate = selectedStartDate;
                        selectedStartDate = currentDate;
                    }
                    
                    // Update calendar display
                    renderCalendar();
                });
                
                // Handle mouseup (end selection)
                $(document).off('mouseup.calendar').on('mouseup.calendar', function() {
                    if (!selectionInProgress) return;
                    
                    selectionInProgress = false;
                    
                    // If only start date is selected, set end date to same as start
                    if (selectedStartDate && !selectedEndDate) {
                        selectedEndDate = new Date(selectedStartDate);
                    }
                    
                    // Update the date inputs
                    updateSelectedPeriod();
                });
                
                // Handle mouse leaving calendar
                $('.calendar-wrapper').off('mouseleave').on('mouseleave', function() {
                    if (!selectionInProgress) return;
                    renderCalendar();
                });
                
                // Prevent text selection during drag
                $('.calendar-wrapper').off('selectstart').on('selectstart', function(e) {
                    e.preventDefault();
                    return false;
                });
            }
            
            // Update the selected period and enable button
            function updateSelectedPeriod() {
                if (selectedStartDate && selectedEndDate) {
                    const startInput = document.getElementById('retention_start_date');
                    const endInput = document.getElementById('retention_end_date');
                    const periodDaysInput = document.getElementById('period_days');
                    
                    startInput.value = formatDate(selectedStartDate);
                    endInput.value = formatDate(selectedEndDate);
                    
                    // Calculate the number of days between dates
                    const timeDiff = Math.abs(selectedEndDate.getTime() - selectedStartDate.getTime());
                    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // Adding 1 to include both start and end dates
                    periodDaysInput.value = daysDiff + ' يوم';
                    
                    // Enable the create report button
                    const createReportBtn = document.getElementById('create-report-btn');
                    if (createReportBtn) {
                        createReportBtn.disabled = false;
                    }
                }
            }
            
            // Initialize calendar if task data is available
            if (<?php echo isset($task_data) ? 'true' : 'false'; ?>) {
                renderCalendar();
            } else {
                // Hide the achievement period section if no task is selected
                $('#achievement-period-section').hide();
            }
            
            // Create Achievement Report button click handler
            $('#create-report-btn').on('click', function() {
                if (selectedStartDate && selectedEndDate) {
                    // Show the achievement section
                    $('#create-achievement-section').show();
                    $('#json-display-section').show();
                    
                    // Populate the achievement form with task data
                    setupAchievementTable();
                    
                    // Smooth scroll to the achievement section
                    $('html, body').animate({
                        scrollTop: $('#create-achievement-section').offset().top - 20
                    }, 300);
                    
                    // Update the summary table
                    updateSummaryTable();
                }
            });
            
            // Function to set up the achievement table
            function setupAchievementTable() {
                const thead = document.querySelector('#achievement-tasks-table thead');
                const tbody = document.querySelector('#achievement-tasks-table tbody');
                
                // Clear existing content
                thead.innerHTML = '';
                tbody.innerHTML = '';
                
                // Get task data
                const taskData = <?php echo isset($task_data) ? json_encode($task_data) : 'null'; ?>;
                
                if (!taskData) {
                    console.error('No task data available');
                    return;
                }
                
                console.log('Task data:', taskData);
                
                let todoList;
                
                try {
                    // Check if data_todo_list_TASKS is a string or already an object
                    if (typeof taskData.data_todo_list_TASKS === 'string') {
                        todoList = JSON.parse(taskData.data_todo_list_TASKS);
                        console.log('Parsed JSON from string:', todoList);
                    } else if (typeof taskData.data_todo_list_TASKS === 'object') {
                        todoList = taskData.data_todo_list_TASKS;
                        console.log('Using task data object directly:', todoList);
                    } else {
                        throw new Error('Invalid task data format');
                    }
                    
                    // Initialize the todoList structure if it doesn't exist
                    if (!todoList.evaluation) {
                        todoList.evaluation = {
                            workDaysEvaluation: "yes",
                            percentageEvaluation: "-"
                        };
                    }
                    
                    if (!todoList.taskDetails) {
                        todoList.taskDetails = {
                            subtasks: []
                        };
                    }
                    
                    // Store the todoList for later use
                    window.currentTodoList = todoList;
                    
                    // Update JSON display
                    const jsonDisplay = document.getElementById('json_display');
                    jsonDisplay.value = JSON.stringify(todoList, null, 2);
                } catch (e) {
                    console.error('Error parsing todo list data:', e);
                    console.error('Raw data:', taskData.data_todo_list_TASKS);
                    todoList = {
                        evaluation: {
                            workDaysEvaluation: "yes",
                            percentageEvaluation: "-"
                        },
                        taskDetails: {
                            subtasks: []
                        }
                    };
                    window.currentTodoList = todoList;
                    
                    // Update JSON display with the default structure
                    const jsonDisplay = document.getElementById('json_display');
                    jsonDisplay.value = JSON.stringify(todoList, null, 2);
                }
                
                // Remove any existing actual workdays container
                const existingContainer = document.querySelector('.actual-workdays-container');
                if (existingContainer) {
                    existingContainer.remove();
                }
                
                // Get total period days
                const periodDaysInput = document.getElementById('period_days');
                const totalPeriodDays = parseInt(periodDaysInput.value.split(' ')[0]) || 0;
                
                // Create header for the table
                const headerRow = document.createElement('tr');
                headerRow.innerHTML = `
                    <th>اسم المهمة</th>
                    <th>أيام العمل المنجزة حتى الآن</th>
                    <th>أيام العمل للفترة المحددة</th>
                    <th>إجمالي أيام العمل</th>
                    <th>ملاحظات</th>
                `;
                thead.appendChild(headerRow);
                
                // Check for subtasks in the correct path
                let subtasks = [];
                
                if (todoList.taskDetails && todoList.taskDetails.subtasks && Array.isArray(todoList.taskDetails.subtasks)) {
                    subtasks = todoList.taskDetails.subtasks;
                    console.log('Found subtasks:', subtasks);
                } else {
                    console.warn('No subtasks found in regular path, checking alternative paths');
                    
                    // Check alternative paths in the structure
                    if (todoList.taskDetails && todoList.taskDetails.tasks && Array.isArray(todoList.taskDetails.tasks)) {
                        subtasks = todoList.taskDetails.tasks;
                        console.log('Found tasks instead of subtasks:', subtasks);
                    }
                }
                
                // If no subtasks found, create a default one from the task name
                if (subtasks.length === 0) {
                    console.warn('No subtasks found, creating a default subtask');
                    const defaultSubtask = {
                        subtaskName: taskData.name_TASKS || 'مهمة افتراضية',
                        completionRate: 0,
                        total: 0
                    };
                    subtasks.push(defaultSubtask);
                    
                    // Update the todoList structure
                    if (!todoList.taskDetails) {
                        todoList.taskDetails = {};
                    }
                    todoList.taskDetails.subtasks = subtasks;
                    window.currentTodoList = todoList;
                    
                    // Update JSON display
                    const jsonDisplay = document.getElementById('json_display');
                    jsonDisplay.value = JSON.stringify(todoList, null, 2);
                }
                
                // Ensure all subtasks have both completionRate and total properties
                subtasks.forEach(subtask => {
                    if (typeof subtask.total === 'undefined') {
                        subtask.total = subtask.completionRate || 0; // Initialize total from completionRate if it exists
                    }
                });
                
                // Add subtasks to the table
                subtasks.forEach((subtask, index) => {
                    // Set the initial values
                    const progressSoFar = subtask.total || 0;
                    subtask.completionRate = totalPeriodDays; // Set current period days to completionRate
                    const subtaskName = subtask.subtaskName || subtask.taskName || 'مهمة فرعية';
                    
                    // Calculate new total
                    const newTotal = parseInt(progressSoFar) + parseInt(totalPeriodDays);
                    
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${subtaskName}</td>
                        <td class="text-center previous-progress">
                            ${progressSoFar}
                        </td>
                        <td class="text-center">
                            <input type="number" 
                                class="form-control current-progress" 
                                data-subtask-index="${index}"
                                min="${totalPeriodDays}" 
                                max="${totalPeriodDays}"
                                value="${totalPeriodDays}"
                                readonly
                                style="width: 100px; margin: 0 auto; background-color: #f8f9fa;">
                            <div class="input-help-text">القيمة الثابتة: ${totalPeriodDays} يوم</div>
                        </td>
                        <td class="text-center total-progress">
                            ${newTotal}
                        </td>
                        <td class="text-center">
                            <input type="text" 
                                class="form-control task-notes" 
                                data-subtask-index="${index}"
                                value="-"
                                placeholder="أدخل ملاحظاتك هنا">
                        </td>
                    `;
                    tbody.appendChild(row);
                });
                
                // Add event listeners for progress inputs
                document.querySelectorAll('.current-progress').forEach(input => {
                    // Set the total in the todoList when the page loads
                    const subtaskIndex = parseInt(input.dataset.subtaskIndex);
                    const currentPeriodDays = parseInt(input.value) || 0;
                    const previousProgress = parseInt(input.parentElement.previousElementSibling.textContent.trim()) || 0;
                    const total = previousProgress + currentPeriodDays;
                    
                    // Update the todoList
                    if (window.currentTodoList && window.currentTodoList.taskDetails) {
                        if (window.currentTodoList.taskDetails.subtasks && Array.isArray(window.currentTodoList.taskDetails.subtasks)) {
                            if (window.currentTodoList.taskDetails.subtasks[subtaskIndex]) {
                                window.currentTodoList.taskDetails.subtasks[subtaskIndex].completionRate = currentPeriodDays;
                                window.currentTodoList.taskDetails.subtasks[subtaskIndex].total = total;
                                console.log('Set initial subtask values:', subtaskIndex, 'completionRate:', currentPeriodDays, 'total:', total);
                            }
                        }
                        
                        // Update JSON display
                        updateJsonDisplay();
                    }
                });
                
                // Add event listeners for notes inputs
                document.querySelectorAll('.task-notes').forEach(input => {
                    input.addEventListener('input', function() {
                        const subtaskIndex = parseInt(this.dataset.subtaskIndex);
                        
                        // Update the todoList
                        if (window.currentTodoList && window.currentTodoList.taskDetails) {
                            if (window.currentTodoList.taskDetails.subtasks && Array.isArray(window.currentTodoList.taskDetails.subtasks)) {
                                if (window.currentTodoList.taskDetails.subtasks[subtaskIndex]) {
                                    window.currentTodoList.taskDetails.subtasks[subtaskIndex].notes = this.value;
                                    console.log('Updated subtask notes:', subtaskIndex, this.value);
                                }
                            }
                            
                            // Update JSON display
                            updateJsonDisplay();
                        }
                    });
                });
                
                // Add event listeners for file uploads
                document.querySelectorAll('.file-upload-input').forEach(input => {
                    input.addEventListener('change', function() {
                        const subtaskIndex = parseInt(this.dataset.subtaskIndex);
                        const fileNameDisplay = document.getElementById(`file-name-${subtaskIndex}`);
                        
                        if (this.files && this.files[0]) {
                            const fileName = this.files[0].name;
                            fileNameDisplay.textContent = fileName;
                            
                            // Update the todoList
                            if (window.currentTodoList && window.currentTodoList.taskDetails) {
                                if (window.currentTodoList.taskDetails.subtasks && Array.isArray(window.currentTodoList.taskDetails.subtasks)) {
                                    if (window.currentTodoList.taskDetails.subtasks[subtaskIndex]) {
                                        window.currentTodoList.taskDetails.subtasks[subtaskIndex].attachment = fileName;
                                        console.log('Updated subtask attachment:', subtaskIndex, fileName);
                                    }
                                }
                                
                                // Update JSON display
                                updateJsonDisplay();
                            }
                        } else {
                            fileNameDisplay.textContent = 'لا يوجد ملف';
                            
                            // Remove attachment from todoList
                            if (window.currentTodoList && window.currentTodoList.taskDetails) {
                                if (window.currentTodoList.taskDetails.subtasks && Array.isArray(window.currentTodoList.taskDetails.subtasks)) {
                                    if (window.currentTodoList.taskDetails.subtasks[subtaskIndex]) {
                                        delete window.currentTodoList.taskDetails.subtasks[subtaskIndex].attachment;
                                        console.log('Removed subtask attachment:', subtaskIndex);
                                    }
                                }
                                
                                // Update JSON display
                                updateJsonDisplay();
                            }
                        }
                    });
                });
            }
            
            // Function to update the summary table
            function updateSummaryTable() {
                const taskData = <?php echo isset($task_data) ? json_encode($task_data) : 'null'; ?>;
                if (!taskData) return;
                
                // Update the summary table
                document.getElementById('task-number').textContent = taskData.id_TASKS || '-';
                document.getElementById('assignee-number').textContent = taskData.id_employees || taskData.id_assigned || '-';
                document.getElementById('project-number').textContent = taskData.id_Project || '-';
                document.getElementById('start-date').textContent = selectedStartDate ? formatDate(selectedStartDate) : '-';
                document.getElementById('end-date').textContent = selectedEndDate ? formatDate(selectedEndDate) : '-';
            }
            
            // Function to update the JSON display
            function updateJsonDisplay() {
                const jsonDisplay = document.getElementById('json_display');
                jsonDisplay.value = JSON.stringify(window.currentTodoList, null, 2);
            }
            
            // Function to show input warnings
            function showInputWarning(inputElement, message) {
                // Create a tooltip or use an alert for simplicity
                alert(message);
            }
            
            // Handle save achievement report button click
            document.getElementById('save-achievement-btn').addEventListener('click', function() {
                // Get all the necessary data
                const taskData = <?php echo isset($task_data) ? json_encode($task_data) : 'null'; ?>;
                if (!taskData) {
                    alert('لم يتم العثور على بيانات المهمة');
                    return;
                }
                
                const projectId = taskData.id_Project || '';
                const taskId = taskData.id_TASKS || '';
                const id_employees = taskData.id_employees || '';
                const id_assigned = taskData.id_assigned || '';
                const startDate = document.getElementById('start-date').textContent;
                const endDate = document.getElementById('end-date').textContent;
                const todoListData = document.getElementById('json_display').value;

                // Validate data
                if (projectId === '' || taskId === '' || 
                    startDate === '-' || endDate === '-' || !todoListData) {
                    alert('يرجى التأكد من اكتمال جميع البيانات المطلوبة');
                    return;
                }

                // Function to convert date format if needed
                function formatDateForMySQL(dateStr) {
                    // Check if already in YYYY-MM-DD format
                    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                        return dateStr;
                    }
                    
                    // Handle MM/DD/YYYY format
                    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
                        const [month, day, year] = dateStr.split('/');
                        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                    }
                    
                    return dateStr;
                }

                // Prepare the data for submission
                const formData = new FormData();
                formData.append('id_Project', projectId);
                formData.append('id_TASKS', taskId);
                formData.append('id_employees', id_employees);
                formData.append('id_assigned', id_assigned);
                formData.append('start_date_achievement_reports', formatDateForMySQL(startDate));
                formData.append('end_date_achievement_reports', formatDateForMySQL(endDate));
                formData.append('data_todo_list_achievement', todoListData);

                // Send the data to the server
                fetch('ajax/save_achievement_report_tasks.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم حفظ تقرير إنجاز المهمة بنجاح');
                        // Reset the page or refresh
                        window.location.reload();
                    } else {
                        alert('حدث خطأ أثناء حفظ التقرير: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ أثناء الاتصال بالخادم: ' + error);
                });
            });
        });
    </script>
</body>
</html>
