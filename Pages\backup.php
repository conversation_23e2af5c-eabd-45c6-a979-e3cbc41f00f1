<?php
// Database connection parameters for the database
$host = 'localhost';
$dbname = 'mydb'; // فقط هذه القاعدة سيتم التعامل معها
$username = 'root';
$password = 'root';

try {
    // Connect to the database using PDO
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Initialize variable to hold the SQL dump
    $sqlDump = "";

    // Function to generate SQL dump for a database
    function generateSqlDump($pdo, $dbname) {
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $sqlDump = "";

        foreach ($tables as $table) {
            // Get table structure information
            $columns = $pdo->query("SHOW COLUMNS FROM `$table`")->fetchAll(PDO::FETCH_ASSOC);
            $columnTypes = array();
            foreach ($columns as $column) {
                $columnTypes[$column['Field']] = strtolower($column['Type']);
            }

            // Get all the data from the current table
            $tableData = $pdo->query("SELECT * FROM $table")->fetchAll(PDO::FETCH_ASSOC);

            if (count($tableData) > 0) {
                $columnNames = array_keys($tableData[0]);

                // Create INSERT INTO statements for the table data
                foreach ($tableData as $row) {
                    $sqlDump .= "INSERT INTO `$dbname`.`$table` (`" . implode('`, `', $columnNames) . "`) VALUES (";
                    $values = [];
                    foreach ($row as $column => $value) {
                        if ($value === null || $value === "") {
                            $values[] = "NULL";
                        } else {
                            // Check if the column is a BLOB type
                            $isBlob = strpos($columnTypes[$column], 'blob') !== false;
                            if ($isBlob) {
                                // Base64 encode BLOB data
                                $values[] = "FROM_BASE64('" . base64_encode($value) . "')";
                            } else {
                                $values[] = $pdo->quote($value);
                            }
                        }
                    }
                    $sqlDump .= implode(", ", $values) . ");\n";
                }
                $sqlDump .= "\n";
            }
        }

        return $sqlDump;
    }

    // Generate SQL dump for the specified database only
    $sqlDump .= generateSqlDump($pdo, $dbname);

    // Function to replace empty string ('') with NULL in SQL dump
    function replaceEmptyStringsWithNull($sqlDump) {
        $lines = explode("\n", $sqlDump);
        $processedLines = [];

        foreach ($lines as $line) {
            // Replace '' with NULL
            $processedLines[] = str_replace("''", "NULL", $line);
        }

        return implode("\n", $processedLines);
    }

    // Process the SQL dump to replace empty strings with NULL
    $sqlDump = replaceEmptyStringsWithNull($sqlDump);

    // Get the current date
    $currentDate = date('Y-m-d');

    // Set the filename with the specified format
    $filename = 'نسخه_احتياطيه_' . $currentDate . '.sql';

    // Output the SQL dump as a downloadable file
    header('Content-Type: application/sql');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($sqlDump));

    echo $sqlDump;
    exit();

} catch (PDOException $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء نسخة احتياطية</title>
    <link href='../assets/css/cairo-font.css' rel='stylesheet'>
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #1976D2;
            --success-color: #4CAF50;
            --danger-color: #f44336;
            --background-color: #f5f5f5;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--background-color);
            direction: rtl;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header {
            margin-bottom: 2rem;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .message-box {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }

        .message-box.error {
            background-color: #FFEBEE;
            color: var(--danger-color);
        }

        .message-box.success {
            background-color: #E8F5E9;
            color: var(--success-color);
        }

        .loading {
            display: none;
            margin: 2rem 0;
            text-align: center;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            color: var(--primary-color);
            text-decoration: none;
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            margin-top: 1.5rem;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>إنشاء نسخة احتياطية</h1>
            <?php if (isset($error_message)): ?>
                <div class="message-box error">
                    <p><?php echo $error_message; ?></p>
                </div>
            <?php endif; ?>
        </div>

        <div class="loading" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <p>جاري إنشاء النسخة الاحتياطية...</p>
        </div>

        <a href="xxx.php" class="back-button">العودة للصفحة الرئيسية</a>
    </div>

    <script>
        // Show loading indicator when the page loads
        window.onload = function() {
            document.getElementById('loadingIndicator').style.display = 'block';
            // Hide loading indicator after 2 seconds (assuming the backup is complete)
            setTimeout(function() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }, 2000);
        };
    </script>
</body>
</html>
