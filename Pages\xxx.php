<?php
// Read the database connection details from the text file
$file = fopen("connection\\one.txt", "r");
$servername = trim(fgets($file));
$username = trim(fgets($file));
$password = trim(fgets($file));
$dbname = trim(fgets($file));
fclose($file);
?>







<!DOCTYPE html>
<html lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النسخ الاحتياطية</title>
    <link href='../assets/css/cairo-font.css' rel='stylesheet'>
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #1976D2;
            --success-color: #4CAF50;
            --danger-color: #f44336;
            --background-color: #f5f5f5;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--background-color);
            direction: rtl;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: var(--primary-color);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
        }

        .actions-container {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            width: 400px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .action-button {
            display: inline-block;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            color: white;
            background-color: var(--primary-color);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .action-button:hover {
            background-color: var(--secondary-color);
        }

        .action-button.backup {
            background-color: var(--success-color);
        }

        .action-button.backup:hover {
            background-color: #388E3C;
        }

        .action-button.restore {
            background-color: var(--primary-color);
        }

        .action-button.restore:hover {
            background-color: var(--secondary-color);
        }

        .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .card-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .card-description {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>إدارة النسخ الاحتياطية</h1>
            <p>قم بإنشاء واستعادة النسخ الاحتياطية لقاعدة البيانات بسهولة وأمان</p>
        </div>

        <div class="actions-container">
            <div class="action-card">
                <div class="icon">💾</div>
                <h2 class="card-title">إنشاء نسخة احتياطية</h2>
                <p class="card-description">قم بإنشاء نسخة احتياطية كاملة من قاعدة البيانات للحفاظ على بياناتك</p>
                <a href="backup.php" class="action-button backup">إنشاء نسخة احتياطية</a>
            </div>

            <div class="action-card">
                <div class="icon">🔄</div>
                <h2 class="card-title">استعادة نسخة احتياطية</h2>
                <p class="card-description">قم باستعادة قاعدة البيانات من نسخة احتياطية سابقة</p>
                <form action="restore.php" method="POST">
                    <button type="submit" class="action-button restore">استعادة نسخة احتياطية</button>
                </form>
            </div>
        </div>
    </div>
</body>

</html>