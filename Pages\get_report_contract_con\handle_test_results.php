<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Read database connection details
try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Handle file upload
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isset($_POST['employee_id']) || !isset($_FILES['test_results_document'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing required parameters']);
            exit;
        }

        $employee_id = $_POST['employee_id'];
        $file = $_FILES['test_results_document'];

        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            http_response_code(400);
            echo json_encode(['error' => 'File upload failed']);
            exit;
        }

        if ($file['type'] !== 'application/pdf') {
            http_response_code(400);
            echo json_encode(['error' => 'Only PDF files are allowed']);
            exit;
        }

        // Read file content
        $content = file_get_contents($file['tmp_name']);
        if ($content === false) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to read file content']);
            exit;
        }

        // Update database
        $stmt = $conn->prepare("UPDATE employees SET test_results_document = ? WHERE id_employees = ?");
        $stmt->bind_param("si", $content, $employee_id);

        if (!$stmt->execute()) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to save document']);
            exit;
        }

        echo json_encode(['success' => true]);
        exit;
    }

    // Handle file download
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        if (!isset($_GET['employee_id'])) {
            http_response_code(400);
            echo 'Missing employee ID';
            exit;
        }

        $employee_id = $_GET['employee_id'];

        // Fetch document from database
        $stmt = $conn->prepare("SELECT test_results_document FROM employees WHERE id_employees = ?");
        $stmt->bind_param("i", $employee_id);
        
        if (!$stmt->execute()) {
            http_response_code(500);
            echo 'Failed to fetch document';
            exit;
        }

        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        if (!$row || !$row['test_results_document']) {
            http_response_code(404);
            echo 'Document not found';
            exit;
        }

        // Output file
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="test_results.pdf"');
        echo $row['test_results_document'];
        exit;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
} 