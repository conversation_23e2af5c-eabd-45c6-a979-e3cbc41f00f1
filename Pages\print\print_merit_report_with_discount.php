<?php
// Start output buffering
ob_start();

// Include the TCPDF library
require_once('../../TCPDF-6.6.2/tcpdf.php');

// Get parameters from the request
$data = json_decode($_POST['data'], true);

if (!$data) {
    die('No data provided');
}

// Fetch institution data
$institutionData = null;
try {
    // Connect to the database
    $file = fopen(__DIR__ . "/../../Pages/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch institution data
    $stmt = $db->query("SELECT * FROM documents LIMIT 1");
    $institutionData = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die('Error fetching institution data: ' . $e->getMessage());
}

// Process institution data
if ($institutionData) {
    $agency_name_ar = explode("\n", trim($institutionData['name_ar']));
    $address_ar = explode("\n", trim($institutionData['address_ar']));
    $agency_name_en = explode("\n", trim($institutionData['name_en']));
    $address_en = explode("\n", trim($institutionData['address_en']));
    $numbers = explode("\n", trim($institutionData['numbers']));
    $logoData = $institutionData['logo'];
}

// Clean any output before generating PDF
ob_end_clean();

// Extend TCPDF class to customize header and footer
class MYPDF extends TCPDF {
    protected $institutionData;
    
    public function setInstitutionData($data) {
        $this->institutionData = $data;
    }

    public function Header() {
        if (!$this->institutionData) return;

        // Save current position
        $x = $this->GetX();
        $y = $this->GetY();

        // Calculate widths with equal margins
        $pageWidth = $this->getPageWidth();
        $margin = 2; // Equal margin on both sides
        $logoWidth = 35; // Increased from 25 to 35
        
        // New header layout - Logo at the top center
        if ($this->institutionData['logo']) {
            $this->Image('@'.$this->institutionData['logo'], 
                ($pageWidth - $logoWidth) / 2,
                $y + 2,
                $logoWidth, 
                $logoWidth,
                '', '', '', false, 300, '', false, false, 0);
        }
        
        // Add space after logo
        $y_after_logo = $y + $logoWidth + 5;
        
        // Draw purple lines with text - similar to offer.php style
        // Top purple lines (3 lines)
        $lineY = $y_after_logo;
        $lineHeight = 0.5;
        $lineGap = 1;
        $purpleColor = array(128, 0, 128); // Change back to purple RGB for lines only
        
        $this->SetLineWidth(0.2);
        $this->SetDrawColor($purpleColor[0], $purpleColor[1], $purpleColor[2]);
        
        // Draw top 3 purple lines
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Add text in the middle - only English text between lines
        $this->SetY($y_after_logo + 1.5);
        $this->SetFont('times', 'B', 10);
        $this->SetTextColor(0, 0, 0); // Keep text color black
        $this->Cell(0, 6, 'Assistance for Response and Development', 0, 1, 'L');
        $this->SetTextColor(0, 0, 0); // Reset text color to black for the rest of the document
        
        // Draw bottom 3 purple lines
        $lineY = $y_after_logo + 8;
        for ($i = 0; $i < 3; $i++) {
            $this->Line($margin, $lineY, $pageWidth - $margin, $lineY);
            $lineY += $lineGap;
        }
        
        // Set ending position
        $this->SetY($lineY + 5);
    }

    public function Footer() {
        $this->SetY(-25);
        $this->SetFont('aealarabiya', '', 9);
        $this->Cell(0, 10, 'توقيع الموظف                                  الموارد البشرية                                  رئيس المؤسسة', 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
}

// Create new PDF document
$pdf = new MYPDF('P', 'mm', 'A4', true, 'UTF-8', false);

// Helper function for formatting numbers in RTL context
function formatNumberForRTL($pdf, $number, $decimals = 2, $currencySymbol = '') {
    // Format the number
    $formattedNumber = number_format($number, $decimals, '.', ',');
    
    // Add the currency symbol if provided
    if (!empty($currencySymbol)) {
        $formattedNumber .= ' ' . $currencySymbol;
    }
    
    // Create the final string with no visible markers (using a special non-printing character)
    return chr(8206) . '  ' . $formattedNumber . '  ' . chr(8206);
}

// Set institution data
$pdf->setInstitutionData([
    'agency_name_ar' => $agency_name_ar,
    'address_ar' => $address_ar,
    'agency_name_en' => $agency_name_en,
    'address_en' => $address_en,
    'numbers' => $numbers,
    'logo' => $logoData
]);

// Set document information
$pdf->SetCreator('HR System');
$pdf->SetAuthor('HR System');
$pdf->SetTitle('Merit Report');

// Set margins
$pdf->SetMargins(2, 55, 2);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 25);

// Add a page
$pdf->AddPage();

// Set font for header information
$pdf->SetFont('aealarabiya', 'B', 10);

// Calculate page width and adjust column widths
$pageWidth = 210 - 4; // Total page width minus total margins (2mm + 2mm)
$labelWidth = 22;
$valueWidth = 98;
$smallValueWidth = 55;
$spacing = 2;

// Store starting Y position
$startY = $pdf->GetY();

// Project name row with full width (first row)
$leftStart = $pdf->GetMargins()['left'];
$pdf->SetX($leftStart);

// Project name label and field with full width
$projectLabelWidth = 30; // Width for 'اسم المشروع' label
$projectNameWidth = $pageWidth - $projectLabelWidth; // Remaining width for project name

// Store starting position for project name label
$startX = $pdf->GetX();
$startY = $pdf->GetY();

// Project name with MultiCell - allowing vertical expansion
$pdf->MultiCell($projectNameWidth, 6, $data['Project_name'], 1, 'R', false);

// Get ending Y position after project name content
$endY = $pdf->GetY();

// Add project label at the right height
$pdf->SetXY($startX + $projectNameWidth, $startY);
$pdf->Cell($projectLabelWidth, $endY - $startY, 'اسم المشروع', 1, 1, 'R', false);

// Add spacing after project name
$pdf->Ln(1);

// Calculate widths for the two sections
$leftSectionWidth = $pageWidth / 2 - $spacing / 2; // Left section (Employee info)
$rightSectionWidth = $pageWidth / 2 - $spacing / 2; // Right section (Project/Report info)

// Define label widths for both sections
$leftLabelWidth = 30; // Width for employee section labels
$rightLabelWidth = 45; // Width for project/report section labels (needs to be wider for longer labels)

// Calculate value field widths
$leftValueWidth = $leftSectionWidth - $leftLabelWidth;
$rightValueWidth = $rightSectionWidth - $rightLabelWidth;

// ------ SECTION 1: EMPLOYEE INFORMATION (LEFT SIDE) ------
// Starting X position for left section
$leftSectionX = $leftStart;

// Use a slightly larger font for employee information
$pdf->SetFont('aealarabiya', 'B', 11); // Increased font size for employee fields

// Employee Name row with increased height
$pdf->SetX($leftSectionX);
$pdf->Cell($leftValueWidth, 8, $data['name_ar_contract'], 1, 0, 'R', false); // Increased height from 6 to 8
$pdf->Cell($leftLabelWidth, 8, 'اسم الموظف', 1, 1, 'R', false); // Increased height from 6 to 8

// Job Title row (directly beneath Employee Name) with increased height
$pdf->SetX($leftSectionX);
$pdf->Cell($leftValueWidth, 8, $data['name_Job'], 1, 0, 'R', false); // Increased height from 6 to 8
$pdf->Cell($leftLabelWidth, 8, 'المسمى الوظيفي', 1, 1, 'R', false); // Increased height from 6 to 8

// Reset font size for the rest of the document
$pdf->SetFont('aealarabiya', 'B', 10);

// ------ SECTION 2: PROJECT/REPORT INFORMATION (RIGHT SIDE) ------
// Starting X position for right section
$rightSectionX = $leftStart + $leftSectionWidth + $spacing;

// Store current Y position after employee name row
$currentY = $pdf->GetY() - 16; // Adjusted to account for increased row height (8+8 instead of 6+6)
$pdf->SetY($currentY);

// Project ID row
$pdf->SetX($rightSectionX);
$pdf->Cell($rightValueWidth, 6, $data['id_Project'], 1, 0, 'R', false);
$pdf->Cell($rightLabelWidth, 6, 'رقم المشروع', 1, 1, 'R', false);

// Merit report number row
$pdf->SetX($rightSectionX);
$pdf->Cell($rightValueWidth, 6, $data['id_merit_reports'], 1, 0, 'R', false);
$pdf->Cell($rightLabelWidth, 6, 'رقم تقرير الاستحقاق المالي', 1, 1, 'R', false);

// Achievement report number row
$pdf->SetX($rightSectionX);
$pdf->Cell($rightValueWidth, 6, $data['id_achievement_reports'], 1, 0, 'R', false);
$pdf->Cell($rightLabelWidth, 6, 'رقم تقرير الإنجاز المرتبط', 1, 1, 'R', false);

// Reset Y position to the maximum Y value of both sections
$maxY = max($pdf->GetY(), $currentY + 18); // 3 rows on right side * 6 height
$pdf->SetY($maxY);

// Add spacing after the sections
$pdf->Ln(5);

// Add title with date range
$startDate = date('Y/m/d', strtotime($data['start_date_achievement_reports']));
$endDate = date('Y/m/d', strtotime($data['end_date_achievement_reports']));

// Add period details in a styled box
$pdf->SetFillColor(245, 245, 245);
$pdf->Cell(0, 8, "الفترة: $startDate - $endDate", 1, 1, 'C', true);

$pdf->Ln(5);

// Add Merit Report Summary with enhanced styling
$pdf->SetFont('aealarabiya', 'B', 14);
// Set text color to black
$pdf->SetTextColor(0, 0, 0);
$pdf->Cell(0, 10, "ملخص تقرير الاستحقاق المالي", 0, 1, 'C');
// Reset text color
$pdf->SetTextColor(0, 0, 0);
$pdf->Ln(2);

// Create summary table with enhanced financial details
$pdf->SetFont('aealarabiya', 'B', 11);

// Define colors for different sections
$headerFillColor = array(240, 240, 240); // Light gray for headers
$workInfoColor = array(248, 248, 248);   // Very light gray for work info
$financialColor = array(245, 245, 245);  // Light gray for financial info
$deductionsColor = array(240, 240, 240); // Light gray for deductions
$borderColor = array(0, 0, 0);           // Black border

// Set border color to black
$pdf->SetDrawColor($borderColor[0], $borderColor[1], $borderColor[2]);
$pdf->SetLineWidth(0.3); // Slightly thicker borders

// Get currency type
$currencyType = isset($data['currency_type_name']) ? $data['currency_type_name'] : '';

// First row - Main header
$pdf->SetFillColor(80, 80, 80); // Dark gray background
$pdf->SetTextColor(255, 255, 255); // White text
$pdf->Cell(0, 8, "بيانات الاستحقاق المالي", 1, 1, 'C', true);
$pdf->SetTextColor(0, 0, 0); // Reset text color to black

// Work and Salary Information Section
// Header
$pdf->SetFillColor($headerFillColor[0], $headerFillColor[1], $headerFillColor[2]);
$pdf->Cell(0, 8, 'معلومات العمل والأجر', 1, 1, 'C', true);

// Content rows
$pdf->SetFillColor($workInfoColor[0], $workInfoColor[1], $workInfoColor[2]);
$colWidth1 = ($pageWidth) / 3;

// Headers for work details
$pdf->Cell($colWidth1, 7, 'أيام العمل', 1, 0, 'C', true);
$pdf->Cell($colWidth1, 7, 'الأجر اليومي', 1, 0, 'C', true);
$pdf->Cell($colWidth1, 7, 'نوع العملة', 1, 1, 'C', true);

// Data for work details
$pdf->SetFont('aealarabiya', '', 10);
$pdf->Cell($colWidth1, 7, $data['actual_working_days'], 1, 0, 'C', false);
$pdf->Cell($colWidth1, 7, formatNumberForRTL($pdf, $data['today_wage']), 1, 0, 'C', false);
$pdf->Cell($colWidth1, 7, $currencyType, 1, 1, 'C', false);

// Add a small space between sections
$pdf->Ln(2);

// Financial Entitlement Information Section
// Header
$pdf->SetFont('aealarabiya', 'B', 11);
$pdf->SetFillColor($headerFillColor[0], $headerFillColor[1], $headerFillColor[2]);
$pdf->Cell(0, 8, 'معلومات الاستحقاق المالي', 1, 1, 'C', true);

// Content rows
$pdf->SetFillColor($financialColor[0], $financialColor[1], $financialColor[2]);
$colWidth2 = ($pageWidth) / 2;

// Headers for financial details
$pdf->Cell($colWidth2, 7, 'إجمالي الاستحقاق قبل الخصومات', 1, 0, 'C', true);
$pdf->Cell($colWidth2, 7, 'صافي الاستحقاق بعد الخصومات', 1, 1, 'C', true);

// Data for financial details
$pdf->SetFont('aealarabiya', 'B', 11); // Make this bold for emphasis
// Set color for the total amount (black)
$pdf->SetTextColor(0, 0, 0);
$pdf->Cell($colWidth2, 8, formatNumberForRTL($pdf, $data['total'], 2, $currencyType), 1, 0, 'C', false);
// Set color for the net amount (black)
$pdf->SetTextColor(0, 0, 0);
$pdf->Cell($colWidth2, 8, formatNumberForRTL($pdf, $data['total_after_discount'], 2, $currencyType), 1, 1, 'C', false);
// Reset text color
$pdf->SetTextColor(0, 0, 0);

// Add a small space between sections
$pdf->Ln(2);

// Reset border color and line width to default
$pdf->SetDrawColor(0, 0, 0);
$pdf->SetLineWidth(0.1);

// Add more space before the tasks section
$pdf->Ln(5);

// Add Tasks Section if available
if (isset($data['data_todo_list_merit']) && isset($data['data_todo_list_merit']['tasks'])) {
    $pdf->SetFont('aealarabiya', 'B', 12);
    $pdf->Cell(0, 8, "تفاصيل المهام المنجزة", 0, 1, 'C');
    $pdf->Ln(5);

    // Tasks table headers
    $pdf->SetFont('aealarabiya', 'B', 10);
    $pdf->SetFillColor(220, 220, 220);
    
    // Column widths
    $taskWidth = 140;
    $completionWidth = 64;
    
    // Headers
    $pdf->Cell($taskWidth, 7, 'المهمة', 1, 0, 'C', true);
    $pdf->Cell($completionWidth, 7, 'نسبة الإنجاز', 1, 1, 'C', true);

    // Tasks data
    $pdf->SetFont('aealarabiya', '', 10);
    $fill = false;
    
    foreach ($data['data_todo_list_merit']['tasks'] as $task) {
        // Remove dash prefix from task name if it exists
        $cleanTaskName = preg_replace('/^[\s\-–—]+/', '', $task['taskName']);
        
        // Check if text is long - if so, use a two-step approach with startTransaction
        $longText = (strlen($cleanTaskName) > 40);
        
        if ($longText) {
            // Start a transaction to measure cell height
            $pdf->startTransaction();
            $start_y = $pdf->GetY();
            $start_page = $pdf->getPage();
            
            // Write task name to calculate height
            $pdf->MultiCell($taskWidth, 0, $cleanTaskName, 1, 'R', $fill, 1);
            
            // Get end position
            $end_y = $pdf->GetY();
            $end_page = $pdf->getPage();
            
            // Calculate height
            if ($end_page == $start_page) {
                $height = $end_y - $start_y;
            } else {
                $height = 15; // Default for multi-page cell
            }
            
            // Rollback to start position
            $pdf->rollbackTransaction(true);
            
            // Set minimum height
            $height = max($height, 7);
            
            // Draw cells with calculated height
            $startX = $pdf->GetX();
            $startY = $pdf->GetY();
            
            // Task name
            $pdf->SetXY($startX, $startY);
            $pdf->MultiCell($taskWidth, $height, $cleanTaskName, 1, 'R', $fill, 0);
            
            // Completion percentage
            $pdf->SetXY($startX + $taskWidth, $startY);
            $pdf->MultiCell($completionWidth, $height, $task['completion'] . '%', 1, 'C', $fill, 1);
        } else {
            // For short text, use a standard height
            $height = 7;
            
            // Start position
            $startX = $pdf->GetX();
            $startY = $pdf->GetY();
            
            // Task name
            $pdf->SetXY($startX, $startY);
            $pdf->MultiCell($taskWidth, $height, $cleanTaskName, 1, 'R', $fill, 0);
            
            // Completion percentage
            $pdf->SetXY($startX + $taskWidth, $startY);
            $pdf->MultiCell($completionWidth, $height, $task['completion'] . '%', 1, 'C', $fill, 1);
        }
        
        $fill = !$fill;
    }
}

// Output the PDF
$pdf->Output('merit_report.pdf', 'I'); 