<?php
// Turn off error output to prevent HTML errors instead of proper document download
error_reporting(0);
ini_set('display_errors', 0);
session_start();

// Read database connection details
try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    // Check if all required parameters are provided
    if (!isset($_GET['delegate_id']) || !isset($_GET['document_type'])) {
        throw new Exception("Missing required parameters");
    }

    $delegate_id = (int)$_GET['delegate_id'];
    $document_type = $_GET['document_type'];
    $action = isset($_GET['action']) ? $_GET['action'] : 'download';

    // Validate document type
    $allowed_document_types = [
        'Identity_document',
        'code_conduct'
    ];

    if (!in_array($document_type, $allowed_document_types)) {
        throw new Exception("Invalid document type");
    }

    // Get the document binary data
    $sql = "SELECT {$document_type} FROM assigned WHERE id_assigned = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Query preparation failed: " . $conn->error);
    }
    
    $stmt->bind_param("i", $delegate_id);
    
    if (!$stmt->execute()) {
        throw new Exception("Query execution failed: " . $stmt->error);
    }
    
    $stmt->bind_result($document_data);
    
    if (!$stmt->fetch()) {
        throw new Exception("Document not found for delegate ID: " . $delegate_id);
    }
    
    $stmt->close();
    
    // Check if document exists
    if (!$document_data) {
        throw new Exception("Document not available for this delegate");
    }
    
    // Stream the document
    $document_name = "document_{$delegate_id}_{$document_type}.pdf";
    
    if ($action === 'view') {
        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $document_name . '"');
    } else {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $document_name . '"');
    }
    
    header('Content-Length: ' . strlen($document_data));
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    echo $document_data;
    exit;

} catch (Exception $e) {
    // Handle error
    if (isset($_GET['action']) && $_GET['action'] === 'view') {
        header('Content-Type: text/html; charset=utf-8');
        echo '<div style="text-align: center; font-family: Arial, sans-serif; margin-top: 50px;">';
        echo '<h2 style="color: #e74c3c;">خطأ</h2>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p>الرجاء المحاولة مرة أخرى لاحقاً أو الاتصال بمسؤول النظام.</p>';
        echo '</div>';
    } else {
        header('Content-Type: text/plain; charset=utf-8');
        echo 'Error: ' . $e->getMessage();
    }
} catch (Error $e) {
    // Catch PHP 7+ errors
    if (isset($_GET['action']) && $_GET['action'] === 'view') {
        header('Content-Type: text/html; charset=utf-8');
        echo '<div style="text-align: center; font-family: Arial, sans-serif; margin-top: 50px;">';
        echo '<h2 style="color: #e74c3c;">خطأ نظام</h2>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p>الرجاء المحاولة مرة أخرى لاحقاً أو الاتصال بمسؤول النظام.</p>';
        echo '</div>';
    } else {
        header('Content-Type: text/plain; charset=utf-8');
        echo 'System Error: ' . $e->getMessage();
    }
} finally {
    if (isset($conn)) {
        $conn->close();
    }
} 