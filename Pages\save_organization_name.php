<?php
// Start session and set headers
session_start();
header('Content-Type: application/json; charset=UTF-8');

// Default response
$response = [
    'success' => false,
    'message' => 'An error occurred'
];

try {
    // Connect to the database
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }
    $servername = trim(fgets($file));
    $username   = trim(fgets($file));
    $password   = trim(fgets($file));
    $dbname     = trim(fgets($file));
    fclose($file);
    
    $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Set the organization name in English
    $orgNameEn = "Assistance for Response and Development";
    
    // Update the documents table
    $stmt = $db->prepare("UPDATE documents SET name_en = :name_en");
    $stmt->execute([':name_en' => $orgNameEn]);
    
    // Check if update was successful
    if ($stmt->rowCount() > 0) {
        $response = [
            'success' => true,
            'message' => 'Organization name updated successfully'
        ];
    } else {
        $response = [
            'success' => true,
            'message' => 'No changes needed, organization name already set'
        ];
    }
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ];
}

// Return JSON response
echo json_encode($response);
?> 