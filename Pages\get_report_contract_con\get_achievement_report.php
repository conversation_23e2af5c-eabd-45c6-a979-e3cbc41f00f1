<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    // Read database connection details
    $file = fopen(dirname(__DIR__) . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading configuration file');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    if (!isset($_GET['id'])) {
        throw new Exception("Report ID is required");
    }

    $id = (int)$_GET['id'];

    $sql = "SELECT ar.*, 
                   pd.start_date_permanent_diapers,
                   pd.end_date_permanent_diapers,
                   c.name_Job,
                   c.contract_type,
                   e.name_ar_contract,
                   p.Project_name,
                   mr.today_wage,
                   mr.total
            FROM achievement_reports ar
            JOIN permanent_diapers pd ON ar.id_permanent_diapers = pd.id_permanent_diapers
            JOIN contract c ON ar.id_contract = c.id_contract
            JOIN employees e ON c.id_employees = e.id_employees
            JOIN Project p ON ar.id_Project = p.id_Project
            LEFT JOIN merit_reports mr ON ar.id_achievement_reports = mr.id_achievement_reports
            WHERE ar.id_achievement_reports = ?";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception("Report not found");
    }

    $report = $result->fetch_assoc();
    
    // Decode the JSON data for tasks
    $report['data_todo_list_achievement'] = json_decode($report['data_todo_list_achievement'], true);

    echo json_encode([
        'success' => true,
        'data' => $report
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} 